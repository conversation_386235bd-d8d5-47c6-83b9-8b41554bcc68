# 鉴权机制

## 1. 概述

本文档定义了通用VOC报表系统的鉴权机制，包括身份认证、权限管理和访问控制等方面的内容。

## 2. 认证机制

### 2.1 JWT Token认证
系统采用JWT（JSON Web Token）进行身份认证，Token包含用户身份信息和权限信息。

#### Token结构
```
Header.Payload.Signature
```

#### Header部分
```json
{
  "alg": "HS256",
  "typ": "JWT"
}
```

#### Payload部分
```json
{
  "userId": "用户ID",
  "username": "用户名",
  "roles": ["角色列表"],
  "permissions": ["权限列表"],
  "exp": "过期时间",
  "iat": "签发时间"
}
```

#### 使用方式
```
Authorization: Bearer {jwt_token}
```

### 2.2 API密钥认证
对于系统间调用，支持API密钥认证方式。

#### 请求头
```
X-API-Key: {api_key}
X-API-Secret: {api_secret}
```

### 2.3 OAuth 2.0认证
支持OAuth 2.0认证，适用于第三方应用接入。

#### 使用方式
```
Authorization: Bearer {oauth_token}
```

## 3. 权限模型

### 3.1 RBAC权限模型
系统采用基于角色的访问控制（RBAC）模型，包含以下核心概念：

#### 用户（User）
系统中的操作者，具有唯一标识和基本信息。

#### 角色（Role）
一组权限的集合，可以分配给用户。

#### 权限（Permission）
系统中最小的不可分割的访问控制单元。

#### 关系
- 一个用户可以拥有多个角色
- 一个角色可以分配给多个用户
- 一个角色可以拥有多个权限
- 一个权限可以分配给多个角色

### 3.2 权限分类
#### 功能权限
- 数据接入权限
- 数据分析权限
- 报表查看权限
- 系统配置权限
- 用户管理权限

#### 数据权限
- 行业数据权限
- 组织数据权限
- 客户数据权限

## 4. 访问控制

### 4.1 接口级访问控制
每个API接口都有明确的权限要求，在请求处理前进行权限验证。

### 4.2 数据级访问控制
根据用户权限控制数据访问范围，确保用户只能访问授权的数据。

### 4.3 操作级访问控制
对敏感操作进行额外的权限验证和操作日志记录。

## 5. 安全措施

### 5.1 Token安全
- Token设置合理的过期时间
- 支持Token刷新机制
- 提供Token撤销功能
- 敏感操作需要重新认证

### 5.2 传输安全
- 所有认证信息通过HTTPS传输
- 防止Token在URL中传递
- 敏感信息加密存储

### 5.3 防攻击措施
- 实现请求频率限制
- 防止暴力破解攻击
- 记录异常访问日志
- 支持IP黑白名单

## 6. 会话管理

### 6.1 会话超时
- Web会话默认超时时间：30分钟
- 可配置的会话超时时间
- 支持"记住我"功能

### 6.2 会话安全
- 会话固定攻击防护
- 安全的会话存储
- 会话状态监控

## 7. 审计日志

### 7.1 登录日志
记录用户登录相关信息：
- 登录时间
- 登录IP
- 登录结果
- 登录方式

### 7.2 操作日志
记录用户关键操作：
- 操作时间
- 操作用户
- 操作类型
- 操作对象
- 操作结果

## 8. 第三方集成

### 8.1 单点登录（SSO）
支持与企业现有的SSO系统集成。

### 8.2 LDAP集成
支持LDAP用户认证和同步。

### 8.3 第三方认证
支持微信、钉钉等第三方认证方式。

## 9. API安全

### 9.1 接口签名
重要接口支持请求签名验证，防止请求被篡改。

### 9.2 参数加密
敏感参数支持加密传输。

### 9.3 访问频率限制
- 接口调用频率限制
- 用户请求频率限制
- IP请求频率限制

## 10. 应急处理

### 10.1 安全事件响应
- 安全漏洞快速响应机制
- 安全事件处理流程
- 安全补丁及时更新

### 10.2 权限回收
- 离职用户权限及时回收
- 异常账号权限冻结
- 权限变更审批流程