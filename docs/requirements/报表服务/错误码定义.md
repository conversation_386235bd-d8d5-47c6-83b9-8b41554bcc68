# 错误码定义

## 1. 概述

本文档定义了通用VOC报表系统中使用的统一错误码规范，便于问题定位和系统维护。

## 2. 错误码结构

错误码采用5位数字格式：`XYYYZ`
- X: 服务类型（1-9）
- YYY: 具体错误类别（000-999）
- Z: 错误级别（0-9）

### 2.1 服务类型定义
| 类型码 | 服务类型 | 说明 |
|--------|----------|------|
| 1 | 用户服务 | 用户认证、权限相关 |
| 2 | 配置服务 | 系统配置、行业配置相关 |
| 3 | 数据接入服务 | 数据上传、数据源管理相关 |
| 4 | 数据分析服务 | AI分析、模型计算相关 |
| 5 | 报表服务 | 报表生成、数据查询相关 |
| 6 | 文件服务 | 文件上传、下载相关 |
| 7 | 通知服务 | 消息通知、邮件发送相关 |
| 8 | 系统服务 | 系统监控、健康检查相关 |
| 9 | 通用服务 | 通用错误、未分类错误 |

### 2.2 错误级别定义
| 级别码 | 说明 |
|--------|------|
| 0 | 调试级别 |
| 1 | 提示级别 |
| 2 | 警告级别 |
| 3 | 一般错误 |
| 4 | 严重错误 |
| 5 | 致命错误 |

## 3. 通用错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 90001 | SUCCESS | 操作成功 |
| 90002 | UNKNOWN_ERROR | 未知错误 |
| 90003 | SYSTEM_ERROR | 系统内部错误 |
| 90004 | SERVICE_UNAVAILABLE | 服务暂时不可用 |
| 90005 | SERVICE_TIMEOUT | 服务调用超时 |
| 90006 | PARAMETER_ERROR | 参数错误 |
| 90007 | PARAMETER_MISSING | 缺少必要参数 |
| 90008 | PARAMETER_FORMAT_ERROR | 参数格式错误 |
| 90009 | DATA_NOT_FOUND | 数据不存在 |
| 90010 | DATA_ALREADY_EXISTS | 数据已存在 |
| 90011 | DATA_VALIDATION_FAILED | 数据验证失败 |
| 90012 | UNSUPPORTED_OPERATION | 不支持的操作 |
| 90013 | OPERATION_DENIED | 操作被拒绝 |
| 90014 | OPERATION_TIMEOUT | 操作超时 |
| 90015 | RESOURCE_LIMIT_EXCEEDED | 资源限制超出 |

## 4. 认证授权错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 10101 | AUTHENTICATION_FAILED | 认证失败 |
| 10102 | AUTHORIZATION_FAILED | 授权失败 |
| 10103 | TOKEN_EXPIRED | Token已过期 |
| 10104 | TOKEN_INVALID | Token无效 |
| 10105 | TOKEN_MISSING | 缺少Token |
| 10106 | ACCESS_DENIED | 访问被拒绝 |
| 10107 | PERMISSION_DENIED | 权限不足 |
| 10108 | ACCOUNT_DISABLED | 账户已被禁用 |
| 10109 | ACCOUNT_LOCKED | 账户已被锁定 |
| 10110 | ACCOUNT_EXPIRED | 账户已过期 |

## 5. 配置管理错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 20101 | INDUSTRY_NOT_FOUND | 行业配置不存在 |
| 20102 | INDUSTRY_ALREADY_EXISTS | 行业配置已存在 |
| 20103 | INDUSTRY_CONFIG_INVALID | 行业配置无效 |
| 20104 | DICTIONARY_NOT_FOUND | 字典配置不存在 |
| 20105 | RULE_NOT_FOUND | 规则配置不存在 |
| 20106 | TEMPLATE_NOT_FOUND | 模板配置不存在 |

## 6. 数据接入错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 30101 | DATA_SOURCE_NOT_FOUND | 数据源不存在 |
| 30102 | DATA_SOURCE_ALREADY_EXISTS | 数据源已存在 |
| 30103 | DATA_SOURCE_CONFIG_INVALID | 数据源配置无效 |
| 30104 | DATA_UPLOAD_FAILED | 数据上传失败 |
| 30105 | DATA_FORMAT_INVALID | 数据格式无效 |
| 30106 | DATA_SIZE_EXCEEDED | 数据大小超出限制 |
| 30107 | BATCH_PROCESSING_FAILED | 批量处理失败 |
| 30108 | DATA_VALIDATION_ERROR | 数据验证错误 |

## 7. 数据分析错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 40101 | MODEL_NOT_FOUND | 分析模型不存在 |
| 40102 | MODEL_NOT_READY | 分析模型未就绪 |
| 40103 | MODEL_EXECUTION_FAILED | 模型执行失败 |
| 40104 | SENTIMENT_ANALYSIS_FAILED | 情感分析失败 |
| 40105 | INTENTION_RECOGNITION_FAILED | 意图识别失败 |
| 40106 | TOPIC_CLASSIFICATION_FAILED | 主题分类失败 |
| 40107 | CONTENT_TOO_SHORT | 内容过短无法分析 |
| 40108 | CONTENT_LANGUAGE_UNSUPPORTED | 不支持的内容语言 |

## 8. 报表服务错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 50101 | REPORT_NOT_FOUND | 报表不存在 |
| 50102 | REPORT_GENERATION_FAILED | 报表生成失败 |
| 50103 | QUERY_EXECUTION_FAILED | 查询执行失败 |
| 50104 | CHART_RENDERING_FAILED | 图表渲染失败 |
| 50105 | EXPORT_FAILED | 导出失败 |
| 50106 | UNSUPPORTED_EXPORT_FORMAT | 不支持的导出格式 |
| 50107 | VISUALIZATION_CONFIG_INVALID | 可视化配置无效 |

## 9. 文件服务错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 60101 | FILE_NOT_FOUND | 文件不存在 |
| 60102 | FILE_UPLOAD_FAILED | 文件上传失败 |
| 60103 | FILE_DOWNLOAD_FAILED | 文件下载失败 |
| 60104 | FILE_SIZE_EXCEEDED | 文件大小超出限制 |
| 60105 | FILE_TYPE_UNSUPPORTED | 不支持的文件类型 |
| 60106 | FILE_STORAGE_FAILED | 文件存储失败 |

## 10. 通知服务错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 70101 | NOTIFICATION_SEND_FAILED | 通知发送失败 |
| 70102 | EMAIL_SEND_FAILED | 邮件发送失败 |
| 70103 | SMS_SEND_FAILED | 短信发送失败 |
| 70104 | TEMPLATE_NOT_FOUND | 通知模板不存在 |

## 11. 系统服务错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 80101 | SYSTEM_BUSY | 系统繁忙 |
| 80102 | SYSTEM_MAINTENANCE | 系统维护中 |
| 80103 | HEALTH_CHECK_FAILED | 健康检查失败 |
| 80104 | DEPENDENCY_SERVICE_ERROR | 依赖服务错误 |
| 80105 | DATABASE_CONNECTION_FAILED | 数据库连接失败 |
| 80106 | CACHE_CONNECTION_FAILED | 缓存连接失败 |

## 12. 使用规范

### 12.1 错误码返回格式
```json
{
  "code": 40001,
  "message": "参数错误",
  "error": {
    "type": "PARAMETER_ERROR",
    "details": [
      {
        "field": "industryCode",
        "message": "行业代码不能为空"
      }
    ]
  },
  "timestamp": "2024-01-01T12:00:00Z",
  "requestId": "req-123456789"
}
```

### 12.2 错误信息国际化
系统支持错误信息的国际化显示，根据客户端语言环境返回对应语言的错误信息。

### 12.3 错误日志记录
所有错误都应记录详细日志，包括：
- 错误码
- 错误信息
- 请求参数
- 用户信息
- 时间戳
- 请求ID