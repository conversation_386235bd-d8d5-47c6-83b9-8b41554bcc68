# VOC数据库数据字典

## 1. 概述

### 1.1 文档目的
本文档提供了VOC数据库中所有表、字段、代码值的详细说明，为数据库开发、维护和使用提供标准化的参考。

### 1.2 文档范围
- 核心事实表字段说明
- 维度表字段说明
- **视图层字段说明**
- 代码值定义
- 字段约束说明
- 业务规则说明

## 2. 核心事实表

### 2.1 dws_voc2_sounds_data (声音数据汇总表)

#### 2.1.1 基础信息字段

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| id | varchar | 255 | YES | YES | - | 声音ID，主键 |
| data_id | varchar | 255 | YES | NO | - | 数据唯一标识 |
| channel_code | varchar | 50 | YES | NO | - | 渠道编码 |
| channel | varchar | 100 | YES | NO | - | 渠道名称 |
| brand_code | varchar | 50 | YES | NO | - | 品牌编码 |
| brand | varchar | 100 | YES | NO | - | 品牌名称 |
| vehicle_series_code | varchar | 50 | YES | NO | - | 车系编码 |
| vehicle_series | varchar | 100 | YES | NO | - | 车系名称 |
| vehicle_model_code | varchar | 50 | YES | NO | - | 车型编码 |
| vehicle_model | varchar | 100 | YES | NO | - | 车型名称 |
| label_type | varchar | 100 | YES | NO | - | 数据类型 |

#### 2.1.2 情感分析字段

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| sentiment | varchar | 50 | YES | NO | - | 情感倾向 |
| intention | varchar | 100 | YES | NO | - | 意图分类 |
| hot_word | TEXT | - | YES | NO | - | 热词提取 |
| user_journey | TEXT | - | YES | NO | - | 用户旅程 |
| keywords | TEXT | - | YES | NO | - | 关键词 |
| topic | TEXT | - | YES | NO | - | 观点主题 |

#### 2.1.3 时间维度字段

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| data_create_time | datetime | - | YES | NO | - | 数据产生时间 |
| create_time | datetime | - | YES | NO | - | 数据抓取时间 |
| data_create_week | varchar | 20 | YES | NO | - | 数据产生周期-周 |
| data_create_month | varchar | 20 | YES | NO | - | 数据产生周期-月 |
| data_create_quarter | varchar | 20 | YES | NO | - | 数据产生周期-季 |
| data_create_year | varchar | 20 | YES | NO | - | 数据产生周期-年 |

#### 2.1.4 客户维度字段

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| one_id | varchar | 100 | YES | NO | - | oneId |
| cust_name | varchar | 100 | YES | NO | - | 客户姓名 |
| cust_mobile | varchar | 20 | YES | NO | - | 客户手机号 |
| cust_age | int | 11 | YES | NO | - | 客户年龄 |
| cust_gender | varchar | 10 | YES | NO | - | 客户性别 |
| cust_province_code | varchar | 20 | YES | NO | - | 客户常驻省份编码 |
| cust_province | varchar | 100 | YES | NO | - | 客户常驻省份 |
| cust_city_code | varchar | 20 | YES | NO | - | 客户常驻市编码 |
| cust_city | varchar | 100 | YES | NO | - | 客户常驻市 |
| cust_district_code | varchar | 20 | YES | NO | - | 客户常驻区编码 |
| cust_district | varchar | 100 | YES | NO | - | 客户常驻区 |
| cust_highest_edu | varchar | 100 | YES | NO | - | 客户最高学历 |
| cust_monthly_income | varchar | 100 | YES | NO | - | 客户月收入 |
| cust_last_purchase_date | datetime | - | YES | NO | - | 客户最近一次购车时间 |
| cust_type | varchar | 100 | YES | NO | - | 客户类型 |
| is_vehicle_owner | boolean | - | YES | NO | - | 是否车主 |

#### 2.1.5 经销商维度字段

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| dealer_id | varchar | 100 | YES | NO | - | 经销商ID |
| dealer_code | varchar | 50 | YES | NO | - | 经销商编码 |
| dealer_name | varchar | 255 | YES | NO | - | 经销商全称 |
| dealer_province_code | varchar | 20 | YES | NO | - | 经销商所属省份编码 |
| dealer_province | varchar | 100 | YES | NO | - | 经销商所属省份 |
| dealer_city_code | varchar | 20 | YES | NO | - | 经销商所在市编码 |
| dealer_city | varchar | 100 | YES | NO | - | 经销商所在市 |
| dealer_regional_code | varchar | 50 | YES | NO | - | 经销商所在大区编码 |
| dealer_regional | varchar | 100 | YES | NO | - | 经销商所在大区 |

#### 2.1.6 车辆维度字段

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| vehicle_purchase_date | datetime | - | YES | NO | - | 车辆购买日期 |
| vehicle_production_date | datetime | - | YES | NO | - | 车辆生产日期 |
| vehicle_factory_release_date | datetime | - | YES | NO | - | 车辆出厂日期 |
| vehicle_vin | varchar | 100 | YES | NO | - | 车辆车架号 |

#### 2.1.7 内容类型字段

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| content_type | varchar | 100 | YES | NO | - | 原文内容类型 |
| work_order_id | varchar | 100 | YES | NO | - | 工单ID |
| is_main_post | boolean | - | YES | NO | - | 是否主贴 |
| post_title | TEXT | - | YES | NO | - | 帖子标题 |
| post_original_link | TEXT | - | YES | NO | - | 帖子原文链接 |
| post_original_content | TEXT | - | YES | NO | - | 帖子原文详情 |
| quest_type | varchar | 100 | YES | NO | - | 问卷类型 |
| quest_question_content | TEXT | - | YES | NO | - | 问卷题目/内容 |
| quest_answer_score | int | 11 | YES | NO | - | 问卷答案分数 |
| quest_business_type | varchar | 100 | YES | NO | - | 问卷业务类型 |
| quest_business_scenario | varchar | 100 | YES | NO | - | 问卷业务场景 |

#### 2.1.8 多维度标签字段

**VTR标签字段**:

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| vtr_tag_first_code | varchar | 50 | YES | NO | - | VRT标签编码1级 |
| vtr_tag_second_code | varchar | 50 | YES | NO | - | VRT标签编码2级 |
| vtr_tag_three_code | varchar | 50 | YES | NO | - | VRT标签编码3级 |
| vtr_tag_first | varchar | 100 | YES | NO | - | VRT标签1级 |
| vtr_tag_second | varchar | 100 | YES | NO | - | VRT标签2级 |
| vtr_tag_three | varchar | 100 | YES | NO | - | VRT标签3级 |

**COM标签字段**:

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| com_tag_first_code | varchar | 50 | YES | NO | - | 商品化属性标签编码1级 |
| com_tag_second_code | varchar | 50 | YES | NO | - | 商品化属性标签编码2级 |
| com_tag_three_code | varchar | 50 | YES | NO | - | 商品化属性标签编码3级 |
| com_tag_first | varchar | 100 | YES | NO | - | 商品化属性标签1级 |
| com_tag_second | varchar | 100 | YES | NO | - | 商品化属性标签2级 |
| com_tag_three | varchar | 100 | YES | NO | - | 商品化属性标签3级 |

**ADB标签字段**:

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| adb_tag_first_code | varchar | 50 | YES | NO | - | 全领域业务标签编码1级 |
| adb_tag_second_code | varchar | 50 | YES | NO | - | 全领域业务标签编码2级 |
| adb_tag_three_code | varchar | 50 | YES | NO | - | 全领域业务标签编码3级 |
| adb_tag_first | varchar | 100 | YES | NO | - | 全领域业务标签1级 |
| adb_tag_second | varchar | 100 | YES | NO | - | 全领域业务标签2级 |
| adb_tag_three | varchar | 100 | YES | NO | - | 全领域业务标签3级 |

**WOM标签字段**:

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| wom_tag_first_code | varchar | 50 | YES | NO | - | 口碑评价指标编码1级 |
| wom_tag_second_code | varchar | 50 | YES | NO | - | 口碑评价指标编码2级 |
| wom_tag_three_code | varchar | 50 | YES | NO | - | 口碑评价指标编码3级 |
| wom_tag_first | varchar | 100 | YES | NO | - | 口碑评价指标1级 |
| wom_tag_second | varchar | 100 | YES | NO | - | 口碑评价指标2级 |
| wom_tag_three | varchar | 100 | YES | NO | - | 口碑评价指标3级 |

**CX标签字段**:

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| cx_tag_first_code | varchar | 50 | YES | NO | - | 客户体验指标编码1级 |
| cx_tag_second_code | varchar | 50 | YES | NO | - | 客户体验指标编码2级 |
| cx_tag_three_code | varchar | 50 | YES | NO | - | 客户体验指标编码3级 |
| cx_tag_first | varchar | 100 | YES | NO | - | 客户体验指标1级 |
| cx_tag_second | varchar | 100 | YES | NO | - | 客户体验指标2级 |
| cx_tag_three | varchar | 100 | YES | NO | - | 客户体验指标3级 |

**CJ标签字段**:

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| cj_tag_first_code | varchar | 50 | YES | NO | - | 全旅程客户签编码1级 |
| cj_tag_second_code | varchar | 50 | YES | NO | - | 全旅程客户签编码2级 |
| cj_tag_three_code | varchar | 50 | YES | NO | - | 全旅程客户签编码3级 |
| cj_tag_first | varchar | 100 | YES | NO | - | 全旅程客户签1级 |
| cj_tag_second | varchar | 100 | YES | NO | - | 全旅程客户签2级 |
| cj_tag_three | varchar | 100 | YES | NO | - | 全旅程客户签3级 |

**SL标签字段**:

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| sl_tag_first_code | varchar | 50 | YES | NO | - | 销售线索编码1级 |
| sl_tag_second_code | varchar | 50 | YES | NO | - | 销售线索编码2级 |
| sl_tag_three_code | varchar | 50 | YES | NO | - | 销售线索编码3级 |
| sl_tag_first | varchar | 100 | YES | NO | - | 销售线索1级 |
| sl_tag_second | varchar | 100 | YES | NO | - | 销售线索2级 |
| sl_tag_three | varchar | 100 | YES | NO | - | 销售线索3级 |

**OM标签字段**:

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| om_tag_first_code | varchar | 50 | YES | NO | - | 全媒体指标编码1级 |
| om_tag_second_code | varchar | 50 | YES | NO | - | 全媒体指标编码2级 |
| om_tag_three_code | varchar | 50 | YES | NO | - | 全媒体指标编码3级 |
| om_tag_first | varchar | 100 | YES | NO | - | 全媒体指标1级 |
| om_tag_second | varchar | 100 | YES | NO | - | 全媒体指标2级 |
| om_tag_three | varchar | 100 | YES | NO | - | 全媒体指标3级 |

## 3. 维度表

### 3.1 dim_voc2_cust_info (客户信息维度表)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| oneid | varchar | 65533 | NO | YES | - | 客户唯一标识 |
| cust_classify | varchar | 65533 | YES | NO | - | 客户分类 |
| id_card_type | varchar | 65533 | YES | NO | - | 身份证类型 |
| id_card_no | varchar | 65533 | YES | NO | - | 身份证号码 |
| global_id | varchar | 65533 | YES | NO | - | 全局ID |
| email | varchar | 65533 | YES | NO | - | 邮箱 |
| mobile | varchar | 65533 | YES | NO | - | 手机号 |
| cust_nm | varchar | 65533 | YES | NO | - | 客户姓名 |
| gender | varchar | 65533 | YES | NO | - | 性别 |
| age | varchar | 65533 | YES | NO | - | 年龄 |
| age_group | varchar | 65533 | YES | NO | - | 年龄段 |
| birthday_dt | varchar | 65533 | YES | NO | - | 生日日期 |
| birthday | varchar | 65533 | YES | NO | - | 生日 |
| born_years | varchar | 65533 | YES | NO | - | 出生年份 |
| life_stage | varchar | 65533 | YES | NO | - | 人生阶段 |
| constellation | varchar | 65533 | YES | NO | - | 星座 |
| zodiac | varchar | 65533 | YES | NO | - | 生肖 |
| high_educaion | varchar | 65533 | YES | NO | - | 最高学历 |
| marriage_statue | varchar | 65533 | YES | NO | - | 婚姻状况 |
| hukou_prov_cd | varchar | 65533 | YES | NO | - | 户籍省份编码 |
| hukou_prov_nm | varchar | 65533 | YES | NO | - | 户籍省份名称 |
| hukou_city_cd | varchar | 65533 | YES | NO | - | 户籍城市编码 |
| hukou_city_nm | varchar | 65533 | YES | NO | - | 户籍城市名称 |
| hukou_cty_cd | varchar | 65533 | YES | NO | - | 户籍区县编码 |
| hukou_cty_nm | varchar | 65533 | YES | NO | - | 户籍区县名称 |
| lived_prov_cd | varchar | 65533 | YES | NO | - | 居住省份编码 |
| lived_prov_nm | varchar | 65533 | YES | NO | - | 居住省份名称 |
| lived_city_cd | varchar | 65533 | YES | NO | - | 居住城市编码 |
| lived_city_nm | varchar | 65533 | YES | NO | - | 居住城市名称 |
| lived_cty_cd | varchar | 65533 | YES | NO | - | 居住区县编码 |
| lived_cty_nm | varchar | 65533 | YES | NO | - | 居住区县名称 |
| lived_addr | varchar | 65533 | YES | NO | - | 居住地址 |
| profession | varchar | 65533 | YES | NO | - | 职业 |
| family_income | varchar | 65533 | YES | NO | - | 家庭收入 |
| cust_type | varchar | 65533 | YES | NO | - | 客户类型 |
| is_exchange_flg | bigint | - | YES | NO | - | 是否置换标志 |
| is_re_purchase_flg | bigint | - | YES | NO | - | 是否复购标志 |
| is_recommend_flg | bigint | - | YES | NO | - | 是否推荐标志 |
| is_car_owner_flg | bigint | - | YES | NO | - | 是否车主标志 |
| is_deal_flg | bigint | - | YES | NO | - | 是否成交标志 |
| is_uni_owner_flg | bigint | - | YES | NO | - | 是否长安车主标志 |
| is_jc_owner_flg | bigint | - | YES | NO | - | 是否轿车车主标志 |
| is_wc_owner_flg | bigint | - | YES | NO | - | 是否微车车主标志 |
| is_ev_owner_flg | bigint | - | YES | NO | - | 是否电动车车主标志 |
| is_qxc_owner_flg | bigint | - | YES | NO | - | 是否轻卡车主标志 |
| purchase_car_qty | bigint | - | YES | NO | - | 购车数量 |
| purchase_car_times | bigint | - | YES | NO | - | 购车次数 |
| lately_purchase_time | varchar | 65533 | YES | NO | - | 最近购车时间 |
| his_consume_amt | decimal | 32,8 | YES | NO | - | 历史消费金额 |
| is_member_flg | bigint | - | YES | NO | - | 是否会员标志 |
| member_register_mth | varchar | 65533 | YES | NO | - | 会员注册月份 |
| mem_activity | varchar | 65533 | YES | NO | - | 会员活跃度 |
| is_birthday_1day_flg | bigint | - | YES | NO | - | 是否生日当天标志 |
| is_birthday_30day_flg | bigint | - | YES | NO | - | 是否生日30天内标志 |
| is_birthday_60day_flg | bigint | - | YES | NO | - | 是否生日60天内标志 |
| dw_insert_time | varchar | 65533 | YES | NO | - | 数据仓库插入时间 |
| dw_update_time | varchar | 65533 | YES | NO | - | 数据仓库更新时间 |
| batch_dt | varchar | 65533 | YES | NO | - | 批次日期 |
| job_nm | varchar | 65533 | YES | NO | - | 作业名称 |

### 3.2 dim_voc2_dealer_info (经销商信息维度表)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| dealer_id | varchar | 65533 | NO | YES | - | 经销商ID |
| dealer_code | varchar | 65533 | YES | NO | - | 经销商编码 |
| erp_code | varchar | 65533 | YES | NO | - | ERP编码 |
| dealer_name | varchar | 65533 | YES | NO | - | 经销商名称 |
| dealer_shortname | varchar | 65533 | YES | NO | - | 经销商简称 |
| dealer_type | varchar | 65533 | YES | NO | - | 经销商类型 |
| dealer_type_name | varchar | 65533 | YES | NO | - | 经销商类型名称 |
| dealer_level | varchar | 65533 | YES | NO | - | 经销商等级 |
| dealer_level_name | varchar | 65533 | YES | NO | - | 经销商等级名称 |
| status | varchar | 65533 | YES | NO | - | 状态 |
| status_name | varchar | 65533 | YES | NO | - | 状态名称 |
| dealer_class | varchar | 65533 | YES | NO | - | 经销商分类 |
| dealer_class_name | varchar | 65533 | YES | NO | - | 经销商分类名称 |
| parent_dealer_code | varchar | 65533 | YES | NO | - | 父级经销商编码 |
| parent_dealer_name | varchar | 65533 | YES | NO | - | 父级经销商名称 |
| company_code | varchar | 65533 | YES | NO | - | 公司编码 |
| company_name | varchar | 65533 | YES | NO | - | 公司名称 |
| region_code_new | varchar | 65533 | YES | NO | - | 新区域编码 |
| region_name_new | varchar | 65533 | YES | NO | - | 新区域名称 |
| province_code | varchar | 65533 | YES | NO | - | 省份编码 |
| province_name | varchar | 65533 | YES | NO | - | 省份名称 |
| city_code | varchar | 65533 | YES | NO | - | 城市编码 |
| city_name | varchar | 65533 | YES | NO | - | 城市名称 |
| district | varchar | 65533 | YES | NO | - | 区县 |
| district_name | varchar | 65533 | YES | NO | - | 区县名称 |
| zip_code | varchar | 65533 | YES | NO | - | 邮编 |
| address | varchar | 65533 | YES | NO | - | 地址 |
| phone | varchar | 65533 | YES | NO | - | 电话 |
| fax_no | varchar | 65533 | YES | NO | - | 传真号 |
| link_man | varchar | 65533 | YES | NO | - | 联系人 |
| email | varchar | 65533 | YES | NO | - | 邮箱 |
| begin_bank | varchar | 65533 | YES | NO | - | 开户银行 |
| remark | varchar | 65533 | YES | NO | - | 备注 |
| tree_code | varchar | 65533 | YES | NO | - | 树形编码 |
| company_id | bigint | - | YES | NO | - | 公司ID |
| oem_company_id | bigint | - | YES | NO | - | OEM公司ID |
| price_id | bigint | - | YES | NO | - | 价格ID |
| dealer_star | varchar | 65533 | YES | NO | - | 经销商星级 |
| taxes_no | varchar | 65533 | YES | NO | - | 税号 |
| area_level | varchar | 65533 | YES | NO | - | 区域等级 |
| service_level | varchar | 65533 | YES | NO | - | 服务等级 |
| dealer_labour_type | varchar | 65533 | YES | NO | - | 经销商劳务类型 |
| is_dqv | varchar | 65533 | YES | NO | - | 是否DQV |
| balance_level | varchar | 65533 | YES | NO | - | 结算等级 |
| invoice_level | varchar | 65533 | YES | NO | - | 开票等级 |
| begin_balance_date | varchar | 65533 | YES | NO | - | 开始结算日期 |
| end_balance_date | varchar | 65533 | YES | NO | - | 结束结算日期 |
| begin_old_date | varchar | 65533 | YES | NO | - | 开始旧日期 |
| end_old_date | varchar | 65533 | YES | NO | - | 结束旧日期 |
| counties | varchar | 65533 | YES | NO | - | 县区 |
| township | varchar | 65533 | YES | NO | - | 乡镇 |
| legal | varchar | 65533 | YES | NO | - | 法人 |
| webmaster_phone | varchar | 65533 | YES | NO | - | 网管电话 |
| duty_phone | varchar | 65533 | YES | NO | - | 值班电话 |
| bank | varchar | 65533 | YES | NO | - | 银行 |
| tax_level | varchar | 65533 | YES | NO | - | 税务等级 |
| change_date | varchar | 65533 | YES | NO | - | 变更日期 |
| main_resources | varchar | 65533 | YES | NO | - | 主要资源 |
| image_level | varchar | 65533 | YES | NO | - | 形象等级 |
| image_date | varchar | 65533 | YES | NO | - | 形象日期 |
| admin_level | varchar | 65533 | YES | NO | - | 管理等级 |
| person_charge | varchar | 65533 | YES | NO | - | 负责人 |
| remark1 | varchar | 65533 | YES | NO | - | 备注1 |
| lng_x | varchar | 65533 | YES | NO | - | 经度 |
| lat_y | varchar | 65533 | YES | NO | - | 纬度 |
| create_date | varchar | 65533 | YES | NO | - | 创建日期 |
| create_by | bigint | - | YES | NO | - | 创建人 |
| update_date | varchar | 65533 | YES | NO | - | 更新日期 |
| update_by | bigint | - | YES | NO | - | 更新人 |
| region_code | varchar | 65533 | YES | NO | - | 区域编码 |
| region_name | varchar | 65533 | YES | NO | - | 区域名称 |
| dealer_lv1_id | bigint | - | YES | NO | - | 一级经销商ID |
| dealer_lv1_code | varchar | 65533 | YES | NO | - | 一级经销商编码 |
| dealer_lv1_name | varchar | 65533 | YES | NO | - | 一级经销商名称 |
| dealer_lv2_id | bigint | - | YES | NO | - | 二级经销商ID |
| dealer_lv2_code | varchar | 65533 | YES | NO | - | 二级经销商编码 |
| dealer_lv2_name | varchar | 65533 | YES | NO | - | 二级经销商名称 |
| dealer_lv3_id | bigint | - | YES | NO | - | 三级经销商ID |
| dealer_lv3_code | varchar | 65533 | YES | NO | - | 三级经销商编码 |
| dealer_lv3_name | varchar | 65533 | YES | NO | - | 三级经销商名称 |
| sale_hotline | varchar | 65533 | YES | NO | - | 销售热线 |
| sale_province | varchar | 65533 | YES | NO | - | 销售省份 |
| sale_city | varchar | 65533 | YES | NO | - | 销售城市 |
| sale_town | varchar | 65533 | YES | NO | - | 销售乡镇 |
| sale_address_describe | varchar | 65533 | YES | NO | - | 销售地址描述 |
| service_hotline | varchar | 65533 | YES | NO | - | 服务热线 |
| service_province | varchar | 65533 | YES | NO | - | 服务省份 |
| service_city | varchar | 65533 | YES | NO | - | 服务城市 |
| service_town | varchar | 65533 | YES | NO | - | 服务乡镇 |
| service_address_describe | varchar | 65533 | YES | NO | - | 服务地址描述 |
| emergency_phone | varchar | 65533 | YES | NO | - | 紧急电话 |
| sale_manager_name | varchar | 65533 | YES | NO | - | 销售经理姓名 |
| sale_manager_phone | varchar | 65533 | YES | NO | - | 销售经理电话 |
| relationship_manager_name | varchar | 65533 | YES | NO | - | 关系经理姓名 |
| relationship_manager_phone | varchar | 65533 | YES | NO | - | 关系经理电话 |
| service_manager_name | varchar | 65533 | YES | NO | - | 服务经理姓名 |
| service_manager_phone | varchar | 65533 | YES | NO | - | 服务经理电话 |
| business_area_flag | varchar | 65533 | YES | NO | - | 业务区域标志 |
| business_area | varchar | 65533 | YES | NO | - | 业务区域 |
| new_dealer_class | varchar | 65533 | YES | NO | - | 新经销商分类 |
| manage_org_code | varchar | 65533 | YES | NO | - | 管理机构编码 |
| manage_org_name | varchar | 65533 | YES | NO | - | 管理机构名称 |
| dealer_group_type | varchar | 65533 | YES | NO | - | 经销商集团类型 |
| dealer_group_code | varchar | 65533 | YES | NO | - | 经销商集团编码 |
| dealer_group_name | varchar | 65533 | YES | NO | - | 经销商集团名称 |
| invest_code | varchar | 65533 | YES | NO | - | 投资编码 |
| invest_name | varchar | 65533 | YES | NO | - | 投资名称 |
| ccu_code | varchar | 65533 | YES | NO | - | CCU编码 |
| ccu_name | varchar | 65533 | YES | NO | - | CCU名称 |
| source_code | varchar | 65533 | YES | NO | - | 来源编码 |
| w_insert_dt | varchar | 65533 | YES | NO | - | 插入日期 |
| start_date_active | varchar | 65533 | YES | NO | - | 开始有效日期 |
| end_date_active | varchar | 65533 | YES | NO | - | 结束有效日期 |
| enable_flag | varchar | 65533 | YES | NO | - | 启用标志 |
| sdu_user_id | varchar | 65533 | YES | NO | - | SDU用户ID |
| sdu_user | varchar | 65533 | YES | NO | - | SDU用户 |
| org_master | varchar | 65533 | YES | NO | - | 组织主控 |
| branch_org_master | varchar | 65533 | YES | NO | - | 分支组织主控 |
| qdxt_code | varchar | 65533 | YES | NO | - | QDXT编码 |
| qdxt_name | varchar | 65533 | YES | NO | - | QDXT名称 |
| dealer_lv1_code_orgion | varchar | 65533 | YES | NO | - | 原始一级经销商编码 |
| dealer_lv1_name_orgion | varchar | 65533 | YES | NO | - | 原始一级经销商名称 |

### 3.3 dim_voc2_vehicle_info (车辆信息维度表)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| vehicle_id | bigint | - | NO | YES | - | 车辆ID |
| create_date | datetime | - | NO | YES | - | 创建日期 |
| material_code | varchar | 65533 | YES | NO | - | 物料编码 |
| material_name | varchar | 65533 | YES | NO | - | 物料名称 |
| erp_id | bigint | - | YES | NO | - | ERP ID |
| erp_name | varchar | 65533 | YES | NO | - | ERP名称 |
| source_code | varchar | 65533 | YES | NO | - | 来源编码 |
| brand_code | varchar | 65533 | YES | NO | - | 品牌编码 |
| brand_name | varchar | 65533 | YES | NO | - | 品牌名称 |
| series_code | varchar | 65533 | YES | NO | - | 车系编码 |
| series_name | varchar | 65533 | YES | NO | - | 车系名称 |
| model_code | varchar | 65533 | YES | NO | - | 车型编码 |
| model_name | varchar | 65533 | YES | NO | - | 车型名称 |
| opt_code | varchar | 65533 | YES | NO | - | 配置编码 |
| opt_name | varchar | 65533 | YES | NO | - | 配置名称 |
| color_code | varchar | 65533 | YES | NO | - | 颜色编码 |
| color_name | varchar | 65533 | YES | NO | - | 颜色名称 |
| org_type | varchar | 65533 | YES | NO | - | 组织类型 |
| org_type_name | varchar | 65533 | YES | NO | - | 组织类型名称 |
| dealer_id | bigint | - | YES | NO | - | 经销商ID |
| vin | varchar | 65533 | YES | NO | - | 车架号 |
| life_cycle | varchar | 65533 | YES | NO | - | 生命周期 |
| life_cycle_name | varchar | 65533 | YES | NO | - | 生命周期名称 |
| lock_status | varchar | 65533 | YES | NO | - | 锁定状态 |
| lock_status_name | varchar | 65533 | YES | NO | - | 锁定状态名称 |
| license_no | varchar | 65533 | YES | NO | - | 车牌号 |
| engine_no | varchar | 65533 | YES | NO | - | 发动机号 |
| package_id | bigint | - | YES | NO | - | 包ID |
| purchased_date | varchar | 65533 | YES | NO | - | 购买日期 |
| product_date | varchar | 65533 | YES | NO | - | 生产日期 |
| factory_date | varchar | 65533 | YES | NO | - | 出厂日期 |
| mileage | varchar | 65533 | YES | NO | - | 里程数 |
| storage_date | varchar | 65533 | YES | NO | - | 入库日期 |
| warehouse_id | bigint | - | YES | NO | - | 仓库ID |
| transfer_no | varchar | 65533 | YES | NO | - | 调拨单号 |
| yieldly | varchar | 65533 | YES | NO | - | 产量 |
| org_storage_date | varchar | 65533 | YES | NO | - | 原始入库日期 |
| special_batch_no | varchar | 65533 | YES | NO | - | 特殊批次号 |
| vehicle_area | varchar | 65533 | YES | NO | - | 车辆区域 |
| oem_company_id | varchar | 65533 | YES | NO | - | OEM公司ID |
| batch_no | varchar | 65533 | YES | NO | - | 批次号 |
| claim_tactics_id | varchar | 65533 | YES | NO | - | 索赔策略ID |
| n_warehouse_id | varchar | 65533 | YES | NO | - | 新仓库ID |
| erp_storage_date | varchar | 65533 | YES | NO | - | ERP入库日期 |
| org_id | bigint | - | YES | NO | - | 组织ID |
| vn | varchar | 65533 | YES | NO | - | VN |
| node_code | varchar | 65533 | YES | NO | - | 节点编码 |
| node_date | varchar | 65533 | YES | NO | - | 节点日期 |
| location | varchar | 65533 | YES | NO | - | 位置 |
| vehicle_type | varchar | 65533 | YES | NO | - | 车辆类型 |
| remark | varchar | 65533 | YES | NO | - | 备注 |
| gearbox_no | varchar | 65533 | YES | NO | - | 变速箱号 |
| rearaxle_no | varchar | 65533 | YES | NO | - | 后桥号 |
| model_year | varchar | 65533 | YES | NO | - | 车型年份 |
| license_date | varchar | 65533 | YES | NO | - | 上牌日期 |
| start_mileage | varchar | 65533 | YES | NO | - | 起始里程 |
| meter_mile | varchar | 65533 | YES | NO | - | 表显里程 |
| history_mile | varchar | 65533 | YES | NO | - | 历史里程 |
| ver | varchar | 65533 | YES | NO | - | 版本 |
| free_times | bigint | - | YES | NO | - | 免费次数 |
| create_by | bigint | - | YES | NO | - | 创建人 |
| update_by | bigint | - | YES | NO | - | 更新人 |
| update_date | varchar | 65533 | YES | NO | - | 更新日期 |
| w_insert_dt | varchar | 65533 | YES | NO | - | 插入日期 |
| start_date_active | varchar | 65533 | YES | NO | - | 开始有效日期 |
| end_date_active | varchar | 65533 | YES | NO | - | 结束有效日期 |
| enable_flag | varchar | 65533 | YES | NO | - | 启用标志 |
| ev_flag | varchar | 65533 | YES | NO | - | 电动车标志 |

## 4. 代码值定义

### 4.1 渠道编码 (channel_code)

| 代码值 | 说明 |
|--------|------|
| WEIBO | 微博 |
| AUTOHOME | 汽车之家 |
| DOUYIN | 抖音 |
| XIAOHONGSHU | 小红书 |
| ZHIHU | 知乎 |
| WEB | 网站 |
| TIEBA | 贴吧 |
| WECHAT | 微信 |

### 4.2 情感倾向 (sentiment)

| 代码值 | 说明 |
|--------|------|
| 正面 | 积极正面的情感 |
| 负面 | 消极负面的情感 |
| 中性 | 中性客观的情感 |

### 4.3 客户性别 (cust_gender)

| 代码值 | 说明 |
|--------|------|
| 男 | 男性 |
| 女 | 女性 |
| 未知 | 性别未知 |

### 4.4 客户类型 (cust_type)

| 代码值 | 说明 |
|--------|------|
| 潜在客户 | 有购买意向但未成交的客户 |
| 意向客户 | 有明确购买意向的客户 |
| 成交客户 | 已成功购买的客户 |
| 流失客户 | 曾经购买但已流失的客户 |

### 4.5 经销商类型 (dealer_type)

| 代码值 | 说明 |
|--------|------|
| 4S店 | 4S经销商 |
| 直营店 | 直营经销商 |
| 授权店 | 授权经销商 |
| 服务店 | 服务经销商 |

### 4.6 经销商等级 (dealer_level)

| 代码值 | 说明 |
|--------|------|
| A级 | A级经销商 |
| B级 | B级经销商 |
| C级 | C级经销商 |
| D级 | D级经销商 |

### 4.7 内容类型 (content_type)

| 代码值 | 说明 |
|--------|------|
| 帖子 | 论坛帖子 |
| 评论 | 评论内容 |
| 问答 | 问答内容 |
| 问卷 | 问卷内容 |
| 工单 | 工单内容 |

### 4.8 问卷类型 (quest_type)

| 代码值 | 说明 |
|--------|------|
| 满意度调查 | 客户满意度调查 |
| 产品调研 | 产品调研问卷 |
| 服务评价 | 服务评价问卷 |
| 市场调研 | 市场调研问卷 |

## 5. 业务规则

### 5.1 数据完整性规则
- 声音数据必须包含客户信息
- 声音数据必须包含渠道信息
- 声音数据必须包含时间信息
- 标签编码必须与标签名称对应

### 5.2 数据一致性规则
- 品牌编码必须与品牌名称一致
- 车系编码必须与车系名称一致
- 车型编码必须与车型名称一致
- 经销商编码必须与经销商名称一致

### 5.3 数据有效性规则
- 时间字段格式必须为YYYY-MM-DD HH:MM:SS
- 手机号格式必须为11位数字
- 年龄必须在0-150之间
- 分数必须在0-100之间

### 5.4 业务逻辑规则
- 车主客户必须关联车辆信息
- 成交客户必须有购买记录
- 经销商必须属于有效的区域
- 标签层级关系必须正确

## 6. 数据质量指标

### 6.1 完整性指标
- 主键完整性: 100%
- 必填字段完整性: >95%
- 外键关联完整性: >90%

### 6.2 准确性指标
- 情感标注准确率: >85%
- 标签标注准确率: >80%
- 地理位置准确率: >90%

### 6.3 一致性指标
- 编码一致性: >95%
- 命名一致性: >95%
- 格式一致性: >90%

### 6.4 及时性指标
- 数据更新及时性: <24小时
- 标签更新及时性: <48小时
- 统计更新及时性: <72小时 

## 3. 视图层

### 3.1 业务视图

#### 3.1.1 voc2_computed_result_all_data_m_v (计算结果汇总视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| id | varchar | 255 | YES | YES | - | 计算结果ID |
| data_id | varchar | 255 | YES | NO | - | 数据唯一标识 |
| channel_code | varchar | 50 | YES | NO | - | 渠道编码 |
| brand_code | varchar | 50 | YES | NO | - | 品牌编码 |
| vehicle_series_code | varchar | 50 | YES | NO | - | 车系编码 |
| sentiment | varchar | 50 | YES | NO | - | 情感倾向 |
| data_create_time | datetime | - | YES | NO | - | 数据产生时间 |
| computed_time | datetime | - | YES | NO | - | 计算时间 |
| result_type | varchar | 100 | YES | NO | - | 结果类型 |
| result_value | decimal | 10,2 | YES | NO | - | 结果值 |

#### 3.1.2 voc2_cust_info_m_v (客户信息视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| one_id | varchar | 100 | YES | YES | - | 客户唯一标识 |
| cust_name | varchar | 100 | YES | NO | - | 客户姓名 |
| cust_mobile | varchar | 20 | YES | NO | - | 客户手机号 |
| cust_age | int | 11 | YES | NO | - | 客户年龄 |
| cust_gender | varchar | 10 | YES | NO | - | 客户性别 |
| cust_province_code | varchar | 20 | YES | NO | - | 客户常驻省份编码 |
| cust_province | varchar | 100 | YES | NO | - | 客户常驻省份 |
| cust_city_code | varchar | 20 | YES | NO | - | 客户常驻市编码 |
| cust_city | varchar | 100 | YES | NO | - | 客户常驻市 |
| cust_district_code | varchar | 20 | YES | NO | - | 客户常驻区编码 |
| cust_district | varchar | 100 | YES | NO | - | 客户常驻区 |
| cust_highest_edu | varchar | 100 | YES | NO | - | 客户最高学历 |
| cust_monthly_income | varchar | 100 | YES | NO | - | 客户月收入 |
| cust_last_purchase_date | datetime | - | YES | NO | - | 客户最近一次购车时间 |
| cust_type | varchar | 100 | YES | NO | - | 客户类型 |
| is_vehicle_owner | boolean | - | YES | NO | - | 是否车主 |
| update_time | datetime | - | YES | NO | - | 更新时间 |

#### 3.1.3 voc2_dealer_info_m_v (经销商信息视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| dealer_id | varchar | 100 | YES | YES | - | 经销商ID |
| dealer_code | varchar | 50 | YES | NO | - | 经销商编码 |
| dealer_name | varchar | 255 | YES | NO | - | 经销商全称 |
| dealer_province_code | varchar | 20 | YES | NO | - | 经销商所属省份编码 |
| dealer_province | varchar | 100 | YES | NO | - | 经销商所属省份 |
| dealer_city_code | varchar | 20 | YES | NO | - | 经销商所在市编码 |
| dealer_city | varchar | 100 | YES | NO | - | 经销商所在市 |
| dealer_regional_code | varchar | 50 | YES | NO | - | 经销商所在大区编码 |
| dealer_regional | varchar | 100 | YES | NO | - | 经销商所在大区 |
| dealer_type | varchar | 100 | YES | NO | - | 经销商类型 |
| dealer_level | varchar | 50 | YES | NO | - | 经销商等级 |
| update_time | datetime | - | YES | NO | - | 更新时间 |

#### 3.1.4 voc2_vehicle_info_m_v (车辆信息视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| vehicle_id | varchar | 100 | YES | YES | - | 车辆ID |
| vehicle_vin | varchar | 100 | YES | NO | - | 车辆车架号 |
| brand_code | varchar | 50 | YES | NO | - | 品牌编码 |
| brand | varchar | 100 | YES | NO | - | 品牌名称 |
| vehicle_series_code | varchar | 50 | YES | NO | - | 车系编码 |
| vehicle_series | varchar | 100 | YES | NO | - | 车系名称 |
| vehicle_model_code | varchar | 50 | YES | NO | - | 车型编码 |
| vehicle_model | varchar | 100 | YES | NO | - | 车型名称 |
| vehicle_purchase_date | datetime | - | YES | NO | - | 车辆购买日期 |
| vehicle_production_date | datetime | - | YES | NO | - | 车辆生产日期 |
| vehicle_factory_release_date | datetime | - | YES | NO | - | 车辆出厂日期 |
| update_time | datetime | - | YES | NO | - | 更新时间 |

#### 3.1.5 voc2_dealership_data_info_m_v (经销商数据视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| dealer_id | varchar | 100 | YES | YES | - | 经销商ID |
| dealer_code | varchar | 50 | YES | NO | - | 经销商编码 |
| dealer_name | varchar | 255 | YES | NO | - | 经销商名称 |
| data_type | varchar | 100 | YES | NO | - | 数据类型 |
| data_value | decimal | 10,2 | YES | NO | - | 数据值 |
| data_date | date | - | YES | NO | - | 数据日期 |
| update_time | datetime | - | YES | NO | - | 更新时间 |

#### 3.1.6 voc2_voc2_computed_result_all_data_v (计算结果数据视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| id | varchar | 255 | YES | YES | - | 计算结果ID |
| data_id | varchar | 255 | YES | NO | - | 数据唯一标识 |
| channel_code | varchar | 50 | YES | NO | - | 渠道编码 |
| brand_code | varchar | 50 | YES | NO | - | 品牌编码 |
| vehicle_series_code | varchar | 50 | YES | NO | - | 车系编码 |
| sentiment | varchar | 50 | YES | NO | - | 情感倾向 |
| data_create_time | datetime | - | YES | NO | - | 数据产生时间 |
| computed_time | datetime | - | YES | NO | - | 计算时间 |
| result_type | varchar | 100 | YES | NO | - | 结果类型 |
| result_value | decimal | 10,2 | YES | NO | - | 结果值 |
| result_status | varchar | 50 | YES | NO | - | 结果状态 |

### 3.2 权限视图

#### 3.2.1 voc2_user_label_scope_perms_v (用户标签权限视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| user_id | varchar | 100 | YES | YES | - | 用户ID |
| label_code | varchar | 100 | YES | NO | - | 标签编码 |
| label_name | varchar | 255 | YES | NO | - | 标签名称 |
| permission_type | varchar | 50 | YES | NO | - | 权限类型 |
| scope_level | varchar | 50 | YES | NO | - | 权限范围级别 |
| is_active | boolean | - | YES | NO | - | 是否激活 |
| update_time | datetime | - | YES | NO | - | 更新时间 |

#### 3.2.2 voc2_ins_car_series_perms_v (车系权限视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| user_id | varchar | 100 | YES | YES | - | 用户ID |
| car_series_code | varchar | 50 | YES | NO | - | 车系编码 |
| car_series_name | varchar | 100 | YES | NO | - | 车系名称 |
| permission_type | varchar | 50 | YES | NO | - | 权限类型 |
| is_active | boolean | - | YES | NO | - | 是否激活 |
| update_time | datetime | - | YES | NO | - | 更新时间 |

### 3.3 配置视图

#### 3.3.1 voc2_ins_car_series_info_m_v (车系配置视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| car_series_code | varchar | 50 | YES | YES | - | 车系编码 |
| car_series_name | varchar | 100 | YES | NO | - | 车系名称 |
| brand_code | varchar | 50 | YES | NO | - | 品牌编码 |
| brand_name | varchar | 100 | YES | NO | - | 品牌名称 |
| is_active | boolean | - | YES | NO | - | 是否激活 |
| sort_order | int | 11 | YES | NO | - | 排序顺序 |
| update_time | datetime | - | YES | NO | - | 更新时间 |

#### 3.3.2 voc2_ins_channel_info_m_v (渠道配置视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| channel_code | varchar | 50 | YES | YES | - | 渠道编码 |
| channel_name | varchar | 100 | YES | NO | - | 渠道名称 |
| channel_type | varchar | 50 | YES | NO | - | 渠道类型 |
| is_active | boolean | - | YES | NO | - | 是否激活 |
| sort_order | int | 11 | YES | NO | - | 排序顺序 |
| update_time | datetime | - | YES | NO | - | 更新时间 |

#### 3.3.3 voc2_ins_project_car_series_info_v (项目车系视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| project_id | varchar | 100 | YES | YES | - | 项目ID |
| car_series_code | varchar | 50 | YES | NO | - | 车系编码 |
| car_series_name | varchar | 100 | YES | NO | - | 车系名称 |
| project_name | varchar | 255 | YES | NO | - | 项目名称 |
| is_active | boolean | - | YES | NO | - | 是否激活 |
| update_time | datetime | - | YES | NO | - | 更新时间 |

#### 3.3.4 voc2_ins_province_area_m_v (省份区域视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| province_code | varchar | 20 | YES | YES | - | 省份编码 |
| province_name | varchar | 100 | YES | NO | - | 省份名称 |
| area_code | varchar | 50 | YES | NO | - | 区域编码 |
| area_name | varchar | 100 | YES | NO | - | 区域名称 |
| is_active | boolean | - | YES | NO | - | 是否激活 |
| sort_order | int | 11 | YES | NO | - | 排序顺序 |
| update_time | datetime | - | YES | NO | - | 更新时间 |

#### 3.3.5 voc2_ins_tags_info_m_v (标签配置视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| tag_code | varchar | 100 | YES | YES | - | 标签编码 |
| tag_name | varchar | 255 | YES | NO | - | 标签名称 |
| tag_category | varchar | 50 | YES | NO | - | 标签分类 |
| tag_level | int | 11 | YES | NO | - | 标签层级 |
| parent_code | varchar | 100 | YES | NO | - | 父级标签编码 |
| is_active | boolean | - | YES | NO | - | 是否激活 |
| sort_order | int | 11 | YES | NO | - | 排序顺序 |
| update_time | datetime | - | YES | NO | - | 更新时间 |

### 3.4 元数据视图

#### 3.4.1 voc2_raw_meta_data_range_m_v (原始数据元数据视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| data_source | varchar | 100 | YES | YES | - | 数据源 |
| table_name | varchar | 255 | YES | NO | - | 表名 |
| min_date | date | - | YES | NO | - | 最小日期 |
| max_date | date | - | YES | NO | - | 最大日期 |
| record_count | bigint | - | YES | NO | - | 记录数 |
| update_time | datetime | - | YES | NO | - | 更新时间 |

#### 3.4.2 voc2_sta_dict_v (统计字典视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| dict_code | varchar | 100 | YES | YES | - | 字典编码 |
| dict_name | varchar | 255 | YES | NO | - | 字典名称 |
| dict_type | varchar | 50 | YES | NO | - | 字典类型 |
| dict_value | varchar | 255 | YES | NO | - | 字典值 |
| is_active | boolean | - | YES | NO | - | 是否激活 |
| update_time | datetime | - | YES | NO | - | 更新时间 |

#### 3.4.3 voc2_sta_tag_level_values_v (标签层级统计视图)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 默认值 | 说明 |
|--------|----------|------|----------|------|--------|------|
| tag_category | varchar | 50 | YES | YES | - | 标签分类 |
| tag_level | int | 11 | YES | NO | - | 标签层级 |
| tag_code | varchar | 100 | YES | NO | - | 标签编码 |
| tag_name | varchar | 255 | YES | NO | - | 标签名称 |
| usage_count | bigint | - | YES | NO | - | 使用次数 |
| update_time | datetime | - | YES | NO | - | 更新时间 | 