# VOC数据库表结构设计文档

## 1. 核心事实表

### 1.1 dws_voc2_sounds_data (声音数据汇总表)

**表描述**: VOC平台的核心事实表，包含所有声音数据的汇总信息，是业务分析的主要数据源。

**表特征**:
- 数据量: 220,500条记录
- 分区策略: 按时间分区（季度）
- 主键: id
- 分布策略: HASH(id) BUCKETS 10
- 存储引擎: StarRocks OLAP

#### 1.1.1 基础信息字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| id | varchar(255) | YES | YES | 声音ID，主键 |
| data_id | varchar(255) | YES | NO | 数据唯一标识 |
| channel_code | varchar(50) | YES | NO | 渠道编码 |
| channel | varchar(100) | YES | NO | 渠道名称 |
| brand_code | varchar(50) | YES | NO | 品牌编码 |
| brand | varchar(100) | YES | NO | 品牌名称 |
| vehicle_series_code | varchar(50) | YES | NO | 车系编码 |
| vehicle_series | varchar(100) | YES | NO | 车系名称 |
| vehicle_model_code | varchar(50) | YES | NO | 车型编码 |
| vehicle_model | varchar(100) | YES | NO | 车型名称 |
| label_type | varchar(100) | YES | NO | 数据类型 |

#### 1.1.2 情感分析字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| sentiment | varchar(50) | YES | NO | 情感倾向（正面/负面/中性） |
| intention | varchar(100) | YES | NO | 意图分类 |
| hot_word | TEXT | YES | NO | 热词提取 |
| user_journey | TEXT | YES | NO | 用户旅程 |
| keywords | TEXT | YES | NO | 关键词 |
| topic | TEXT | YES | NO | 观点主题 |

#### 1.1.3 时间维度字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| data_create_time | datetime | YES | NO | 数据产生时间 |
| create_time | datetime | YES | NO | 数据抓取时间 |
| data_create_week | varchar(20) | YES | NO | 数据产生周期-周 |
| data_create_month | varchar(20) | YES | NO | 数据产生周期-月 |
| data_create_quarter | varchar(20) | YES | NO | 数据产生周期-季 |
| data_create_year | varchar(20) | YES | NO | 数据产生周期-年 |

#### 1.1.4 客户维度字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| one_id | varchar(100) | YES | NO | oneId |
| cust_name | varchar(100) | YES | NO | 客户姓名 |
| cust_mobile | varchar(20) | YES | NO | 客户手机号 |
| cust_age | int(11) | YES | NO | 客户年龄 |
| cust_gender | varchar(10) | YES | NO | 客户性别 |
| cust_province_code | varchar(20) | YES | NO | 客户常驻省份编码 |
| cust_province | varchar(100) | YES | NO | 客户常驻省份 |
| cust_city_code | varchar(20) | YES | NO | 客户常驻市编码 |
| cust_city | varchar(100) | YES | NO | 客户常驻市 |
| cust_district_code | varchar(20) | YES | NO | 客户常驻区编码 |
| cust_district | varchar(100) | YES | NO | 客户常驻区 |
| cust_highest_edu | varchar(100) | YES | NO | 客户最高学历 |
| cust_monthly_income | varchar(100) | YES | NO | 客户月收入 |
| cust_last_purchase_date | datetime | YES | NO | 客户最近一次购车时间 |
| cust_type | varchar(100) | YES | NO | 客户类型 |
| is_vehicle_owner | boolean | YES | NO | 是否车主 |

#### 1.1.5 经销商维度字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| dealer_id | varchar(100) | YES | NO | 经销商ID |
| dealer_code | varchar(50) | YES | NO | 经销商编码 |
| dealer_name | varchar(255) | YES | NO | 经销商全称 |
| dealer_province_code | varchar(20) | YES | NO | 经销商所属省份编码 |
| dealer_province | varchar(100) | YES | NO | 经销商所属省份 |
| dealer_city_code | varchar(20) | YES | NO | 经销商所在市编码 |
| dealer_city | varchar(100) | YES | NO | 经销商所在市 |
| dealer_regional_code | varchar(50) | YES | NO | 经销商所在大区编码 |
| dealer_regional | varchar(100) | YES | NO | 经销商所在大区 |

#### 1.1.6 车辆维度字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| vehicle_purchase_date | datetime | YES | NO | 车辆购买日期 |
| vehicle_production_date | datetime | YES | NO | 车辆生产日期 |
| vehicle_factory_release_date | datetime | YES | NO | 车辆出厂日期 |
| vehicle_vin | varchar(100) | YES | NO | 车辆车架号 |

#### 1.1.7 内容类型字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| content_type | varchar(100) | YES | NO | 原文内容类型 |
| work_order_id | varchar(100) | YES | NO | 工单ID |
| is_main_post | boolean | YES | NO | 是否主贴 |
| post_title | TEXT | YES | NO | 帖子标题 |
| post_original_link | TEXT | YES | NO | 帖子原文链接 |
| post_original_content | TEXT | YES | NO | 帖子原文详情 |
| quest_type | varchar(100) | YES | NO | 问卷类型 |
| quest_question_content | TEXT | YES | NO | 问卷题目/内容 |
| quest_answer_score | int(11) | YES | NO | 问卷答案分数 |
| quest_business_type | varchar(100) | YES | NO | 问卷业务类型 |
| quest_business_scenario | varchar(100) | YES | NO | 问卷业务场景 |

#### 1.1.8 多维度标签字段

**VTR标签（车辆技术相关）**:
- vtr_tag_first_code/vtr_tag_first: 一级标签编码/名称
- vtr_tag_second_code/vtr_tag_second: 二级标签编码/名称
- vtr_tag_three_code/vtr_tag_three: 三级标签编码/名称

**COM标签（商品化属性）**:
- com_tag_first_code/com_tag_first: 一级标签编码/名称
- com_tag_second_code/com_tag_second: 二级标签编码/名称
- com_tag_three_code/com_tag_three: 三级标签编码/名称

**ADB标签（全领域业务）**:
- adb_tag_first_code/adb_tag_first: 一级标签编码/名称
- adb_tag_second_code/adb_tag_second: 二级标签编码/名称
- adb_tag_three_code/adb_tag_three: 三级标签编码/名称

**WOM标签（口碑评价指标）**:
- wom_tag_first_code/wom_tag_first: 一级标签编码/名称
- wom_tag_second_code/wom_tag_second: 二级标签编码/名称
- wom_tag_three_code/wom_tag_three: 三级标签编码/名称

**CX标签（客户体验指标）**:
- cx_tag_first_code/cx_tag_first: 一级标签编码/名称
- cx_tag_second_code/cx_tag_second: 二级标签编码/名称
- cx_tag_three_code/cx_tag_three: 三级标签编码/名称

**CJ标签（全旅程客户）**:
- cj_tag_first_code/cj_tag_first: 一级标签编码/名称
- cj_tag_second_code/cj_tag_second: 二级标签编码/名称
- cj_tag_three_code/cj_tag_three: 三级标签编码/名称

**SL标签（销售线索）**:
- sl_tag_first_code/sl_tag_first: 一级标签编码/名称
- sl_tag_second_code/sl_tag_second: 二级标签编码/名称
- sl_tag_three_code/sl_tag_three: 三级标签编码/名称

**OM标签（全媒体指标）**:
- om_tag_first_code/om_tag_first: 一级标签编码/名称
- om_tag_second_code/om_tag_second: 二级标签编码/名称
- om_tag_three_code/om_tag_three: 三级标签编码/名称

### 1.2 分区策略

```sql
PARTITION BY RANGE(`data_create_time`)
(
    PARTITION p2023_Q1 VALUES [("0000-01-01 00:00:00"), ("2023-04-01 00:00:00")),
    PARTITION p2023_Q2 VALUES [("2023-04-01 00:00:00"), ("2023-07-01 00:00:00")),
    PARTITION p2023_Q3 VALUES [("2023-07-01 00:00:00"), ("2023-10-01 00:00:00")),
    PARTITION p2023_Q4 VALUES [("2023-10-01 00:00:00"), ("2024-01-01 00:00:00")),
    PARTITION p2024_Q1 VALUES [("2024-01-01 00:00:00"), ("2024-04-01 00:00:00")),
    PARTITION p2024_Q2 VALUES [("2024-04-01 00:00:00"), ("2024-07-01 00:00:00")),
    PARTITION p2024_Q3 VALUES [("2024-07-01 00:00:00"), ("2024-10-01 00:00:00")),
    PARTITION p2024_Q4 VALUES [("2024-10-01 00:00:00"), ("2025-01-01 00:00:00")),
    PARTITION p2025_Q1 VALUES [("2025-01-01 00:00:00"), ("2025-04-01 00:00:00")),
    PARTITION p2025_Q2 VALUES [("2025-04-01 00:00:00"), ("2025-07-01 00:00:00")),
    PARTITION p2025_Q3 VALUES [("2025-07-01 00:00:00"), ("2025-10-01 00:00:00")),
    PARTITION p2025_Q4 VALUES [("2025-10-01 00:00:00"), ("2026-01-01 00:00:00")),
    PARTITION p2026_Q1 VALUES [("2026-01-01 00:00:00"), ("2026-04-01 00:00:00")),
    PARTITION p2026_Q2 VALUES [("2026-04-01 00:00:00"), ("2026-07-01 00:00:00")),
    PARTITION p2026_Q3 VALUES [("2026-07-01 00:00:00"), ("2026-10-01 00:00:00")),
    PARTITION p2026_Q4 VALUES [("2026-10-01 00:00:00"), ("2027-01-01 00:00:00"))
)
```

## 2. 维度表

### 2.1 dim_voc2_cust_info (客户信息维度表)

**表描述**: 客户基本信息维度表，包含客户的人口统计学特征、购买历史等信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| oneid | varchar(65533) | NO | YES | 客户唯一标识 |
| cust_classify | varchar(65533) | YES | NO | 客户分类 |
| cust_nm | varchar(65533) | YES | NO | 客户姓名 |
| gender | varchar(65533) | YES | NO | 性别 |
| age | varchar(65533) | YES | NO | 年龄 |
| age_group | varchar(65533) | YES | NO | 年龄段 |
| high_educaion | varchar(65533) | YES | NO | 最高学历 |
| family_income | varchar(65533) | YES | NO | 家庭收入 |
| cust_type | varchar(65533) | YES | NO | 客户类型 |
| is_car_owner_flg | bigint | YES | NO | 是否车主 |
| purchase_car_qty | bigint | YES | NO | 购车数量 |
| purchase_car_times | bigint | YES | NO | 购车次数 |
| lately_purchase_time | varchar(65533) | YES | NO | 最近购车时间 |
| his_consume_amt | decimal(32,8) | YES | NO | 历史消费金额 |

### 2.2 dim_voc2_dealer_info (经销商信息维度表)

**表描述**: 经销商信息维度表，包含经销商的基本信息、层级结构、地理位置等。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| dealer_id | varchar(65533) | NO | YES | 经销商ID |
| dealer_code | varchar(65533) | YES | NO | 经销商编码 |
| dealer_name | varchar(65533) | YES | NO | 经销商名称 |
| dealer_type | varchar(65533) | YES | NO | 经销商类型 |
| dealer_level | varchar(65533) | YES | NO | 经销商等级 |
| status | varchar(65533) | YES | NO | 状态 |
| province_code | varchar(65533) | YES | NO | 省份编码 |
| province_name | varchar(65533) | YES | NO | 省份名称 |
| city_code | varchar(65533) | YES | NO | 城市编码 |
| city_name | varchar(65533) | YES | NO | 城市名称 |
| dealer_lv1_code | varchar(65533) | YES | NO | 一级经销商编码 |
| dealer_lv1_name | varchar(65533) | YES | NO | 一级经销商名称 |
| dealer_lv2_code | varchar(65533) | YES | NO | 二级经销商编码 |
| dealer_lv2_name | varchar(65533) | YES | NO | 二级经销商名称 |
| dealer_lv3_code | varchar(65533) | YES | NO | 三级经销商编码 |
| dealer_lv3_name | varchar(65533) | YES | NO | 三级经销商名称 |

### 2.3 dim_voc2_vehicle_info (车辆信息维度表)

**表描述**: 车辆信息维度表，包含车辆的基本信息、配置信息、生产信息等。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| vehicle_id | bigint | NO | YES | 车辆ID |
| create_date | datetime | NO | YES | 创建日期 |
| material_code | varchar(65533) | YES | NO | 物料编码 |
| material_name | varchar(65533) | YES | NO | 物料名称 |
| brand_code | varchar(65533) | YES | NO | 品牌编码 |
| brand_name | varchar(65533) | YES | NO | 品牌名称 |
| series_code | varchar(65533) | YES | NO | 车系编码 |
| series_name | varchar(65533) | YES | NO | 车系名称 |
| model_code | varchar(65533) | YES | NO | 车型编码 |
| model_name | varchar(65533) | YES | NO | 车型名称 |
| vin | varchar(65533) | YES | NO | 车架号 |
| purchased_date | varchar(65533) | YES | NO | 购买日期 |
| product_date | varchar(65533) | YES | NO | 生产日期 |
| factory_date | varchar(65533) | YES | NO | 出厂日期 |

## 3. 原始数据表

### 3.1 dwd_voc2_raw_public_opinion (公开意见原始表)

**表描述**: 公开渠道的意见数据原始表，包含从公开平台采集的客户意见信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| id | varchar(40) | NO | YES | 主键ID |
| data_create_time | datetime | NO | YES | 数据创建时间 |
| create_date | datetime | NO | NO | 创建日期 |
| data_id | varchar(40) | NO | NO | 数据ID |
| channel_code | varchar(30) | NO | NO | 渠道编码 |
| brand_code | varchar(65533) | YES | NO | 品牌编码 |
| series_code | varchar(65533) | YES | NO | 车系编码 |
| is_outer | varchar(1) | NO | NO | 是否外部数据 |
| one_id | varchar(65533) | YES | NO | oneId |
| user_id | varchar(65533) | YES | NO | 用户ID |
| user_name | varchar(65533) | NO | NO | 用户名 |
| title | varchar(65533) | NO | NO | 标题 |
| content | varchar(65533) | NO | NO | 内容 |
| is_wsater_army | varchar(1) | YES | NO | 是否水军 |
| attrs | json | YES | NO | 属性信息 |
| url | varchar(65533) | YES | NO | 原文链接 |

## 4. 视图层设计

### 4.1 业务视图

#### 4.1.1 voc2_computed_result_all_data_m_v (计算结果汇总视图)

**视图描述**: 计算结果汇总视图，提供业务分析所需的数据视图。

**主要特征**:
- 基于dws_voc2_sounds_data表
- 包含完整的业务字段
- 支持多维度分析
- 优化查询性能

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| id | varchar(255) | YES | YES | 计算结果ID |
| data_id | varchar(255) | YES | NO | 数据唯一标识 |
| channel_code | varchar(50) | YES | NO | 渠道编码 |
| brand_code | varchar(50) | YES | NO | 品牌编码 |
| vehicle_series_code | varchar(50) | YES | NO | 车系编码 |
| sentiment | varchar(50) | YES | NO | 情感倾向 |
| data_create_time | datetime | YES | NO | 数据产生时间 |
| computed_time | datetime | YES | NO | 计算时间 |
| result_type | varchar(100) | YES | NO | 结果类型 |
| result_value | decimal(10,2) | YES | NO | 结果值 |

#### 4.1.2 voc2_cust_info_m_v (客户信息视图)

**视图描述**: 客户信息视图，提供客户相关的汇总信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| one_id | varchar(100) | YES | YES | 客户唯一标识 |
| cust_name | varchar(100) | YES | NO | 客户姓名 |
| cust_mobile | varchar(20) | YES | NO | 客户手机号 |
| cust_age | int(11) | YES | NO | 客户年龄 |
| cust_gender | varchar(10) | YES | NO | 客户性别 |
| cust_province_code | varchar(20) | YES | NO | 客户常驻省份编码 |
| cust_province | varchar(100) | YES | NO | 客户常驻省份 |
| cust_city_code | varchar(20) | YES | NO | 客户常驻市编码 |
| cust_city | varchar(100) | YES | NO | 客户常驻市 |
| cust_district_code | varchar(20) | YES | NO | 客户常驻区编码 |
| cust_district | varchar(100) | YES | NO | 客户常驻区 |
| cust_highest_edu | varchar(100) | YES | NO | 客户最高学历 |
| cust_monthly_income | varchar(100) | YES | NO | 客户月收入 |
| cust_last_purchase_date | datetime | YES | NO | 客户最近一次购车时间 |
| cust_type | varchar(100) | YES | NO | 客户类型 |
| is_vehicle_owner | boolean | YES | NO | 是否车主 |
| update_time | datetime | YES | NO | 更新时间 |

#### 4.1.3 voc2_dealer_info_m_v (经销商信息视图)

**视图描述**: 经销商信息视图，提供经销商相关的汇总信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| dealer_id | varchar(100) | YES | YES | 经销商ID |
| dealer_code | varchar(50) | YES | NO | 经销商编码 |
| dealer_name | varchar(255) | YES | NO | 经销商全称 |
| dealer_province_code | varchar(20) | YES | NO | 经销商所属省份编码 |
| dealer_province | varchar(100) | YES | NO | 经销商所属省份 |
| dealer_city_code | varchar(20) | YES | NO | 经销商所在市编码 |
| dealer_city | varchar(100) | YES | NO | 经销商所在市 |
| dealer_regional_code | varchar(50) | YES | NO | 经销商所在大区编码 |
| dealer_regional | varchar(100) | YES | NO | 经销商所在大区 |
| dealer_type | varchar(100) | YES | NO | 经销商类型 |
| dealer_level | varchar(50) | YES | NO | 经销商等级 |
| update_time | datetime | YES | NO | 更新时间 |

#### 4.1.4 voc2_vehicle_info_m_v (车辆信息视图)

**视图描述**: 车辆信息视图，提供车辆相关的汇总信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| vehicle_id | varchar(100) | YES | YES | 车辆ID |
| vehicle_vin | varchar(100) | YES | NO | 车辆车架号 |
| brand_code | varchar(50) | YES | NO | 品牌编码 |
| brand | varchar(100) | YES | NO | 品牌名称 |
| vehicle_series_code | varchar(50) | YES | NO | 车系编码 |
| vehicle_series | varchar(100) | YES | NO | 车系名称 |
| vehicle_model_code | varchar(50) | YES | NO | 车型编码 |
| vehicle_model | varchar(100) | YES | NO | 车型名称 |
| vehicle_purchase_date | datetime | YES | NO | 车辆购买日期 |
| vehicle_production_date | datetime | YES | NO | 车辆生产日期 |
| vehicle_factory_release_date | datetime | YES | NO | 车辆出厂日期 |
| update_time | datetime | YES | NO | 更新时间 |

#### 4.1.5 voc2_dealership_data_info_m_v (经销商数据视图)

**视图描述**: 经销商数据视图，提供经销商相关的数据统计信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| dealer_id | varchar(100) | YES | YES | 经销商ID |
| dealer_code | varchar(50) | YES | NO | 经销商编码 |
| dealer_name | varchar(255) | YES | NO | 经销商名称 |
| data_type | varchar(100) | YES | NO | 数据类型 |
| data_value | decimal(10,2) | YES | NO | 数据值 |
| data_date | date | YES | NO | 数据日期 |
| update_time | datetime | YES | NO | 更新时间 |

#### 4.1.6 voc2_voc2_computed_result_all_data_v (计算结果数据视图)

**视图描述**: 计算结果数据视图，提供详细的计算结果信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| id | varchar(255) | YES | YES | 计算结果ID |
| data_id | varchar(255) | YES | NO | 数据唯一标识 |
| channel_code | varchar(50) | YES | NO | 渠道编码 |
| brand_code | varchar(50) | YES | NO | 品牌编码 |
| vehicle_series_code | varchar(50) | YES | NO | 车系编码 |
| sentiment | varchar(50) | YES | NO | 情感倾向 |
| data_create_time | datetime | YES | NO | 数据产生时间 |
| computed_time | datetime | YES | NO | 计算时间 |
| result_type | varchar(100) | YES | NO | 结果类型 |
| result_value | decimal(10,2) | YES | NO | 结果值 |
| result_status | varchar(50) | YES | NO | 结果状态 |

### 4.2 权限视图

#### 4.2.1 voc2_user_label_scope_perms_v (用户标签权限视图)

**视图描述**: 用户标签权限视图，提供用户对标签的权限控制信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| user_id | varchar(100) | YES | YES | 用户ID |
| label_code | varchar(100) | YES | NO | 标签编码 |
| label_name | varchar(255) | YES | NO | 标签名称 |
| permission_type | varchar(50) | YES | NO | 权限类型 |
| scope_level | varchar(50) | YES | NO | 权限范围级别 |
| is_active | boolean | YES | NO | 是否激活 |
| update_time | datetime | YES | NO | 更新时间 |

#### 4.2.2 voc2_ins_car_series_perms_v (车系权限视图)

**视图描述**: 车系权限视图，提供用户对车系的权限控制信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| user_id | varchar(100) | YES | YES | 用户ID |
| car_series_code | varchar(50) | YES | NO | 车系编码 |
| car_series_name | varchar(100) | YES | NO | 车系名称 |
| permission_type | varchar(50) | YES | NO | 权限类型 |
| is_active | boolean | YES | NO | 是否激活 |
| update_time | datetime | YES | NO | 更新时间 |

### 4.3 配置视图

#### 4.3.1 voc2_ins_car_series_info_m_v (车系配置视图)

**视图描述**: 车系配置视图，提供车系相关的配置信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| car_series_code | varchar(50) | YES | YES | 车系编码 |
| car_series_name | varchar(100) | YES | NO | 车系名称 |
| brand_code | varchar(50) | YES | NO | 品牌编码 |
| brand_name | varchar(100) | YES | NO | 品牌名称 |
| is_active | boolean | YES | NO | 是否激活 |
| sort_order | int(11) | YES | NO | 排序顺序 |
| update_time | datetime | YES | NO | 更新时间 |

#### 4.3.2 voc2_ins_channel_info_m_v (渠道配置视图)

**视图描述**: 渠道配置视图，提供渠道相关的配置信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| channel_code | varchar(50) | YES | YES | 渠道编码 |
| channel_name | varchar(100) | YES | NO | 渠道名称 |
| channel_type | varchar(50) | YES | NO | 渠道类型 |
| is_active | boolean | YES | NO | 是否激活 |
| sort_order | int(11) | YES | NO | 排序顺序 |
| update_time | datetime | YES | NO | 更新时间 |

#### 4.3.3 voc2_ins_project_car_series_info_v (项目车系视图)

**视图描述**: 项目车系视图，提供项目与车系的关联配置信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| project_id | varchar(100) | YES | YES | 项目ID |
| car_series_code | varchar(50) | YES | NO | 车系编码 |
| car_series_name | varchar(100) | YES | NO | 车系名称 |
| project_name | varchar(255) | YES | NO | 项目名称 |
| is_active | boolean | YES | NO | 是否激活 |
| update_time | datetime | YES | NO | 更新时间 |

#### 4.3.4 voc2_ins_province_area_m_v (省份区域视图)

**视图描述**: 省份区域视图，提供省份与区域的关联配置信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| province_code | varchar(20) | YES | YES | 省份编码 |
| province_name | varchar(100) | YES | NO | 省份名称 |
| area_code | varchar(50) | YES | NO | 区域编码 |
| area_name | varchar(100) | YES | NO | 区域名称 |
| is_active | boolean | YES | NO | 是否激活 |
| sort_order | int(11) | YES | NO | 排序顺序 |
| update_time | datetime | YES | NO | 更新时间 |

#### 4.3.5 voc2_ins_tags_info_m_v (标签配置视图)

**视图描述**: 标签配置视图，提供标签相关的配置信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| tag_code | varchar(100) | YES | YES | 标签编码 |
| tag_name | varchar(255) | YES | NO | 标签名称 |
| tag_category | varchar(50) | YES | NO | 标签分类 |
| tag_level | int(11) | YES | NO | 标签层级 |
| parent_code | varchar(100) | YES | NO | 父级标签编码 |
| is_active | boolean | YES | NO | 是否激活 |
| sort_order | int(11) | YES | NO | 排序顺序 |
| update_time | datetime | YES | NO | 更新时间 |

### 4.4 元数据视图

#### 4.4.1 voc2_raw_meta_data_range_m_v (原始数据元数据视图)

**视图描述**: 原始数据元数据视图，提供原始数据的元数据信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| data_source | varchar(100) | YES | YES | 数据源 |
| table_name | varchar(255) | YES | NO | 表名 |
| min_date | date | YES | NO | 最小日期 |
| max_date | date | YES | NO | 最大日期 |
| record_count | bigint | YES | NO | 记录数 |
| update_time | datetime | YES | NO | 更新时间 |

#### 4.4.2 voc2_sta_dict_v (统计字典视图)

**视图描述**: 统计字典视图，提供统计相关的字典信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| dict_code | varchar(100) | YES | YES | 字典编码 |
| dict_name | varchar(255) | YES | NO | 字典名称 |
| dict_type | varchar(50) | YES | NO | 字典类型 |
| dict_value | varchar(255) | YES | NO | 字典值 |
| is_active | boolean | YES | NO | 是否激活 |
| update_time | datetime | YES | NO | 更新时间 |

#### 4.4.3 voc2_sta_tag_level_values_v (标签层级统计视图)

**视图描述**: 标签层级统计视图，提供标签层级的统计信息。

**主要字段**:

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| tag_category | varchar(50) | YES | YES | 标签分类 |
| tag_level | int(11) | YES | NO | 标签层级 |
| tag_code | varchar(100) | YES | NO | 标签编码 |
| tag_name | varchar(255) | YES | NO | 标签名称 |
| usage_count | bigint | YES | NO | 使用次数 |
| update_time | datetime | YES | NO | 更新时间 |

## 5. 索引设计

### 5.1 主键索引
- 表: dws_voc2_sounds_data
- 字段: id
- 类型: 唯一索引

### 5.2 复合索引
- 表: dws_voc2_sounds_data
- 字段: (channel_code, data_create_time)
- 用途: 优化按渠道和时间查询

### 5.3 标签索引
- 表: dws_voc2_sounds_data
- 字段: 各标签字段
- 用途: 优化标签查询

## 6. 数据分布

### 6.1 渠道分布
| 渠道 | 数据量 | 占比 |
|------|--------|------|
| 小红书 | 27,818 | 12.6% |
| 知乎 | 27,640 | 12.5% |
| 微博 | 27,628 | 12.5% |
| 网站 | 27,618 | 12.5% |
| 汽车之家 | 27,612 | 12.5% |
| 贴吧 | 27,445 | 12.4% |
| 微信 | 27,372 | 12.4% |
| 抖音 | 27,367 | 12.4% |

### 6.2 情感分布
| 情感 | 数据量 | 占比 |
|------|--------|------|
| 负面 | 73,834 | 33.5% |
| 正面 | 73,452 | 33.3% |
| 中性 | 73,214 | 33.2% |

## 7. 数据质量指标

### 7.1 完整性指标
- 主键完整性: 100%
- 必填字段完整性: >95%
- 外键关联完整性: >90%

### 7.2 准确性指标
- 情感标注准确率: >85%
- 标签标注准确率: >80%
- 地理位置准确率: >90%

### 7.3 一致性指标
- 编码标准统一性: 100%
- 命名规范一致性: 100%
- 数据格式标准化: >95% 