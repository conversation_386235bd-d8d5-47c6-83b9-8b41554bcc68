# VOC数据库设计文档

## 概述

本目录包含了VOC（Voice of Customer）云平台的完整数据库设计文档，为数据库开发、维护和使用提供全面的技术参考。

## 文档结构

```
docs/database/
├── README.md                           # 本文档
├── StarRocks数据库设计文档.md            # StarRocks数据库设计概览
├── MySQL数据库设计文档.md              # MySQL数据库设计
├── 表结构设计文档.md                   # 详细表结构设计
├── 标签体系设计文档.md                 # 标签体系设计
├── 数据库ER图设计.md                   # 实体关系图设计
├── 数据库架构图.md                     # 数据库架构图
├── 权限体系设计文档.md                 # 权限体系设计
├── 数据流转图.md                       # 数据流转图
├── 数据字典.md                         # 数据字典
├── 数据库索引设计文档.md               # 索引设计
├── 视图层设计文档.md                   # 视图层设计
└── 报表/
    └── starrocks_voc_schema.sql        # StarRocks建表语句
```

## 文档说明

### 1. StarRocks数据库设计文档.md
- **内容**: 数据库整体架构、设计原则、分层结构
- **用途**: 了解数据库整体设计思路和架构
- **适用对象**: 架构师、项目经理、技术负责人

### 2. MySQL数据库设计文档.md
- **内容**: MySQL基础库和客户库的详细设计
- **用途**: MySQL数据库开发、维护和管理
- **适用对象**: 数据库开发人员、DBA、系统管理员

### 3. 表结构设计文档.md
- **内容**: 详细的表结构定义、字段说明、数据类型
- **用途**: 数据库开发、表结构维护
- **适用对象**: 数据库开发人员、后端开发人员

### 4. 标签体系设计文档.md
- **内容**: 8大类标签体系、4层级结构、应用场景
- **用途**: 标签系统开发、业务分析
- **适用对象**: 数据分析师、产品经理、开发人员

### 5. 数据库ER图设计.md
- **内容**: 实体关系图、关系定义、约束说明
- **用途**: 理解数据关系、数据库设计验证
- **适用对象**: 数据库设计师、系统分析师

### 6. 数据库架构图.md
- **内容**: 数据库架构图、系统架构图、部署架构图
- **用途**: 理解数据库架构、系统设计
- **适用对象**: 架构师、系统设计师、技术负责人

### 7. 权限体系设计文档.md
- **内容**: 权限管理架构、角色设计、权限控制
- **用途**: 权限系统开发、安全管理
- **适用对象**: 安全工程师、系统管理员、开发人员

### 8. 数据流转图.md
- **内容**: 数据流转架构、处理流程、监控体系
- **用途**: 数据流程设计、系统集成
- **适用对象**: 数据工程师、系统集成工程师、运维工程师

### 9. 数据字典.md
- **内容**: 字段详细说明、代码值定义、业务规则
- **用途**: 数据标准、开发规范
- **适用对象**: 所有开发人员、测试人员

### 10. 数据库索引设计文档.md
- **内容**: 索引策略、性能优化、监控维护
- **用途**: 数据库性能优化、运维管理
- **适用对象**: 数据库管理员、运维工程师

### 11. 视图层设计文档.md
- **内容**: 视图层架构、分类设计、应用场景
- **用途**: 视图开发、权限管理、配置管理
- **适用对象**: 前端开发人员、系统管理员、产品经理

### 12. starrocks_voc_schema.sql
- **内容**: StarRocks数据库建表语句
- **用途**: 数据库部署、环境搭建
- **适用对象**: 数据库管理员、运维工程师

## 数据库概览

### 基本信息
- **系统架构**: 双数据库架构（MySQL + StarRocks）
- **MySQL基础库**: vdp_ms_be（存储基础数据）
- **MySQL客户库**: vdp_ms_td（存储业务数据）
- **StarRocks仓库**: VDP_RS_TD（存储分析数据）
- **数据规模**: 约22万条声音数据记录
- **数据来源**: 8个主要渠道

### 数据库分布
- **vdp_ms_be**: 41个用户，5个品牌，基础配置数据
- **vdp_ms_td**: 2个项目，权限管理，统计分析数据
- **StarRocks**: 220,500条声音数据，8大类标签体系

### 核心表
- **dws_voc2_sounds_data**: 声音数据汇总表（核心事实表）
- **dim_voc2_cust_info**: 客户信息维度表
- **dim_voc2_dealer_info**: 经销商信息维度表
- **dim_voc2_vehicle_info**: 车辆信息维度表

### 标签体系
- **VTR标签**: 车辆技术相关（4层级）
- **COM标签**: 商品化属性（4层级）
- **ADB标签**: 全领域业务（4层级）
- **WOM标签**: 口碑评价指标（4层级）
- **CX标签**: 客户体验指标（4层级）
- **CJ标签**: 全旅程客户（4层级）
- **SL标签**: 销售线索（4层级）
- **OM标签**: 全媒体指标（4层级）

## 快速开始

### 1. 了解整体架构
首先阅读 [VOC数据库设计文档.md](StarRocks数据库设计文档.md)，了解数据库的整体架构和设计思路。

### 2. 了解MySQL数据库
阅读 [MySQL数据库设计文档.md](./MySQL数据库设计文档.md)，了解基础库和客户库的设计。

### 3. 查看表结构
阅读 [表结构设计文档.md](./表结构设计文档.md)，了解具体的表结构和字段定义。

### 4. 理解标签体系
阅读 [标签体系设计文档.md](./标签体系设计文档.md)，了解8大类标签的分类和应用场景。

### 5. 查看数据关系
阅读 [数据库ER图设计.md](./数据库ER图设计.md)，通过图形化方式理解数据实体间的关系。

### 6. 了解数据库架构
阅读 [数据库架构图.md](./数据库架构图.md)，了解数据库的整体架构设计。

### 7. 了解权限体系
阅读 [权限体系设计文档.md](./权限体系设计文档.md)，了解权限管理架构。

### 8. 了解数据流转
阅读 [数据流转图.md](./数据流转图.md)，了解数据流转架构和处理流程。

### 9. 参考数据字典
阅读 [数据字典.md](./数据字典.md)，了解字段的详细说明和代码值定义。

### 10. 了解视图层设计
阅读 [视图层设计文档.md](./视图层设计文档.md)，了解视图层的分类和应用场景。

### 11. 优化性能
阅读 [数据库索引设计文档.md](./数据库索引设计文档.md)，了解索引设计和性能优化策略。

### 12. 部署数据库
使用 [starrocks_voc_schema.sql](./报表/starrocks_voc_schema.sql) 创建数据库表结构。

## 数据流向

### 整体数据流向

```
数据源 → 数据采集 → 数据清洗 → 数据标注 → 数据聚合 → 数据服务
  ↓         ↓         ↓         ↓         ↓         ↓
原始数据 → 明细数据 → 汇总数据 → 维度数据 → 视图数据 → 应用接口
```

### 数据库间数据流向

```
基础库 (vdp_ms_be) → 数据同步 → 客户库 (vdp_ms_td) → 数据集成 → StarRocks
     ↓                    ↓              ↓              ↓
  用户数据             权限数据        业务数据      分析数据
  品牌数据             配置数据        标签数据      报表数据
  渠道数据             字典数据        项目数据      洞察数据
```

## 分层架构

### MySQL数据库架构

```
┌─────────────────────────────────────────────────────────────┐
│                    MySQL数据库架构                           │
├─────────────────────────────────────────────────────────────┤
│  vdp_ms_be (基础库)                    vdp_ms_td (客户库)    │
│  ├── 用户管理 (sys_users)              ├── 项目管理         │
│  ├── 客户信息 (ins_customer_info)      ├── 权限管理         │
│  ├── 品牌车型 (ins_brand_info)         ├── 数据源管理       │
│  ├── 渠道配置 (ins_channel)            ├── 标签管理         │
│  ├── 系统配置 (ins_dict)               ├── 统计分析         │
│  └── 基础字典 (ins_dict_item)          └── 业务数据         │
└─────────────────────────────────────────────────────────────┘
```

### StarRocks数据仓库架构

```
┌─────────────────────────────────────────────────────────────┐
│                        应用层                                │
├─────────────────────────────────────────────────────────────┤
│                        视图层                                │
│  业务视图:                                                  │
│  voc2_computed_result_all_data_m_v (计算结果汇总)           │
│  voc2_cust_info_m_v (客户信息)                              │
│  voc2_dealer_info_m_v (经销商信息)                          │
│  voc2_vehicle_info_m_v (车辆信息)                           │
│  voc2_dealership_data_info_m_v (经销商数据)                 │
│  voc2_voc2_computed_result_all_data_v (计算结果数据)        │
│                                                             │
│  权限视图:                                                  │
│  voc2_user_label_scope_perms_v (用户标签权限)               │
│  voc2_ins_car_series_perms_v (车系权限)                     │
│                                                             │
│  配置视图:                                                  │
│  voc2_ins_car_series_info_m_v (车系配置)                    │
│  voc2_ins_channel_info_m_v (渠道配置)                       │
│  voc2_ins_project_car_series_info_v (项目车系)              │
│  voc2_ins_province_area_m_v (省份区域)                      │
│  voc2_ins_tags_info_m_v (标签配置)                          │
│                                                             │
│  元数据视图:                                                │
│  voc2_raw_meta_data_range_m_v (原始数据元数据)              │
│  voc2_sta_dict_v (统计字典)                                 │
│  voc2_sta_tag_level_values_v (标签层级统计)                 │
├─────────────────────────────────────────────────────────────┤
│                        汇总层 (DWS)                          │
│  dws_voc2_sounds_data (核心事实表)                           │
│  dws_voc2_batch_push_record                                 │
│  dws_voc2_error_push_data                                   │
├─────────────────────────────────────────────────────────────┤
│                        维度层 (DIM)                          │
│  dim_voc2_cust_info (客户维度)                              │
│  dim_voc2_dealer_info (经销商维度)                          │
│  dim_voc2_vehicle_info (车辆维度)                           │
│  dim_voc2_product_info (产品维度)                           │
│  dim_voc2_big_v_user_info (大V用户维度)                     │
│  dim_voc2_manager_user_info (管理员维度)                    │
├─────────────────────────────────────────────────────────────┤
│                        明细层 (DWD)                          │
│  dwd_voc2_raw_public_opinion (公开意见)                     │
│  dwd_voc2_raw_private_opinion (私密意见)                    │
│  dwd_voc2_raw_public_consult (公开咨询)                     │
│  dwd_voc2_raw_private_consult (私密咨询)                    │
│  dwd_voc2_raw_public_posts_comment (公开帖子评论)           │
│  dwd_voc2_raw_private_posts_comment (私密帖子评论)          │
│  dwd_voc2_raw_public_questionnaire (公开问卷)               │
│  dwd_voc2_raw_private_questionnaire (私密问卷)              │
│  dwd_voc2_raw_private_work_order (私密工单)                 │
└─────────────────────────────────────────────────────────────┘
```

## 数据特征

### 数据分布
- **渠道分布**: 各渠道数据量相对均衡（约2.7万条/渠道）
- **情感分布**: 负面(33.5%)、正面(33.3%)、中性(33.2%)
- **品牌覆盖**: 长安、欧尚、凯程等品牌

### 数据质量
- **主键完整性**: 100%
- **必填字段完整性**: >95%
- **外键关联完整性**: >90%
- **情感标注准确率**: >85%
- **标签标注准确率**: >80%

## 业务价值

### 应用场景
- **情感分析**: 客户满意度监控
- **热点分析**: 产品问题识别
- **趋势分析**: 市场动态跟踪
- **客户画像**: 精准营销支持
- **产品优化**: 基于反馈的产品改进

### 支持决策
- **产品决策**: 基于客户反馈的产品改进
- **营销决策**: 精准营销和推广策略
- **服务决策**: 服务流程优化和改进
- **战略决策**: 市场趋势和竞争分析

## 维护指南

### 日常维护
- 每日检查数据质量指标
- 每周分析查询性能
- 每月优化索引结构

### 定期维护
- 每季度重建重要索引
- 每半年分析数据分布
- 每年评估架构设计

### 监控指标
- 数据量增长趋势
- 查询响应时间
- 系统资源使用率
- 数据质量指标

## 联系信息

如有问题或建议，请联系：
- **技术负责人**: [联系方式]
- **数据库管理员**: [联系方式]
- **产品经理**: [联系方式]

## 版本历史

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| v1.0 | 2024-01-01 | 初始版本 | [姓名] |
| v1.1 | 2024-01-15 | 增加索引设计 | [姓名] |
| v1.2 | 2024-02-01 | 完善标签体系 | [姓名] |

## 许可证

本文档仅供内部使用，请勿外传。 