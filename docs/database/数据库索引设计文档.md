# VOC数据库索引设计文档

## 1. 概述

### 1.1 设计目标
本文档描述了VOC数据库的索引设计策略，通过合理的索引设计提升查询性能，确保系统在高并发和大数据量场景下的稳定运行。

### 1.2 设计原则
- **性能优先**: 优先考虑查询性能提升
- **空间平衡**: 平衡索引空间占用和查询性能
- **维护成本**: 考虑索引维护成本
- **业务导向**: 基于实际业务查询需求设计

## 2. 索引策略

### 2.1 索引类型选择

#### 2.1.1 主键索引
- **类型**: 唯一索引
- **特点**: 自动创建，不可删除
- **作用**: 确保数据唯一性，提供快速定位

#### 2.1.2 唯一索引
- **类型**: 唯一约束索引
- **特点**: 确保字段值唯一
- **作用**: 防止重复数据，提升查询性能

#### 2.1.3 普通索引
- **类型**: B+树索引
- **特点**: 支持等值查询和范围查询
- **作用**: 提升查询性能

#### 2.1.4 复合索引
- **类型**: 多字段组合索引
- **特点**: 支持多字段查询
- **作用**: 优化复杂查询条件

#### 2.1.5 前缀索引
- **类型**: 字符串前缀索引
- **特点**: 减少索引空间占用
- **作用**: 优化长字符串字段查询

### 2.2 索引设计策略

#### 2.2.1 查询频率分析
- **高频查询**: 优先创建索引
- **中频查询**: 根据性能需求创建索引
- **低频查询**: 谨慎创建索引

#### 2.2.2 字段选择性分析
- **高选择性**: 字段值分布均匀，适合创建索引
- **低选择性**: 字段值分布集中，索引效果有限

#### 2.2.3 查询模式分析
- **等值查询**: 适合单字段索引
- **范围查询**: 适合复合索引
- **模糊查询**: 适合前缀索引

## 3. 核心表索引设计

### 3.1 dws_voc2_sounds_data (声音数据汇总表)

#### 3.1.1 主键索引
```sql
-- 主键索引 (自动创建)
PRIMARY KEY (id)
```

#### 3.1.2 唯一索引
```sql
-- 数据唯一标识索引
CREATE UNIQUE INDEX idx_data_id ON dws_voc2_sounds_data(data_id);
```

#### 3.1.3 单字段索引
```sql
-- 渠道编码索引
CREATE INDEX idx_channel_code ON dws_voc2_sounds_data(channel_code);

-- 品牌编码索引
CREATE INDEX idx_brand_code ON dws_voc2_sounds_data(brand_code);

-- 车系编码索引
CREATE INDEX idx_vehicle_series_code ON dws_voc2_sounds_data(vehicle_series_code);

-- 车型编码索引
CREATE INDEX idx_vehicle_model_code ON dws_voc2_sounds_data(vehicle_model_code);

-- 情感倾向索引
CREATE INDEX idx_sentiment ON dws_voc2_sounds_data(sentiment);

-- 数据创建时间索引
CREATE INDEX idx_data_create_time ON dws_voc2_sounds_data(data_create_time);

-- 客户ID索引
CREATE INDEX idx_one_id ON dws_voc2_sounds_data(one_id);

-- 经销商ID索引
CREATE INDEX idx_dealer_id ON dws_voc2_sounds_data(dealer_id);

-- 车辆VIN索引
CREATE INDEX idx_vehicle_vin ON dws_voc2_sounds_data(vehicle_vin);
```

#### 3.1.4 复合索引
```sql
-- 渠道+时间复合索引
CREATE INDEX idx_channel_time ON dws_voc2_sounds_data(channel_code, data_create_time);

-- 品牌+时间复合索引
CREATE INDEX idx_brand_time ON dws_voc2_sounds_data(brand_code, data_create_time);

-- 车系+时间复合索引
CREATE INDEX idx_series_time ON dws_voc2_sounds_data(vehicle_series_code, data_create_time);

-- 情感+时间复合索引
CREATE INDEX idx_sentiment_time ON dws_voc2_sounds_data(sentiment, data_create_time);

-- 客户+时间复合索引
CREATE INDEX idx_customer_time ON dws_voc2_sounds_data(one_id, data_create_time);

-- 经销商+时间复合索引
CREATE INDEX idx_dealer_time ON dws_voc2_sounds_data(dealer_id, data_create_time);

-- 品牌+车系+时间复合索引
CREATE INDEX idx_brand_series_time ON dws_voc2_sounds_data(brand_code, vehicle_series_code, data_create_time);

-- 渠道+品牌+时间复合索引
CREATE INDEX idx_channel_brand_time ON dws_voc2_sounds_data(channel_code, brand_code, data_create_time);

-- 情感+品牌+时间复合索引
CREATE INDEX idx_sentiment_brand_time ON dws_voc2_sounds_data(sentiment, brand_code, data_create_time);
```

#### 3.1.5 标签字段索引
```sql
-- VTR标签索引
CREATE INDEX idx_vtr_first ON dws_voc2_sounds_data(vtr_tag_first_code);
CREATE INDEX idx_vtr_second ON dws_voc2_sounds_data(vtr_tag_second_code);
CREATE INDEX idx_vtr_three ON dws_voc2_sounds_data(vtr_tag_three_code);

-- COM标签索引
CREATE INDEX idx_com_first ON dws_voc2_sounds_data(com_tag_first_code);
CREATE INDEX idx_com_second ON dws_voc2_sounds_data(com_tag_second_code);
CREATE INDEX idx_com_three ON dws_voc2_sounds_data(com_tag_three_code);

-- ADB标签索引
CREATE INDEX idx_adb_first ON dws_voc2_sounds_data(adb_tag_first_code);
CREATE INDEX idx_adb_second ON dws_voc2_sounds_data(adb_tag_second_code);
CREATE INDEX idx_adb_three ON dws_voc2_sounds_data(adb_tag_three_code);

-- WOM标签索引
CREATE INDEX idx_wom_first ON dws_voc2_sounds_data(wom_tag_first_code);
CREATE INDEX idx_wom_second ON dws_voc2_sounds_data(wom_tag_second_code);
CREATE INDEX idx_wom_three ON dws_voc2_sounds_data(wom_tag_three_code);

-- CX标签索引
CREATE INDEX idx_cx_first ON dws_voc2_sounds_data(cx_tag_first_code);
CREATE INDEX idx_cx_second ON dws_voc2_sounds_data(cx_tag_second_code);
CREATE INDEX idx_cx_three ON dws_voc2_sounds_data(cx_tag_three_code);

-- CJ标签索引
CREATE INDEX idx_cj_first ON dws_voc2_sounds_data(cj_tag_first_code);
CREATE INDEX idx_cj_second ON dws_voc2_sounds_data(cj_tag_second_code);
CREATE INDEX idx_cj_three ON dws_voc2_sounds_data(cj_tag_three_code);

-- SL标签索引
CREATE INDEX idx_sl_first ON dws_voc2_sounds_data(sl_tag_first_code);
CREATE INDEX idx_sl_second ON dws_voc2_sounds_data(sl_tag_second_code);
CREATE INDEX idx_sl_three ON dws_voc2_sounds_data(sl_tag_three_code);

-- OM标签索引
CREATE INDEX idx_om_first ON dws_voc2_sounds_data(om_tag_first_code);
CREATE INDEX idx_om_second ON dws_voc2_sounds_data(om_tag_second_code);
CREATE INDEX idx_om_three ON dws_voc2_sounds_data(om_tag_three_code);
```

#### 3.1.6 时间维度索引
```sql
-- 时间维度索引
CREATE INDEX idx_data_create_week ON dws_voc2_sounds_data(data_create_week);
CREATE INDEX idx_data_create_month ON dws_voc2_sounds_data(data_create_month);
CREATE INDEX idx_data_create_quarter ON dws_voc2_sounds_data(data_create_quarter);
CREATE INDEX idx_data_create_year ON dws_voc2_sounds_data(data_create_year);
```

### 3.2 dim_voc2_cust_info (客户信息维度表)

#### 3.2.1 主键索引
```sql
-- 主键索引 (自动创建)
PRIMARY KEY (oneid)
```

#### 3.2.2 单字段索引
```sql
-- 手机号索引
CREATE INDEX idx_mobile ON dim_voc2_cust_info(mobile);

-- 客户姓名索引
CREATE INDEX idx_cust_nm ON dim_voc2_cust_info(cust_nm);

-- 客户类型索引
CREATE INDEX idx_cust_type ON dim_voc2_cust_info(cust_type);

-- 性别索引
CREATE INDEX idx_gender ON dim_voc2_cust_info(gender);

-- 年龄索引
CREATE INDEX idx_age ON dim_voc2_cust_info(age);

-- 户籍省份索引
CREATE INDEX idx_hukou_prov_cd ON dim_voc2_cust_info(hukou_prov_cd);

-- 居住省份索引
CREATE INDEX idx_lived_prov_cd ON dim_voc2_cust_info(lived_prov_cd);

-- 居住城市索引
CREATE INDEX idx_lived_city_cd ON dim_voc2_cust_info(lived_city_cd);
```

#### 3.2.3 复合索引
```sql
-- 客户类型+性别复合索引
CREATE INDEX idx_cust_type_gender ON dim_voc2_cust_info(cust_type, gender);

-- 年龄+性别复合索引
CREATE INDEX idx_age_gender ON dim_voc2_cust_info(age, gender);

-- 居住省份+城市复合索引
CREATE INDEX idx_lived_prov_city ON dim_voc2_cust_info(lived_prov_cd, lived_city_cd);

-- 客户类型+居住省份复合索引
CREATE INDEX idx_cust_type_province ON dim_voc2_cust_info(cust_type, lived_prov_cd);
```

### 3.3 dim_voc2_dealer_info (经销商信息维度表)

#### 3.3.1 主键索引
```sql
-- 主键索引 (自动创建)
PRIMARY KEY (dealer_id)
```

#### 3.3.2 唯一索引
```sql
-- 经销商编码唯一索引
CREATE UNIQUE INDEX idx_dealer_code ON dim_voc2_dealer_info(dealer_code);
```

#### 3.3.3 单字段索引
```sql
-- 经销商名称索引
CREATE INDEX idx_dealer_name ON dim_voc2_dealer_info(dealer_name);

-- 经销商类型索引
CREATE INDEX idx_dealer_type ON dim_voc2_dealer_info(dealer_type);

-- 经销商等级索引
CREATE INDEX idx_dealer_level ON dim_voc2_dealer_info(dealer_level);

-- 状态索引
CREATE INDEX idx_status ON dim_voc2_dealer_info(status);

-- 省份编码索引
CREATE INDEX idx_province_code ON dim_voc2_dealer_info(province_code);

-- 城市编码索引
CREATE INDEX idx_city_code ON dim_voc2_dealer_info(city_code);

-- 区域编码索引
CREATE INDEX idx_region_code ON dim_voc2_dealer_info(region_code);

-- 一级经销商编码索引
CREATE INDEX idx_dealer_lv1_code ON dim_voc2_dealer_info(dealer_lv1_code);

-- 二级经销商编码索引
CREATE INDEX idx_dealer_lv2_code ON dim_voc2_dealer_info(dealer_lv2_code);

-- 三级经销商编码索引
CREATE INDEX idx_dealer_lv3_code ON dim_voc2_dealer_info(dealer_lv3_code);
```

#### 3.3.4 复合索引
```sql
-- 省份+城市复合索引
CREATE INDEX idx_province_city ON dim_voc2_dealer_info(province_code, city_code);

-- 经销商类型+等级复合索引
CREATE INDEX idx_dealer_type_level ON dim_voc2_dealer_info(dealer_type, dealer_level);

-- 区域+省份复合索引
CREATE INDEX idx_region_province ON dim_voc2_dealer_info(region_code, province_code);

-- 状态+类型复合索引
CREATE INDEX idx_status_type ON dim_voc2_dealer_info(status, dealer_type);
```

### 3.4 dim_voc2_vehicle_info (车辆信息维度表)

#### 3.4.1 主键索引
```sql
-- 主键索引 (自动创建)
PRIMARY KEY (vehicle_id, create_date)
```

#### 3.4.2 唯一索引
```sql
-- 车架号唯一索引
CREATE UNIQUE INDEX idx_vin ON dim_voc2_vehicle_info(vin);
```

#### 3.4.3 单字段索引
```sql
-- 物料编码索引
CREATE INDEX idx_material_code ON dim_voc2_vehicle_info(material_code);

-- 品牌编码索引
CREATE INDEX idx_brand_code ON dim_voc2_vehicle_info(brand_code);

-- 车系编码索引
CREATE INDEX idx_series_code ON dim_voc2_vehicle_info(series_code);

-- 车型编码索引
CREATE INDEX idx_model_code ON dim_voc2_vehicle_info(model_code);

-- 配置编码索引
CREATE INDEX idx_opt_code ON dim_voc2_vehicle_info(opt_code);

-- 颜色编码索引
CREATE INDEX idx_color_code ON dim_voc2_vehicle_info(color_code);

-- 经销商ID索引
CREATE INDEX idx_dealer_id ON dim_voc2_vehicle_info(dealer_id);

-- 生命周期索引
CREATE INDEX idx_life_cycle ON dim_voc2_vehicle_info(life_cycle);

-- 锁定状态索引
CREATE INDEX idx_lock_status ON dim_voc2_vehicle_info(lock_status);

-- 车牌号索引
CREATE INDEX idx_license_no ON dim_voc2_vehicle_info(license_no);

-- 发动机号索引
CREATE INDEX idx_engine_no ON dim_voc2_vehicle_info(engine_no);

-- 购买日期索引
CREATE INDEX idx_purchased_date ON dim_voc2_vehicle_info(purchased_date);

-- 生产日期索引
CREATE INDEX idx_product_date ON dim_voc2_vehicle_info(product_date);

-- 出厂日期索引
CREATE INDEX idx_factory_date ON dim_voc2_vehicle_info(factory_date);
```

#### 3.4.4 复合索引
```sql
-- 品牌+车系复合索引
CREATE INDEX idx_brand_series ON dim_voc2_vehicle_info(brand_code, series_code);

-- 车系+车型复合索引
CREATE INDEX idx_series_model ON dim_voc2_vehicle_info(series_code, model_code);

-- 经销商+品牌复合索引
CREATE INDEX idx_dealer_brand ON dim_voc2_vehicle_info(dealer_id, brand_code);

-- 生命周期+状态复合索引
CREATE INDEX idx_life_cycle_status ON dim_voc2_vehicle_info(life_cycle, lock_status);

-- 品牌+车系+车型复合索引
CREATE INDEX idx_brand_series_model ON dim_voc2_vehicle_info(brand_code, series_code, model_code);
```

## 4. 索引优化策略

### 4.1 索引选择优化

#### 4.1.1 查询分析
```sql
-- 分析查询执行计划
EXPLAIN SELECT * FROM dws_voc2_sounds_data 
WHERE channel_code = 'WEIBO' 
AND data_create_time >= '2024-01-01' 
AND data_create_time < '2024-02-01';
```

#### 4.1.2 索引使用分析
```sql
-- 查看索引使用情况
SHOW INDEX FROM dws_voc2_sounds_data;

-- 查看索引统计信息
SHOW INDEX_STATISTICS FROM dws_voc2_sounds_data;
```

### 4.2 索引维护策略

#### 4.2.1 定期重建索引
```sql
-- 重建索引
ALTER TABLE dws_voc2_sounds_data REBUILD INDEX idx_channel_time;

-- 优化表
OPTIMIZE TABLE dws_voc2_sounds_data;
```

#### 4.2.2 索引碎片整理
```sql
-- 分析索引碎片
ANALYZE TABLE dws_voc2_sounds_data;

-- 整理索引碎片
REPAIR TABLE dws_voc2_sounds_data;
```

### 4.3 索引监控

#### 4.3.1 索引使用率监控
```sql
-- 查询索引使用率
SELECT 
    table_name,
    index_name,
    cardinality,
    sub_part,
    packed,
    null,
    index_type,
    comment
FROM information_schema.statistics 
WHERE table_schema = 'VDP_RS_TD' 
AND table_name = 'dws_voc2_sounds_data';
```

#### 4.3.2 慢查询分析
```sql
-- 开启慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- 查看慢查询日志
SHOW VARIABLES LIKE 'slow_query_log%';
```

## 5. 性能优化建议

### 5.1 查询优化

#### 5.1.1 避免全表扫描
- 确保WHERE条件使用索引字段
- 避免在索引字段上使用函数
- 避免使用!=、<>、NOT IN等操作符

#### 5.1.2 优化JOIN查询
- 使用小表驱动大表
- 确保JOIN字段有索引
- 避免多表JOIN

#### 5.1.3 优化ORDER BY
- 确保ORDER BY字段有索引
- 避免使用SELECT *
- 使用LIMIT限制结果集

### 5.2 索引优化

#### 5.2.1 索引覆盖
- 创建包含查询字段的复合索引
- 避免回表查询
- 使用EXPLAIN分析查询计划

#### 5.2.2 索引选择性
- 优先为高选择性字段创建索引
- 避免为低选择性字段创建索引
- 定期分析索引使用情况

#### 5.2.3 索引数量控制
- 控制单表索引数量
- 避免重复索引
- 定期清理无用索引

### 5.3 分区优化

#### 5.3.1 分区策略
- 按时间分区优化查询性能
- 使用分区裁剪减少扫描范围
- 定期维护分区

#### 5.3.2 分区索引
- 为分区表创建本地索引
- 避免全局索引
- 优化分区键选择

## 6. 监控和维护

### 6.1 性能监控

#### 6.1.1 查询性能监控
- 监控慢查询数量
- 监控查询响应时间
- 监控索引使用率

#### 6.1.2 系统性能监控
- 监控CPU使用率
- 监控内存使用率
- 监控磁盘I/O

### 6.2 维护计划

#### 6.2.1 日常维护
- 每日检查索引使用情况
- 每周分析慢查询日志
- 每月优化索引结构

#### 6.2.2 定期维护
- 每季度重建重要索引
- 每半年分析索引效果
- 每年评估索引策略

### 6.3 应急预案

#### 6.3.1 性能问题处理
- 快速识别性能瓶颈
- 临时优化措施
- 长期解决方案

#### 6.3.2 索引故障处理
- 索引损坏修复
- 索引重建恢复
- 数据一致性检查 

## 4. 视图层索引设计

### 4.1 业务视图索引

#### 4.1.1 voc2_computed_result_all_data_m_v (计算结果汇总视图)

```sql
-- 主键索引
PRIMARY KEY (id)

-- 数据ID索引
CREATE INDEX idx_data_id ON voc2_computed_result_all_data_m_v(data_id);

-- 渠道编码索引
CREATE INDEX idx_channel_code ON voc2_computed_result_all_data_m_v(channel_code);

-- 品牌编码索引
CREATE INDEX idx_brand_code ON voc2_computed_result_all_data_m_v(brand_code);

-- 车系编码索引
CREATE INDEX idx_vehicle_series_code ON voc2_computed_result_all_data_m_v(vehicle_series_code);

-- 情感倾向索引
CREATE INDEX idx_sentiment ON voc2_computed_result_all_data_m_v(sentiment);

-- 数据创建时间索引
CREATE INDEX idx_data_create_time ON voc2_computed_result_all_data_m_v(data_create_time);

-- 计算时间索引
CREATE INDEX idx_computed_time ON voc2_computed_result_all_data_m_v(computed_time);

-- 结果类型索引
CREATE INDEX idx_result_type ON voc2_computed_result_all_data_m_v(result_type);

-- 复合索引：渠道+时间
CREATE INDEX idx_channel_time ON voc2_computed_result_all_data_m_v(channel_code, data_create_time);

-- 复合索引：品牌+时间
CREATE INDEX idx_brand_time ON voc2_computed_result_all_data_m_v(brand_code, data_create_time);

-- 复合索引：车系+时间
CREATE INDEX idx_series_time ON voc2_computed_result_all_data_m_v(vehicle_series_code, data_create_time);
```

#### 4.1.2 voc2_cust_info_m_v (客户信息视图)

```sql
-- 主键索引
PRIMARY KEY (one_id)

-- 客户姓名索引
CREATE INDEX idx_cust_name ON voc2_cust_info_m_v(cust_name);

-- 客户手机号索引
CREATE INDEX idx_cust_mobile ON voc2_cust_info_m_v(cust_mobile);

-- 客户年龄索引
CREATE INDEX idx_cust_age ON voc2_cust_info_m_v(cust_age);

-- 客户性别索引
CREATE INDEX idx_cust_gender ON voc2_cust_info_m_v(cust_gender);

-- 客户省份索引
CREATE INDEX idx_cust_province ON voc2_cust_info_m_v(cust_province);

-- 客户城市索引
CREATE INDEX idx_cust_city ON voc2_cust_info_m_v(cust_city);

-- 客户类型索引
CREATE INDEX idx_cust_type ON voc2_cust_info_m_v(cust_type);

-- 是否车主索引
CREATE INDEX idx_is_vehicle_owner ON voc2_cust_info_m_v(is_vehicle_owner);

-- 更新时间索引
CREATE INDEX idx_update_time ON voc2_cust_info_m_v(update_time);

-- 复合索引：省份+城市
CREATE INDEX idx_province_city ON voc2_cust_info_m_v(cust_province, cust_city);

-- 复合索引：性别+年龄
CREATE INDEX idx_gender_age ON voc2_cust_info_m_v(cust_gender, cust_age);
```

#### 4.1.3 voc2_dealer_info_m_v (经销商信息视图)

```sql
-- 主键索引
PRIMARY KEY (dealer_id)

-- 经销商编码索引
CREATE INDEX idx_dealer_code ON voc2_dealer_info_m_v(dealer_code);

-- 经销商名称索引
CREATE INDEX idx_dealer_name ON voc2_dealer_info_m_v(dealer_name);

-- 经销商省份索引
CREATE INDEX idx_dealer_province ON voc2_dealer_info_m_v(dealer_province);

-- 经销商城市索引
CREATE INDEX idx_dealer_city ON voc2_dealer_info_m_v(dealer_city);

-- 经销商大区索引
CREATE INDEX idx_dealer_regional ON voc2_dealer_info_m_v(dealer_regional);

-- 经销商类型索引
CREATE INDEX idx_dealer_type ON voc2_dealer_info_m_v(dealer_type);

-- 经销商等级索引
CREATE INDEX idx_dealer_level ON voc2_dealer_info_m_v(dealer_level);

-- 更新时间索引
CREATE INDEX idx_update_time ON voc2_dealer_info_m_v(update_time);

-- 复合索引：省份+城市
CREATE INDEX idx_province_city ON voc2_dealer_info_m_v(dealer_province, dealer_city);

-- 复合索引：大区+省份
CREATE INDEX idx_regional_province ON voc2_dealer_info_m_v(dealer_regional, dealer_province);
```

#### 4.1.4 voc2_vehicle_info_m_v (车辆信息视图)

```sql
-- 主键索引
PRIMARY KEY (vehicle_id)

-- 车辆车架号索引
CREATE INDEX idx_vehicle_vin ON voc2_vehicle_info_m_v(vehicle_vin);

-- 品牌编码索引
CREATE INDEX idx_brand_code ON voc2_vehicle_info_m_v(brand_code);

-- 品牌名称索引
CREATE INDEX idx_brand ON voc2_vehicle_info_m_v(brand);

-- 车系编码索引
CREATE INDEX idx_vehicle_series_code ON voc2_vehicle_info_m_v(vehicle_series_code);

-- 车系名称索引
CREATE INDEX idx_vehicle_series ON voc2_vehicle_info_m_v(vehicle_series);

-- 车型编码索引
CREATE INDEX idx_vehicle_model_code ON voc2_vehicle_info_m_v(vehicle_model_code);

-- 车型名称索引
CREATE INDEX idx_vehicle_model ON voc2_vehicle_info_m_v(vehicle_model);

-- 购买日期索引
CREATE INDEX idx_purchase_date ON voc2_vehicle_info_m_v(vehicle_purchase_date);

-- 生产日期索引
CREATE INDEX idx_production_date ON voc2_vehicle_info_m_v(vehicle_production_date);

-- 出厂日期索引
CREATE INDEX idx_factory_date ON voc2_vehicle_info_m_v(vehicle_factory_release_date);

-- 更新时间索引
CREATE INDEX idx_update_time ON voc2_vehicle_info_m_v(update_time);

-- 复合索引：品牌+车系
CREATE INDEX idx_brand_series ON voc2_vehicle_info_m_v(brand_code, vehicle_series_code);

-- 复合索引：车系+车型
CREATE INDEX idx_series_model ON voc2_vehicle_info_m_v(vehicle_series_code, vehicle_model_code);
```

### 4.2 权限视图索引

#### 4.2.1 voc2_user_label_scope_perms_v (用户标签权限视图)

```sql
-- 主键索引
PRIMARY KEY (user_id)

-- 标签编码索引
CREATE INDEX idx_label_code ON voc2_user_label_scope_perms_v(label_code);

-- 标签名称索引
CREATE INDEX idx_label_name ON voc2_user_label_scope_perms_v(label_name);

-- 权限类型索引
CREATE INDEX idx_permission_type ON voc2_user_label_scope_perms_v(permission_type);

-- 权限范围级别索引
CREATE INDEX idx_scope_level ON voc2_user_label_scope_perms_v(scope_level);

-- 是否激活索引
CREATE INDEX idx_is_active ON voc2_user_label_scope_perms_v(is_active);

-- 更新时间索引
CREATE INDEX idx_update_time ON voc2_user_label_scope_perms_v(update_time);

-- 复合索引：用户+标签
CREATE INDEX idx_user_label ON voc2_user_label_scope_perms_v(user_id, label_code);

-- 复合索引：用户+权限类型
CREATE INDEX idx_user_permission ON voc2_user_label_scope_perms_v(user_id, permission_type);
```

#### 4.2.2 voc2_ins_car_series_perms_v (车系权限视图)

```sql
-- 主键索引
PRIMARY KEY (user_id)

-- 车系编码索引
CREATE INDEX idx_car_series_code ON voc2_ins_car_series_perms_v(car_series_code);

-- 车系名称索引
CREATE INDEX idx_car_series_name ON voc2_ins_car_series_perms_v(car_series_name);

-- 权限类型索引
CREATE INDEX idx_permission_type ON voc2_ins_car_series_perms_v(permission_type);

-- 是否激活索引
CREATE INDEX idx_is_active ON voc2_ins_car_series_perms_v(is_active);

-- 更新时间索引
CREATE INDEX idx_update_time ON voc2_ins_car_series_perms_v(update_time);

-- 复合索引：用户+车系
CREATE INDEX idx_user_series ON voc2_ins_car_series_perms_v(user_id, car_series_code);

-- 复合索引：用户+权限类型
CREATE INDEX idx_user_permission ON voc2_ins_car_series_perms_v(user_id, permission_type);
```

### 4.3 配置视图索引

#### 4.3.1 voc2_ins_car_series_info_m_v (车系配置视图)

```sql
-- 主键索引
PRIMARY KEY (car_series_code)

-- 车系名称索引
CREATE INDEX idx_car_series_name ON voc2_ins_car_series_info_m_v(car_series_name);

-- 品牌编码索引
CREATE INDEX idx_brand_code ON voc2_ins_car_series_info_m_v(brand_code);

-- 品牌名称索引
CREATE INDEX idx_brand_name ON voc2_ins_car_series_info_m_v(brand_name);

-- 是否激活索引
CREATE INDEX idx_is_active ON voc2_ins_car_series_info_m_v(is_active);

-- 排序顺序索引
CREATE INDEX idx_sort_order ON voc2_ins_car_series_info_m_v(sort_order);

-- 更新时间索引
CREATE INDEX idx_update_time ON voc2_ins_car_series_info_m_v(update_time);

-- 复合索引：品牌+车系
CREATE INDEX idx_brand_series ON voc2_ins_car_series_info_m_v(brand_code, car_series_code);

-- 复合索引：激活状态+排序
CREATE INDEX idx_active_sort ON voc2_ins_car_series_info_m_v(is_active, sort_order);
```

#### 4.3.2 voc2_ins_channel_info_m_v (渠道配置视图)

```sql
-- 主键索引
PRIMARY KEY (channel_code)

-- 渠道名称索引
CREATE INDEX idx_channel_name ON voc2_ins_channel_info_m_v(channel_name);

-- 渠道类型索引
CREATE INDEX idx_channel_type ON voc2_ins_channel_info_m_v(channel_type);

-- 是否激活索引
CREATE INDEX idx_is_active ON voc2_ins_channel_info_m_v(is_active);

-- 排序顺序索引
CREATE INDEX idx_sort_order ON voc2_ins_channel_info_m_v(sort_order);

-- 更新时间索引
CREATE INDEX idx_update_time ON voc2_ins_channel_info_m_v(update_time);

-- 复合索引：渠道类型+激活状态
CREATE INDEX idx_type_active ON voc2_ins_channel_info_m_v(channel_type, is_active);

-- 复合索引：激活状态+排序
CREATE INDEX idx_active_sort ON voc2_ins_channel_info_m_v(is_active, sort_order);
```

#### 4.3.3 voc2_ins_tags_info_m_v (标签配置视图)

```sql
-- 主键索引
PRIMARY KEY (tag_code)

-- 标签名称索引
CREATE INDEX idx_tag_name ON voc2_ins_tags_info_m_v(tag_name);

-- 标签分类索引
CREATE INDEX idx_tag_category ON voc2_ins_tags_info_m_v(tag_category);

-- 标签层级索引
CREATE INDEX idx_tag_level ON voc2_ins_tags_info_m_v(tag_level);

-- 父级标签编码索引
CREATE INDEX idx_parent_code ON voc2_ins_tags_info_m_v(parent_code);

-- 是否激活索引
CREATE INDEX idx_is_active ON voc2_ins_tags_info_m_v(is_active);

-- 排序顺序索引
CREATE INDEX idx_sort_order ON voc2_ins_tags_info_m_v(sort_order);

-- 更新时间索引
CREATE INDEX idx_update_time ON voc2_ins_tags_info_m_v(update_time);

-- 复合索引：分类+层级
CREATE INDEX idx_category_level ON voc2_ins_tags_info_m_v(tag_category, tag_level);

-- 复合索引：父级+层级
CREATE INDEX idx_parent_level ON voc2_ins_tags_info_m_v(parent_code, tag_level);

-- 复合索引：激活状态+排序
CREATE INDEX idx_active_sort ON voc2_ins_tags_info_m_v(is_active, sort_order);
```

### 4.4 元数据视图索引

#### 4.4.1 voc2_raw_meta_data_range_m_v (原始数据元数据视图)

```sql
-- 主键索引
PRIMARY KEY (data_source)

-- 表名索引
CREATE INDEX idx_table_name ON voc2_raw_meta_data_range_m_v(table_name);

-- 最小日期索引
CREATE INDEX idx_min_date ON voc2_raw_meta_data_range_m_v(min_date);

-- 最大日期索引
CREATE INDEX idx_max_date ON voc2_raw_meta_data_range_m_v(max_date);

-- 记录数索引
CREATE INDEX idx_record_count ON voc2_raw_meta_data_range_m_v(record_count);

-- 更新时间索引
CREATE INDEX idx_update_time ON voc2_raw_meta_data_range_m_v(update_time);

-- 复合索引：数据源+表名
CREATE INDEX idx_source_table ON voc2_raw_meta_data_range_m_v(data_source, table_name);

-- 复合索引：最小日期+最大日期
CREATE INDEX idx_date_range ON voc2_raw_meta_data_range_m_v(min_date, max_date);
```

#### 4.4.2 voc2_sta_dict_v (统计字典视图)

```sql
-- 主键索引
PRIMARY KEY (dict_code)

-- 字典名称索引
CREATE INDEX idx_dict_name ON voc2_sta_dict_v(dict_name);

-- 字典类型索引
CREATE INDEX idx_dict_type ON voc2_sta_dict_v(dict_type);

-- 字典值索引
CREATE INDEX idx_dict_value ON voc2_sta_dict_v(dict_value);

-- 是否激活索引
CREATE INDEX idx_is_active ON voc2_sta_dict_v(is_active);

-- 更新时间索引
CREATE INDEX idx_update_time ON voc2_sta_dict_v(update_time);

-- 复合索引：字典类型+激活状态
CREATE INDEX idx_type_active ON voc2_sta_dict_v(dict_type, is_active);

-- 复合索引：字典类型+字典值
CREATE INDEX idx_type_value ON voc2_sta_dict_v(dict_type, dict_value);
```

#### 4.4.3 voc2_sta_tag_level_values_v (标签层级统计视图)

```sql
-- 主键索引
PRIMARY KEY (tag_category)

-- 标签层级索引
CREATE INDEX idx_tag_level ON voc2_sta_tag_level_values_v(tag_level);

-- 标签编码索引
CREATE INDEX idx_tag_code ON voc2_sta_tag_level_values_v(tag_code);

-- 标签名称索引
CREATE INDEX idx_tag_name ON voc2_sta_tag_level_values_v(tag_name);

-- 使用次数索引
CREATE INDEX idx_usage_count ON voc2_sta_tag_level_values_v(usage_count);

-- 更新时间索引
CREATE INDEX idx_update_time ON voc2_sta_tag_level_values_v(update_time);

-- 复合索引：分类+层级
CREATE INDEX idx_category_level ON voc2_sta_tag_level_values_v(tag_category, tag_level);

-- 复合索引：分类+使用次数
CREATE INDEX idx_category_usage ON voc2_sta_tag_level_values_v(tag_category, usage_count);

-- 复合索引：层级+使用次数
CREATE INDEX idx_level_usage ON voc2_sta_tag_level_values_v(tag_level, usage_count);
``` 