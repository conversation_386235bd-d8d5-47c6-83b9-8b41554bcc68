# VOC数据库视图层设计文档

## 1. 概述

### 1.1 设计目标
视图层是VOC数据库架构中的最上层，为业务应用提供统一的数据访问接口。通过视图层，可以简化复杂的数据查询，提供业务友好的数据格式，并实现数据权限控制。

### 1.2 设计原则
- **业务导向**: 视图设计基于实际业务需求
- **性能优化**: 通过预计算和索引优化查询性能
- **权限控制**: 实现基于角色的数据访问控制
- **数据一致性**: 确保视图数据与底层数据的一致性

## 2. 视图层架构

### 2.1 视图分类

#### 2.1.1 业务视图
- **数据汇总视图**: 提供业务分析所需的数据汇总
- **维度视图**: 提供各维度的标准化数据
- **标签视图**: 提供标签体系的标准化数据

#### 2.1.2 权限视图
- **用户权限视图**: 基于用户角色的数据访问控制
- **项目权限视图**: 基于项目的数据访问控制
- **区域权限视图**: 基于地理区域的数据访问控制

#### 2.1.3 配置视图
- **字典视图**: 提供系统配置和字典数据
- **元数据视图**: 提供数据元信息
- **统计视图**: 提供数据统计信息

### 2.2 视图命名规范
- **业务视图**: `voc2_[业务模块]_[功能]_m_v`
- **权限视图**: `voc2_[权限类型]_[范围]_v`
- **配置视图**: `voc2_[配置类型]_[功能]_v`

## 3. 核心业务视图

### 3.1 voc2_computed_result_all_data_m_v (计算结果汇总视图)

**视图描述**: VOC平台的核心业务视图，提供所有计算结果的汇总数据，是业务分析的主要数据源。

**主要特征**:
- 数据量: 约22万条记录
- 分区策略: 按时间分区（季度）
- 主键: (data_create_time, brand_code, car_series_code)
- 数据来源: 基于dws_voc2_sounds_data表

#### 3.1.1 基础信息字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| data_create_time | date | NO | YES | 数据产生时间 |
| brand_code | varchar(1000) | YES | YES | 品牌编码 |
| car_series_code | varchar(1000) | YES | YES | 车系编码 |
| id | varchar(40) | NO | NO | 声音ID |
| data_id | varchar(40) | NO | NO | 数据唯一标识 |
| channel_code | varchar(40) | NO | NO | 渠道编码 |
| brand_name | varchar(100) | YES | NO | 品牌名称 |
| car_series_name | varchar(100) | YES | NO | 车系名称 |
| content_type | varchar(10) | YES | NO | 内容类型 |
| sentiment | varchar(100) | YES | NO | 情感倾向 |
| intention | varchar(100) | YES | NO | 意图分类 |
| create_date | date | YES | NO | 创建日期 |
| is_outer | varchar(1048576) | YES | NO | 是否外部数据 |

#### 3.1.2 内容分析字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| hot_word | varchar(1048576) | YES | NO | 热词提取 |
| keywords | varchar(65533) | YES | NO | 关键词 |
| scenario | varchar(65533) | YES | NO | 场景分析 |
| topic | varchar(65533) | YES | NO | 主题分类 |

#### 3.1.3 客户维度字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| one_id | varchar(64) | YES | NO | 客户唯一标识 |
| cust_age | varchar(65533) | YES | NO | 客户年龄 |
| cust_gender | varchar(65533) | YES | NO | 客户性别 |
| cust_province_code | varchar(60) | YES | NO | 客户省份编码 |
| cust_province | varchar(60) | YES | NO | 客户省份 |
| cust_city_code | varchar(60) | YES | NO | 客户城市编码 |
| cust_city | varchar(60) | YES | NO | 客户城市 |
| cust_name | varchar(65533) | YES | NO | 客户姓名 |
| cust_mobile | varchar(65533) | YES | NO | 客户手机号 |
| cust_type | varchar(65533) | YES | NO | 客户类型 |
| is_car_owner | bigint | YES | NO | 是否车主 |

#### 3.1.4 车辆维度字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| get_json_string(ext_fields, '$.vin') | varchar(1048576) | YES | NO | 车辆VIN号 |
| vhl_id | bigint | YES | NO | 车辆ID |
| vhl_purchase_date | varchar(65533) | YES | NO | 车辆购买日期 |
| vhl_production_date | varchar(65533) | YES | NO | 车辆生产日期 |
| vhl_factory_felease_date | varchar(65533) | YES | NO | 车辆出厂日期 |
| vhl_series_code | varchar(65533) | YES | NO | 车辆车系编码 |
| vhl_series | varchar(65533) | YES | NO | 车辆车系 |
| vhl_model_code | varchar(65533) | YES | NO | 车辆车型编码 |
| vhl_model | varchar(65533) | YES | NO | 车辆车型 |
| vhl_drivetrain_type | varchar(65533) | YES | NO | 驱动类型 |
| vhl_manufacturer_type | varchar(65533) | YES | NO | 制造商类型 |
| vhl_energy_type_level1 | varchar(65533) | YES | NO | 能源类型一级 |
| vhl_energy_type_level2 | varchar(65533) | YES | NO | 能源类型二级 |
| vhl_claim_tactics_id | varchar(65533) | YES | NO | 索赔策略ID |

#### 3.1.5 经销商维度字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| dlr_id | varchar(1048576) | YES | NO | 经销商ID |
| dlr_code | varchar(65533) | YES | NO | 经销商编码 |
| dlr_name | varchar(65533) | YES | NO | 经销商名称 |
| dlr_shortname | varchar(65533) | YES | NO | 经销商简称 |
| dlr_big_area_code | varchar(65533) | YES | NO | 大区编码 |
| dlr_big_area | varchar(65533) | YES | NO | 大区名称 |
| dlr_province_code | varchar(60) | YES | NO | 省份编码 |
| dlr_province | varchar(1048576) | YES | NO | 省份名称 |
| dlr_city_code | varchar(60) | YES | NO | 城市编码 |
| dlr_city | varchar(1048576) | YES | NO | 城市名称 |
| dlr_status | varchar(65533) | YES | NO | 经销商状态 |

#### 3.1.6 内容特征字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| is_wsater_army | varchar(1048576) | YES | NO | 是否水军 |
| is_manager_focused | varchar(1048576) | YES | NO | 是否管理层关注 |
| is_big_v | varchar(1048576) | YES | NO | 是否大V |
| author_id | varchar(1048576) | YES | NO | 作者ID |
| author_nick | varchar(1048576) | YES | NO | 作者昵称 |
| is_main_post | varchar(1048576) | YES | NO | 是否主贴 |
| title | varchar(1048576) | YES | NO | 标题 |
| content | varchar(1048576) | YES | NO | 内容 |
| original_link | varchar(1048576) | YES | NO | 原文链接 |

#### 3.1.7 互动数据字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| view_count | varchar(1048576) | YES | NO | 浏览量 |
| comment_count | varchar(1048576) | YES | NO | 评论数 |
| like_count | varchar(1048576) | YES | NO | 点赞数 |
| share_count | varchar(1048576) | YES | NO | 分享数 |
| favorite_count | varchar(1048576) | YES | NO | 收藏数 |

#### 3.1.8 业务字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| work_order_id | varchar(1048576) | YES | NO | 工单ID |
| quest_type | varchar(1048576) | YES | NO | 问卷类型 |
| quest_answer_score | varchar(1048576) | YES | NO | 问卷答案分数 |
| quest_business_type | varchar(1048576) | YES | NO | 问卷业务类型 |
| quest_business_scenario | varchar(1048576) | YES | NO | 问卷业务场景 |

#### 3.1.9 标签字段

**VTR标签**:
- vtr_tag_first_code/vtr_tag_first: 一级标签编码/名称
- vtr_tag_second_code/vtr_tag_second: 二级标签编码/名称
- vtr_tag_three_code/vtr_tag_three: 三级标签编码/名称
- vtr_tag_four_code/vtr_tag_four: 四级标签编码/名称

**COM标签**:
- com_tag_first_code/com_tag_first: 一级标签编码/名称
- com_tag_second_code/com_tag_second: 二级标签编码/名称
- com_tag_three_code/com_tag_three: 三级标签编码/名称
- com_tag_four_code/com_tag_four: 四级标签编码/名称

**ADB标签**:
- adb_tag_first_code/adb_tag_first: 一级标签编码/名称
- adb_tag_second_code/adb_tag_second: 二级标签编码/名称
- adb_tag_three_code/adb_tag_three: 三级标签编码/名称
- adb_tag_four_code/adb_tag_four: 四级标签编码/名称

**CJ标签**:
- cj_tag_first_code/cj_tag_first: 一级标签编码/名称
- cj_tag_second_code/cj_tag_second: 二级标签编码/名称
- cj_tag_three_code/cj_tag_three: 三级标签编码/名称
- cj_tag_four_code/cj_tag_four: 四级标签编码/名称

**NPS标签**:
- nps_tag_first_code/nps_tag_first: 一级标签编码/名称
- nps_tag_second_code/nps_tag_second: 二级标签编码/名称
- nps_tag_three_code/nps_tag_three: 三级标签编码/名称
- nps_tag_four_code/nps_tag_four: 四级标签编码/名称

#### 3.1.10 时间维度字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| data_create_year | varchar(1048576) | YES | NO | 数据产生年份 |
| data_create_quarter | varchar(1048576) | YES | NO | 数据产生季度 |
| data_create_month | varchar(1048576) | YES | NO | 数据产生月份 |
| data_create_week | varchar(1048576) | YES | NO | 数据产生周 |

### 3.2 voc2_cust_info_m_v (客户信息视图)

**视图描述**: 客户信息汇总视图，提供客户的基本信息、人口统计学特征、购买历史等数据。

**主要特征**:
- 数据量: 约10万条客户记录
- 主键: (oneid, id_card_no, mobile)
- 数据来源: 基于dim_voc2_cust_info表

#### 3.2.1 基础信息字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| oneid | varchar(65533) | NO | YES | 客户唯一标识 |
| id_card_no | varchar(65533) | YES | YES | 身份证号码 |
| mobile | varchar(65533) | YES | YES | 手机号 |
| cust_classify | varchar(65533) | YES | NO | 客户分类 |
| id_card_type | varchar(65533) | YES | NO | 身份证类型 |
| global_id | varchar(65533) | YES | NO | 全局ID |
| email | varchar(65533) | YES | NO | 邮箱 |
| cust_nm | varchar(65533) | YES | NO | 客户姓名 |

#### 3.2.2 人口统计学字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| gender | varchar(65533) | YES | NO | 性别 |
| age | varchar(65533) | YES | NO | 年龄 |
| age_group | varchar(65533) | YES | NO | 年龄段 |
| birthday_dt | varchar(65533) | YES | NO | 生日日期 |
| birthday | varchar(65533) | YES | NO | 生日 |
| born_years | varchar(65533) | YES | NO | 出生年份 |
| life_stage | varchar(65533) | YES | NO | 人生阶段 |
| constellation | varchar(65533) | YES | NO | 星座 |
| zodiac | varchar(65533) | YES | NO | 生肖 |
| high_educaion | varchar(65533) | YES | NO | 最高学历 |
| marriage_statue | varchar(65533) | YES | NO | 婚姻状况 |

#### 3.2.3 地理位置字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| hukou_prov_cd | varchar(65533) | YES | NO | 户籍省份编码 |
| hukou_prov_nm | varchar(65533) | YES | NO | 户籍省份名称 |
| hukou_city_cd | varchar(65533) | YES | NO | 户籍城市编码 |
| hukou_city_nm | varchar(65533) | YES | NO | 户籍城市名称 |
| hukou_cty_cd | varchar(65533) | YES | NO | 户籍区县编码 |
| hukou_cty_nm | varchar(65533) | YES | NO | 户籍区县名称 |
| lived_prov_cd | varchar(60) | YES | NO | 居住省份编码 |
| lived_prov_nm | varchar(60) | YES | NO | 居住省份名称 |
| lived_city_cd | varchar(60) | YES | NO | 居住城市编码 |
| lived_city_nm | varchar(60) | YES | NO | 居住城市名称 |
| lived_cty_cd | varchar(65533) | YES | NO | 居住区县编码 |
| lived_cty_nm | varchar(65533) | YES | NO | 居住区县名称 |
| lived_addr | varchar(65533) | YES | NO | 居住地址 |

#### 3.2.4 社会经济字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| profession | varchar(65533) | YES | NO | 职业 |
| family_income | varchar(65533) | YES | NO | 家庭收入 |
| cust_type | varchar(65533) | YES | NO | 客户类型 |

#### 3.2.5 购车历史字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| is_exchange_flg | bigint | YES | NO | 是否置换标志 |
| is_re_purchase_flg | bigint | YES | NO | 是否复购标志 |
| is_recommend_flg | bigint | YES | NO | 是否推荐标志 |
| is_car_owner_flg | bigint | YES | NO | 是否车主标志 |
| is_deal_flg | bigint | YES | NO | 是否成交标志 |
| is_uni_owner_flg | bigint | YES | NO | 是否长安车主标志 |
| is_jc_owner_flg | bigint | YES | NO | 是否轿车车主标志 |
| is_wc_owner_flg | bigint | YES | NO | 是否微车车主标志 |
| is_ev_owner_flg | bigint | YES | NO | 是否电动车车主标志 |
| is_qxc_owner_flg | bigint | YES | NO | 是否轻卡车主标志 |
| purchase_car_qty | bigint | YES | NO | 购车数量 |
| purchase_car_times | bigint | YES | NO | 购车次数 |
| lately_purchase_time | varchar(65533) | YES | NO | 最近购车时间 |
| his_consume_amt | decimal(32,8) | YES | NO | 历史消费金额 |

#### 3.2.6 会员信息字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| is_member_flg | bigint | YES | NO | 是否会员标志 |
| member_register_mth | varchar(65533) | YES | NO | 会员注册月份 |
| mem_activity | varchar(65533) | YES | NO | 会员活跃度 |
| is_birthday_1day_flg | bigint | YES | NO | 是否生日当天标志 |
| is_birthday_30day_flg | bigint | YES | NO | 是否生日30天内标志 |
| is_birthday_60day_flg | bigint | YES | NO | 是否生日60天内标志 |

#### 3.2.7 系统字段

| 字段名 | 数据类型 | 是否为空 | 主键 | 说明 |
|--------|----------|----------|------|------|
| dw_insert_time | varchar(65533) | YES | NO | 数据仓库插入时间 |
| dw_update_time | varchar(65533) | YES | NO | 数据仓库更新时间 |
| batch_dt | varchar(65533) | YES | NO | 批次日期 |
| job_nm | varchar(65533) | YES | NO | 作业名称 |

### 3.3 voc2_dealer_info_m_v (经销商信息视图)

**视图描述**: 经销商信息汇总视图，提供经销商的详细信息、层级结构、地理位置等数据。

**主要特征**:
- 数据量: 约1万条经销商记录
- 主键: dealer_id
- 数据来源: 基于dim_voc2_dealer_info表

### 3.4 voc2_vehicle_info_m_v (车辆信息视图)

**视图描述**: 车辆信息汇总视图，提供车辆的详细信息、配置信息、生产信息等数据。

**主要特征**:
- 数据量: 约50万条车辆记录
- 主键: (vehicle_id, create_date)
- 数据来源: 基于dim_voc2_vehicle_info表

## 4. 权限控制视图

### 4.1 voc2_user_label_scope_perms_v (用户标签权限视图)

**视图描述**: 用户标签权限控制视图，基于用户角色控制标签数据的访问权限。

**主要功能**:
- 用户权限验证
- 标签范围控制
- 数据访问限制

### 4.2 voc2_ins_car_series_perms_v (车系权限视图)

**视图描述**: 车系权限控制视图，基于用户权限控制车系数据的访问。

**主要功能**:
- 车系权限验证
- 数据范围控制
- 访问权限管理

## 5. 配置管理视图

### 5.1 voc2_ins_car_series_info_m_v (车系信息配置视图)

**视图描述**: 车系信息配置视图，提供车系的基础配置信息。

**主要功能**:
- 车系基础信息
- 配置参数管理
- 系统配置支持

### 5.2 voc2_ins_channel_info_m_v (渠道信息配置视图)

**视图描述**: 渠道信息配置视图，提供数据渠道的配置信息。

**主要功能**:
- 渠道基础信息
- 渠道配置参数
- 数据源配置

### 5.3 voc2_ins_project_car_series_info_v (项目车系信息视图)

**视图描述**: 项目车系信息视图，提供项目与车系的关联配置。

**主要功能**:
- 项目车系关联
- 配置参数管理
- 项目支持

### 5.4 voc2_ins_province_area_m_v (省份区域配置视图)

**视图描述**: 省份区域配置视图，提供地理区域的配置信息。

**主要功能**:
- 地理区域配置
- 区域层级管理
- 地理位置支持

### 5.5 voc2_ins_tags_info_m_v (标签信息配置视图)

**视图描述**: 标签信息配置视图，提供标签体系的配置信息。

**主要功能**:
- 标签体系配置
- 标签层级管理
- 标签规则配置

## 6. 元数据视图

### 6.1 voc2_raw_meta_data_range_m_v (原始数据元数据视图)

**视图描述**: 原始数据元数据视图，提供数据源和元数据信息。

**主要功能**:
- 数据源信息
- 元数据管理
- 数据质量监控

### 6.2 voc2_sta_dict_v (统计字典视图)

**视图描述**: 统计字典视图，提供统计相关的字典数据。

**主要功能**:
- 统计指标定义
- 字典数据管理
- 统计规则配置

### 6.3 voc2_sta_tag_level_values_v (标签层级值统计视图)

**视图描述**: 标签层级值统计视图，提供标签各层级的统计信息。

**主要功能**:
- 标签层级统计
- 标签值分布
- 统计数据分析

## 7. 业务视图

### 7.1 voc2_dealership_data_info_m_v (经销商数据信息视图)

**视图描述**: 经销商数据信息视图，提供经销商相关的业务数据。

**主要功能**:
- 经销商业务数据
- 销售数据统计
- 业务分析支持

### 7.2 voc2_voc2_computed_result_all_data_v (计算结果数据视图)

**视图描述**: 计算结果数据视图，提供计算结果的详细数据。

**主要功能**:
- 计算结果展示
- 数据分析支持
- 业务决策支持

## 8. 视图优化策略

### 8.1 性能优化

#### 8.1.1 索引优化
- 为视图的常用查询字段创建索引
- 优化复合索引的顺序
- 定期分析索引使用情况

#### 8.1.2 分区优化
- 按时间分区优化查询性能
- 使用分区裁剪减少扫描范围
- 定期维护分区

#### 8.1.3 缓存优化
- 对热点数据进行缓存
- 使用Redis缓存查询结果
- 实现多级缓存策略

### 8.2 数据一致性

#### 8.2.1 实时同步
- 确保视图数据与底层数据的一致性
- 实现增量更新机制
- 监控数据同步状态

#### 8.2.2 数据质量
- 定期检查数据质量
- 实现数据质量监控
- 建立数据修复机制

### 8.3 权限控制

#### 8.3.1 行级权限
- 基于用户角色的行级权限控制
- 实现数据隔离
- 支持动态权限调整

#### 8.3.2 列级权限
- 基于用户角色的列级权限控制
- 敏感数据脱敏
- 支持字段级权限管理

## 9. 监控和维护

### 9.1 性能监控

#### 9.1.1 查询性能
- 监控视图查询响应时间
- 分析慢查询日志
- 优化查询性能

#### 9.1.2 系统资源
- 监控CPU使用率
- 监控内存使用率
- 监控磁盘I/O

### 9.2 数据监控

#### 9.2.1 数据质量
- 监控数据完整性
- 监控数据准确性
- 监控数据一致性

#### 9.2.2 数据量监控
- 监控数据量增长趋势
- 监控数据分布情况
- 预测数据增长

### 9.3 维护计划

#### 9.3.1 日常维护
- 每日检查视图状态
- 每周分析性能指标
- 每月优化视图结构

#### 9.3.2 定期维护
- 每季度重建重要视图
- 每半年分析视图效果
- 每年评估视图策略

## 10. 应用场景

### 10.1 业务分析
- 客户行为分析
- 产品满意度分析
- 市场趋势分析

### 10.2 决策支持
- 产品优化决策
- 营销策略制定
- 服务改进决策

### 10.3 运营管理
- 客户关系管理
- 经销商管理
- 数据质量管理 