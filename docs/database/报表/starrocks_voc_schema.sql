-- VDP_RS_TD.dws_voc2_sounds_data definition

CREATE TABLE `dws_voc2_sounds_data` (
                              `id` varchar(255) NULL COMMENT "声音ID",
                              `data_id` varchar(255) NULL COMMENT "数据唯一标识",
                              `channel_code` varchar(50) NULL COMMENT "渠道编码",
                              `channel` varchar(100) NULL COMMENT "渠道名称",
                              `brand_code` varchar(50) NULL COMMENT "品牌编码",
                              `brand` varchar(100) NULL COMMENT "品牌名称",
                              `vehicle_series_code` varchar(50) NULL COMMENT "车系编码",
                              `vehicle_series` varchar(100) NULL COMMENT "车系名称",
                              `vehicle_model_code` varchar(50) NULL COMMENT "车型编码",
                              `vehicle_model` varchar(100) NULL COMMENT "车型名称",
                              `label_type` varchar(100) NULL COMMENT "数据类型",
                              `sentiment` varchar(50) NULL COMMENT "情感",
                              `intention` varchar(100) NULL COMMENT "意图",
                              `hot_word` TEXT NULL COMMENT "热词",
                              `user_journey` TEXT NULL COMMENT "用户旅程",
                              `keywords` TEXT NULL COMMENT "关键词",
                              `data_create_time` datetime NULL COMMENT "数据产生时间",
                              `create_time` datetime NULL COMMENT "数据抓取时间",
                              `dealer_id` varchar(100) NULL COMMENT "经销商ID",
                              `dealer_code` varchar(50) NULL COMMENT "经销商编码",
                              `dealer_name` varchar(255) NULL COMMENT "经销商全称",
                              `dealer_province_code` varchar(20) NULL COMMENT "经销商所属省份编码",
                              `dealer_province` varchar(100) NULL COMMENT "经销商所属省份",
                              `dealer_city_code` varchar(20) NULL COMMENT "经销商所在市编码",
                              `dealer_city` varchar(100) NULL COMMENT "经销商所在市",
                              `vehicle_purchase_date` datetime NULL COMMENT "车辆购买日期",
                              `vehicle_production_date` datetime NULL COMMENT "车辆生产日期",
                              `vehicle_factory_release_date` datetime NULL COMMENT "车辆出厂日期",
                              `vehicle_vin` varchar(100) NULL COMMENT "车辆车架号",
                              `one_id` varchar(100) NULL COMMENT "oneId",
                              `cust_name` varchar(100) NULL COMMENT "客户姓名",
                              `cust_mobile` varchar(20) NULL COMMENT "客户手机号",
                              `cust_age` int(11) NULL COMMENT "客户年龄",
                              `cust_gender` varchar(10) NULL COMMENT "客户性别",
                              `cust_province_code` varchar(20) NULL COMMENT "客户常驻省份编码",
                              `cust_province` varchar(100) NULL COMMENT "客户常驻省份",
                              `cust_city_code` varchar(20) NULL COMMENT "客户常驻市编码",
                              `cust_city` varchar(100) NULL COMMENT "客户常驻市",
                              `cust_district_code` varchar(20) NULL COMMENT "客户常驻区编码",
                              `cust_district` varchar(100) NULL COMMENT "客户常驻区",
                              `cust_highest_edu` varchar(100) NULL COMMENT "客户最高学历",
                              `cust_monthly_income` varchar(100) NULL COMMENT "客户月收入",
                              `cust_last_purchase_date` datetime NULL COMMENT "客户最近一次购车时间",
                              `cust_type` varchar(100) NULL COMMENT "客户类型",
                              `is_vehicle_owner` boolean NULL COMMENT "是否车主",
                              `content_type` varchar(100) NULL COMMENT "原文内容类型",
                              `work_order_id` varchar(100) NULL COMMENT "工单ID",
                              `is_main_post` boolean NULL COMMENT "是否主贴",
                              `post_title` TEXT NULL COMMENT "帖子标题",
                              `post_original_link` TEXT NULL COMMENT "帖子原文链接",
                              `post_original_content` TEXT NULL COMMENT "帖子原文详情",
                              `quest_type` varchar(100) NULL COMMENT "问卷类型",
                              `quest_question_content` TEXT NULL COMMENT "问卷题目/内容",
                              `quest_answer_score` int(11) NULL COMMENT "问卷答案分数",
                              `quest_business_type` varchar(100) NULL COMMENT "问卷业务类型",
                              `quest_business_scenario` varchar(100) NULL COMMENT "问卷业务场景",
                              `topic` TEXT NULL COMMENT "观点",
                              `vtr_tag_first_code` varchar(50) NULL COMMENT "VRT标签编码1级",
                              `vtr_tag_second_code` varchar(50) NULL COMMENT "VRT标签编码2级",
                              `vtr_tag_three_code` varchar(50) NULL COMMENT "VRT标签编码3级",
                              `vtr_tag_first` varchar(100) NULL COMMENT "VRT标签1级",
                              `vtr_tag_second` varchar(100) NULL COMMENT "VRT标签2级",
                              `vtr_tag_three` varchar(100) NULL COMMENT "VRT标签3级",
                              `com_tag_first_code` varchar(50) NULL COMMENT "商品化属性标签编码1级",
                              `com_tag_second_code` varchar(50) NULL COMMENT "商品化属性标签编码2级",
                              `com_tag_three_code` varchar(50) NULL COMMENT "商品化属性标签编码3级",
                              `com_tag_first` varchar(100) NULL COMMENT "商品化属性标签1级",
                              `com_tag_second` varchar(100) NULL COMMENT "商品化属性标签2级",
                              `com_tag_three` varchar(100) NULL COMMENT "商品化属性标签3级",
                              `adb_tag_first_code` varchar(50) NULL COMMENT "全领域业务标签编码1级",
                              `adb_tag_second_code` varchar(50) NULL COMMENT "全领域业务标签编码2级",
                              `adb_tag_three_code` varchar(50) NULL COMMENT "全领域业务标签编码3级",
                              `adb_tag_first` varchar(100) NULL COMMENT "全领域业务标签1级",
                              `adb_tag_second` varchar(100) NULL COMMENT "全领域业务标签2级",
                              `adb_tag_three` varchar(100) NULL COMMENT "全领域业务标签3级",
                              `wom_tag_first_code` varchar(50) NULL COMMENT "口碑评价指标编码1级",
                              `wom_tag_second_code` varchar(50) NULL COMMENT "口碑评价指标编码2级",
                              `wom_tag_three_code` varchar(50) NULL COMMENT "口碑评价指标编码3级",
                              `wom_tag_first` varchar(100) NULL COMMENT "口碑评价指标1级",
                              `wom_tag_second` varchar(100) NULL COMMENT "口碑评价指标2级",
                              `wom_tag_three` varchar(100) NULL COMMENT "口碑评价指标3级",
                              `cx_tag_first_code` varchar(50) NULL COMMENT "客户体验指标编码1级",
                              `cx_tag_second_code` varchar(50) NULL COMMENT "客户体验指标编码2级",
                              `cx_tag_three_code` varchar(50) NULL COMMENT "客户体验指标编码3级",
                              `cx_tag_first` varchar(100) NULL COMMENT "客户体验指标1级",
                              `cx_tag_second` varchar(100) NULL COMMENT "客户体验指标2级",
                              `cx_tag_three` varchar(100) NULL COMMENT "客户体验指标3级",
                              `cj_tag_first_code` varchar(50) NULL COMMENT "全旅程客户签编码1级",
                              `cj_tag_second_code` varchar(50) NULL COMMENT "全旅程客户签编码2级",
                              `cj_tag_three_code` varchar(50) NULL COMMENT "全旅程客户签编码3级",
                              `cj_tag_first` varchar(100) NULL COMMENT "全旅程客户签1级",
                              `cj_tag_second` varchar(100) NULL COMMENT "全旅程客户签2级",
                              `cj_tag_three` varchar(100) NULL COMMENT "全旅程客户签3级",
                              `sl_tag_first_code` varchar(50) NULL COMMENT "销售线索编码1级",
                              `sl_tag_second_code` varchar(50) NULL COMMENT "销售线索编码2级",
                              `sl_tag_three_code` varchar(50) NULL COMMENT "销售线索编码3级",
                              `sl_tag_first` varchar(100) NULL COMMENT "销售线索1级",
                              `sl_tag_second` varchar(100) NULL COMMENT "销售线索2级",
                              `sl_tag_three` varchar(100) NULL COMMENT "销售线索3级",
                              `om_tag_first_code` varchar(50) NULL COMMENT "全媒体指标编码1级",
                              `om_tag_second_code` varchar(50) NULL COMMENT "全媒体指标编码2级",
                              `om_tag_three_code` varchar(50) NULL COMMENT "全媒体指标编码3级",
                              `om_tag_first` varchar(100) NULL COMMENT "全媒体指标1级",
                              `om_tag_second` varchar(100) NULL COMMENT "全媒体指标2级",
                              `om_tag_three` varchar(100) NULL COMMENT "全媒体指标3级",
                              `data_create_week` varchar(20) NULL COMMENT "数据产生周期-周",
                              `data_create_month` varchar(20) NULL COMMENT "数据产生周期-月",
                              `data_create_quarter` varchar(20) NULL COMMENT "数据产生周期-季",
                              `data_create_year` varchar(20) NULL COMMENT "数据产生周期-年",
                              `dealer_regional_code` varchar(50) NULL COMMENT "经销商所在大区编码",
                              `dealer_regional` varchar(100) NULL COMMENT "经销商所在大区"
) ENGINE=OLAP
    DUPLICATE KEY(`id`)
COMMENT "voc所有声音数据宽表"
PARTITION BY RANGE(`data_create_time`)
(PARTITION p2023_Q1 VALUES [("0000-01-01 00:00:00"), ("2023-04-01 00:00:00")),
PARTITION p2023_Q2 VALUES [("2023-04-01 00:00:00"), ("2023-07-01 00:00:00")),
PARTITION p2023_Q3 VALUES [("2023-07-01 00:00:00"), ("2023-10-01 00:00:00")),
PARTITION p2023_Q4 VALUES [("2023-10-01 00:00:00"), ("2024-01-01 00:00:00")),
PARTITION p2024_Q1 VALUES [("2024-01-01 00:00:00"), ("2024-04-01 00:00:00")),
PARTITION p2024_Q2 VALUES [("2024-04-01 00:00:00"), ("2024-07-01 00:00:00")),
PARTITION p2024_Q3 VALUES [("2024-07-01 00:00:00"), ("2024-10-01 00:00:00")),
PARTITION p2024_Q4 VALUES [("2024-10-01 00:00:00"), ("2025-01-01 00:00:00")),
PARTITION p2025_Q1 VALUES [("2025-01-01 00:00:00"), ("2025-04-01 00:00:00")),
PARTITION p2025_Q2 VALUES [("2025-04-01 00:00:00"), ("2025-07-01 00:00:00")),
PARTITION p2025_Q3 VALUES [("2025-07-01 00:00:00"), ("2025-10-01 00:00:00")),
PARTITION p2025_Q4 VALUES [("2025-10-01 00:00:00"), ("2026-01-01 00:00:00")),
PARTITION p2026_Q1 VALUES [("2026-01-01 00:00:00"), ("2026-04-01 00:00:00")),
PARTITION p2026_Q2 VALUES [("2026-04-01 00:00:00"), ("2026-07-01 00:00:00")),
PARTITION p2026_Q3 VALUES [("2026-07-01 00:00:00"), ("2026-10-01 00:00:00")),
PARTITION p2026_Q4 VALUES [("2026-10-01 00:00:00"), ("2027-01-01 00:00:00")))
DISTRIBUTED BY HASH(`id`) BUCKETS 10
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

ALTER TABLE voc_overview RENAME dws_voc2_sounds_data;

-- 修改表结构并添加备注说明
ALTER TABLE dws_voc2_sounds_data
    COMMENT='voc声音数据表';

alter table dws_voc2_sounds_data
    modify dealer_province_code varchar(20) null comment '经销商所属省份编码';

alter table dws_voc2_sounds_data
    modify dealer_province varchar(100) null comment '经销商所属省份';

ALTER TABLE VDP_RS_TD.dws_voc2_sounds_data MODIFY COLUMN dealer_province_code varchar(20) NULL COMMENT '经销商所属省份编码';
ALTER TABLE VDP_RS_TD.dws_voc2_sounds_data MODIFY COLUMN dealer_province varchar(20) NULL COMMENT '经销商所属省份';


ALTER TABLE VDP_RS_TD.dws_voc2_sounds_data ADD COLUMN dealer_regional_code VARCHAR(50) COMMENT '经销商所在大区编码';
ALTER TABLE VDP_RS_TD.dws_voc2_sounds_data ADD COLUMN dealer_regional VARCHAR(100) COMMENT '经销商所在大区' ;

