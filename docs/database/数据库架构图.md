# VOC Cloud 数据库架构图设计文档

## 1. 整体架构图

### 1.1 系统架构概览

```mermaid
graph TB
    subgraph "应用层"
        A1[报表服务]
        A2[分析服务]
        A3[洞察服务]
        A4[AI工作流服务]
    end
    
    subgraph "服务层"
        B1[数据集成服务]
        B2[模型服务]
        B3[权限服务]
        B4[配置服务]
    end
    
    subgraph "数据层"
        C1[vdp_ms_be<br/>基础库]
        C2[vdp_ms_td<br/>客户库]
        C3[StarRocks<br/>数据仓库]
        C4[Redis<br/>缓存]
        C5[Elasticsearch<br/>搜索引擎]
    end
    
    subgraph "数据源"
        D1[CRM系统]
        D2[社交媒体]
        D3[客服系统]
        D4[问卷调研]
        D5[其他数据源]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
    
    B1 --> C1
    B1 --> C2
    B1 --> C3
    B2 --> C3
    B3 --> C1
    B3 --> C2
    B4 --> C1
    B4 --> C2
    
    C1 --> C4
    C2 --> C4
    C3 --> C4
    
    B1 --> C5
    
    D1 --> B1
    D2 --> B1
    D3 --> B1
    D4 --> B1
    D5 --> B1
```

### 1.2 数据流向架构

```mermaid
flowchart LR
    subgraph "数据采集"
        E1[API接口]
        E2[文件上传]
        E3[数据库同步]
        E4[消息队列]
    end
    
    subgraph "数据处理"
        F1[数据清洗]
        F2[数据转换]
        F3[数据验证]
        F4[AI分析]
    end
    
    subgraph "数据存储"
        G1[MySQL基础库]
        G2[MySQL客户库]
        G3[StarRocks仓库]
        G4[Redis缓存]
    end
    
    subgraph "数据服务"
        H1[实时查询]
        H2[批量分析]
        H3[报表生成]
        H4[数据导出]
    end
    
    E1 --> F1
    E2 --> F1
    E3 --> F1
    E4 --> F1
    
    F1 --> F2
    F2 --> F3
    F3 --> F4
    
    F4 --> G1
    F4 --> G2
    F4 --> G3
    F4 --> G4
    
    G1 --> H1
    G2 --> H1
    G3 --> H2
    G3 --> H3
    G4 --> H1
    G4 --> H4
```

## 2. MySQL数据库架构

### 2.1 基础库架构 (vdp_ms_be)

```mermaid
erDiagram
    %% 用户管理模块
    sys_users ||--o{ sys_credentials : "用户凭证"
    sys_users ||--o{ sys_login_histroy : "登录历史"
    sys_users ||--o{ sys_users_change_record : "变更记录"
    sys_credentials ||--o{ sys_credentials_change_record : "凭证变更"
    
    %% 客户管理模块
    ins_customer_info ||--o{ ins_customer_permission : "客户权限"
    
    %% 品牌车型模块
    ins_brand_info ||--o{ ins_car_series_info : "车系信息"
    ins_car_series_info ||--o{ ins_model_info : "车型信息"
    ins_model_info ||--o{ ins_model_desc : "车型描述"
    
    %% 渠道配置模块
    ins_channel ||--o{ ins_channel_distribution : "渠道分布"
    
    %% 系统配置模块
    ins_dict ||--o{ ins_dict_item : "字典项"
    ins_data_source ||--o{ ins_data_source_desc : "数据源描述"
    ins_data_resource ||--o{ ins_data_resource_desc : "资源描述"
    
    %% 实体定义
    sys_users {
        varchar id PK
        varchar username UK
        varchar phone
        varchar firstname
        varchar lastname
        varchar email
        varchar operator
        int non_locked
        int enabled
        datetime expire_date
        varchar client_id FK
        varchar employee_id
        varchar position
        text remark
        datetime create_time
        datetime update_time
    }
    
    ins_customer_info {
        varchar id PK
        varchar code
        varchar full_name UK
        varchar abbreviation
        varchar province
        varchar city
        varchar contacts
        varchar phone
        varchar email
        varchar address
        int status
        int del_flag
        int sort
        text remark
        datetime create_time
        datetime update_time
    }
    
    ins_brand_info {
        varchar id PK
        varchar code PK
        varchar name
        varchar name_en
        text alias
        text exclusion_words
        int order_by
        text img
        varchar app_id
        varchar operator
        tinyint del_flag
        datetime create_time
        datetime update_time
    }
```

### 2.2 客户库架构 (vdp_ms_td)

```mermaid
erDiagram
    %% 项目管理模块
    ins_project_info ||--o{ ins_project_details : "项目详情"
    
    %% 权限管理模块
    ins_role ||--o{ ins_user_role : "用户角色"
    ins_role ||--o{ ins_role_relation_permission : "角色权限"
    ins_menu_permission ||--o{ ins_role_relation_permission : "权限关联"
    ins_menu_permission ||--o{ ins_menu_permission : "菜单层级"
    
    %% 统计分析模块
    sta_sys_role ||--o{ sta_sys_user_role : "用户角色"
    sta_sys_role ||--o{ sta_sys_role_area : "区域权限"
    sta_sys_role ||--o{ sta_sys_role_business_tag : "业务标签"
    sta_sys_role ||--o{ sta_sys_role_channel : "渠道权限"
    sta_sys_role ||--o{ sta_sys_role_permission : "功能权限"
    sta_sys_role ||--o{ sta_sys_role_quality_tag : "质量标签"
    sta_sys_role ||--o{ sta_sys_role_series : "车系权限"
    sta_sys_depart ||--o{ sta_sys_user_depart : "用户部门"
    sta_sys_depart ||--o{ sta_sys_depart_role_user : "部门角色"
    
    %% 数据源管理模块
    ins_data_source ||--o{ ins_data_source_desc : "数据源描述"
    ins_data_source ||--o{ ins_data_resource : "数据资源"
    ins_data_resource ||--o{ ins_data_resource_desc : "资源描述"
    ins_data_source ||--o{ ins_si_data_source : "SI数据源"
    
    %% 标签管理模块
    ins_tag_client ||--o{ ins_label_correction_record : "标签校正"
    
    %% 区域管理模块
    ins_region ||--o{ ins_region_detail : "区域详情"
    
    %% 客户分析模块
    cli_risk_keywords ||--o{ cli_high_frequency_opinions : "高频意见"
    cli_risk_keywords ||--o{ cli_high_frequency_words : "高频词汇"
    cli_risk_keywords ||--o{ cli_allocation_opinions_record : "意见分配"
    cli_risk_keywords ||--o{ cli_allocation_words_record : "词汇分配"
    
    %% 实体定义
    ins_project_info {
        varchar id PK
        varchar project_Name
        text project_desc
        varchar status
        varchar create_user
        varchar update_user
        datetime create_time
        datetime update_time
    }
    
    ins_role {
        varchar id PK
        varchar role_name
        int role_type
        int enabled
        datetime create_time
        datetime update_time
    }
    
    ins_menu_permission {
        varchar id PK
        varchar parent_id FK
        varchar name
        varchar html_uri
        varchar api_url
        varchar permission_key
        int sort_no
        tinyint last_level
        varchar del_flag
        datetime create_time
    }
    
    sta_sys_role {
        varchar id
        varchar role_name
        varchar role_code
        varchar role_type
        varchar brand_code
        decimal is_export
        decimal is_download
        decimal is_quality
        decimal is_all
        decimal role_status
        text remark
        varchar create_by
        varchar update_by
        datetime create_time
        datetime update_time
    }
    
    ins_data_source {
        varchar id PK
        varchar data_source_name
        varchar data_source_type
        varchar data_source_access_way
        json label_type
        varchar model_type
        varchar create_user
        datetime create_time
    }
```

## 3. StarRocks数据仓库架构

### 3.1 数据仓库分层架构

```mermaid
graph TB
    subgraph "应用层"
        I1[报表应用]
        I2[分析应用]
        I3[洞察应用]
    end
    
    subgraph "视图层"
        J1[业务视图]
        J2[权限视图]
        J3[配置视图]
        J4[元数据视图]
    end
    
    subgraph "汇总层 DWS"
        K1[dws_voc2_sounds_data<br/>声音数据汇总]
        K2[dws_voc2_batch_push_record<br/>批量推送记录]
        K3[dws_voc2_error_push_data<br/>错误推送数据]
    end
    
    subgraph "维度层 DIM"
        L1[dim_voc2_cust_info<br/>客户维度]
        L2[dim_voc2_dealer_info<br/>经销商维度]
        L3[dim_voc2_vehicle_info<br/>车辆维度]
        L4[dim_voc2_product_info<br/>产品维度]
        L5[dim_voc2_big_v_user_info<br/>大V用户维度]
        L6[dim_voc2_manager_user_info<br/>管理员维度]
    end
    
    subgraph "明细层 DWD"
        M1[dwd_voc2_raw_public_opinion<br/>公开意见]
        M2[dwd_voc2_raw_private_opinion<br/>私密意见]
        M3[dwd_voc2_raw_public_consult<br/>公开咨询]
        M4[dwd_voc2_raw_private_consult<br/>私密咨询]
        M5[dwd_voc2_raw_public_posts_comment<br/>公开帖子评论]
        M6[dwd_voc2_raw_private_posts_comment<br/>私密帖子评论]
        M7[dwd_voc2_raw_public_questionnaire<br/>公开问卷]
        M8[dwd_voc2_raw_private_questionnaire<br/>私密问卷]
        M9[dwd_voc2_raw_private_work_order<br/>私密工单]
    end
    
    I1 --> J1
    I2 --> J2
    I3 --> J3
    
    J1 --> K1
    J2 --> K2
    J3 --> K3
    
    K1 --> L1
    K1 --> L2
    K1 --> L3
    K1 --> L4
    K1 --> L5
    K1 --> L6
    
    L1 --> M1
    L2 --> M2
    L3 --> M3
    L4 --> M4
    L5 --> M5
    L6 --> M6
```

### 3.2 核心事实表结构

```mermaid
erDiagram
    dws_voc2_sounds_data {
        varchar id PK "声音ID"
        varchar data_id "数据唯一标识"
        varchar channel_code "渠道编码"
        varchar channel "渠道名称"
        varchar brand_code "品牌编码"
        varchar brand "品牌名称"
        varchar vehicle_series_code "车系编码"
        varchar vehicle_series "车系名称"
        varchar vehicle_model_code "车型编码"
        varchar vehicle_model "车型名称"
        varchar label_type "数据类型"
        varchar sentiment "情感倾向"
        varchar intention "意图分类"
        text hot_word "热词提取"
        text user_journey "用户旅程"
        text keywords "关键词"
        text topic "观点主题"
        datetime data_create_time "数据产生时间"
        datetime create_time "数据抓取时间"
        varchar dealer_id "经销商ID"
        varchar dealer_code "经销商编码"
        varchar dealer_name "经销商全称"
        varchar dealer_province_code "经销商省份编码"
        varchar dealer_province "经销商省份"
        varchar dealer_city_code "经销商城市编码"
        varchar dealer_city "经销商城市"
        datetime vehicle_purchase_date "车辆购买日期"
        datetime vehicle_production_date "车辆生产日期"
        datetime vehicle_factory_release_date "车辆出厂日期"
        varchar vehicle_vin "车辆车架号"
        varchar one_id "客户唯一标识"
        varchar cust_name "客户姓名"
        varchar cust_mobile "客户手机号"
        int cust_age "客户年龄"
        varchar cust_gender "客户性别"
        varchar cust_province_code "客户省份编码"
        varchar cust_province "客户省份"
        varchar cust_city_code "客户城市编码"
        varchar cust_city "客户城市"
        varchar cust_district_code "客户区县编码"
        varchar cust_district "客户区县"
        varchar cust_highest_edu "客户最高学历"
        varchar cust_monthly_income "客户月收入"
        datetime cust_last_purchase_date "客户最近购车时间"
        varchar cust_type "客户类型"
        boolean is_vehicle_owner "是否车主"
        varchar content_type "原文内容类型"
        varchar work_order_id "工单ID"
        boolean is_main_post "是否主贴"
        text post_title "帖子标题"
        text post_original_link "帖子原文链接"
        text post_original_content "帖子原文详情"
        varchar quest_type "问卷类型"
        text quest_question_content "问卷题目内容"
        int quest_answer_score "问卷答案分数"
        varchar quest_business_type "问卷业务类型"
        varchar quest_business_scenario "问卷业务场景"
        varchar vtr_tag_first_code "VTR标签1级编码"
        varchar vtr_tag_second_code "VTR标签2级编码"
        varchar vtr_tag_three_code "VTR标签3级编码"
        varchar vtr_tag_first "VTR标签1级"
        varchar vtr_tag_second "VTR标签2级"
        varchar vtr_tag_three "VTR标签3级"
        varchar com_tag_first_code "商品化属性标签1级编码"
        varchar com_tag_second_code "商品化属性标签2级编码"
        varchar com_tag_three_code "商品化属性标签3级编码"
        varchar com_tag_first "商品化属性标签1级"
        varchar com_tag_second "商品化属性标签2级"
        varchar com_tag_three "商品化属性标签3级"
        varchar adb_tag_first_code "全领域业务标签1级编码"
        varchar adb_tag_second_code "全领域业务标签2级编码"
        varchar adb_tag_three_code "全领域业务标签3级编码"
        varchar adb_tag_first "全领域业务标签1级"
        varchar adb_tag_second "全领域业务标签2级"
        varchar adb_tag_three "全领域业务标签3级"
        varchar wom_tag_first_code "口碑评价指标1级编码"
        varchar wom_tag_second_code "口碑评价指标2级编码"
        varchar wom_tag_three_code "口碑评价指标3级编码"
        varchar wom_tag_first "口碑评价指标1级"
        varchar wom_tag_second "口碑评价指标2级"
        varchar wom_tag_three "口碑评价指标3级"
        varchar cx_tag_first_code "客户体验指标1级编码"
        varchar cx_tag_second_code "客户体验指标2级编码"
        varchar cx_tag_three_code "客户体验指标3级编码"
        varchar cx_tag_first "客户体验指标1级"
        varchar cx_tag_second "客户体验指标2级"
        varchar cx_tag_three "客户体验指标3级"
        varchar cj_tag_first_code "全旅程客户标签1级编码"
        varchar cj_tag_second_code "全旅程客户标签2级编码"
        varchar cj_tag_three_code "全旅程客户标签3级编码"
        varchar cj_tag_first "全旅程客户标签1级"
        varchar cj_tag_second "全旅程客户标签2级"
        varchar cj_tag_three "全旅程客户标签3级"
        varchar sl_tag_first_code "销售线索标签1级编码"
        varchar sl_tag_second_code "销售线索标签2级编码"
        varchar sl_tag_three_code "销售线索标签3级编码"
        varchar sl_tag_first "销售线索标签1级"
        varchar sl_tag_second "销售线索标签2级"
        varchar sl_tag_three "销售线索标签3级"
        varchar om_tag_first_code "全媒体指标1级编码"
        varchar om_tag_second_code "全媒体指标2级编码"
        varchar om_tag_three_code "全媒体指标3级编码"
        varchar om_tag_first "全媒体指标1级"
        varchar om_tag_second "全媒体指标2级"
        varchar om_tag_three "全媒体指标3级"
        varchar data_create_week "数据产生周期-周"
        varchar data_create_month "数据产生周期-月"
        varchar data_create_quarter "数据产生周期-季"
        varchar data_create_year "数据产生周期-年"
    }
```

## 4. 权限架构设计

### 4.1 权限体系架构

```mermaid
graph TB
    subgraph "权限控制层"
        N1[用户认证]
        N2[角色管理]
        N3[权限分配]
        N4[数据权限]
    end
    
    subgraph "权限数据层"
        O1[用户表]
        O2[角色表]
        O3[权限表]
        O4[用户角色关联]
        O5[角色权限关联]
    end
    
    subgraph "权限类型"
        P1[功能权限]
        P2[数据权限]
        P3[区域权限]
        P4[品牌权限]
        P5[渠道权限]
        P6[标签权限]
    end
    
    subgraph "权限范围"
        Q1[全局权限]
        Q2[区域权限]
        Q3[品牌权限]
        Q4[渠道权限]
        Q5[标签权限]
        Q6[车系权限]
    end
    
    N1 --> O1
    N2 --> O2
    N3 --> O3
    N4 --> O4
    N4 --> O5
    
    O1 --> P1
    O2 --> P2
    O3 --> P3
    O4 --> P4
    O5 --> P5
    O5 --> P6
    
    P1 --> Q1
    P2 --> Q2
    P3 --> Q3
    P4 --> Q4
    P5 --> Q5
    P6 --> Q6
```

### 4.2 权限关系图

```mermaid
erDiagram
    %% 基础权限体系
    sys_users ||--o{ ins_user_role : "用户角色"
    ins_role ||--o{ ins_user_role : "角色用户"
    ins_role ||--o{ ins_role_relation_permission : "角色权限"
    ins_menu_permission ||--o{ ins_role_relation_permission : "权限角色"
    
    %% 统计权限体系
    sta_sys_role ||--o{ sta_sys_user_role : "统计用户角色"
    sta_sys_role ||--o{ sta_sys_role_area : "区域权限"
    sta_sys_role ||--o{ sta_sys_role_business_tag : "业务标签权限"
    sta_sys_role ||--o{ sta_sys_role_channel : "渠道权限"
    sta_sys_role ||--o{ sta_sys_role_permission : "功能权限"
    sta_sys_role ||--o{ sta_sys_role_quality_tag : "质量标签权限"
    sta_sys_role ||--o{ sta_sys_role_series : "车系权限"
    
    %% 组织机构权限
    sta_sys_depart ||--o{ sta_sys_user_depart : "用户部门"
    sta_sys_depart ||--o{ sta_sys_depart_role_user : "部门角色用户"
    
    %% 实体定义
    sys_users {
        varchar id PK
        varchar username UK
        varchar phone
        varchar email
        int enabled
        varchar client_id
        datetime create_time
    }
    
    ins_role {
        varchar id PK
        varchar role_name
        int role_type
        int enabled
        datetime create_time
    }
    
    ins_menu_permission {
        varchar id PK
        varchar parent_id FK
        varchar name
        varchar html_uri
        varchar api_url
        varchar permission_key
        int sort_no
        tinyint last_level
        varchar del_flag
    }
    
    sta_sys_role {
        varchar id
        varchar role_name
        varchar role_code
        varchar role_type
        varchar brand_code
        decimal is_export
        decimal is_download
        decimal is_quality
        decimal is_all
        decimal role_status
        text remark
    }
    
    sta_sys_depart {
        varchar id
        varchar dept_name
        varchar dept_code
        varchar parent_id
        int dept_level
        int sort_order
        int status
    }
```

## 5. 数据流转架构

### 5.1 完整数据流转图

```mermaid
flowchart TD
    subgraph "数据源层"
        R1[CRM系统]
        R2[社交媒体]
        R3[客服系统]
        R4[问卷调研]
        R5[其他数据源]
    end
    
    subgraph "数据采集层"
        S1[API接口]
        S2[文件上传]
        S3[数据库同步]
        S4[消息队列]
        S5[爬虫采集]
    end
    
    subgraph "数据处理层"
        T1[数据清洗]
        T2[数据转换]
        T3[数据验证]
        T4[数据标准化]
        T5[数据去重]
    end
    
    subgraph "AI分析层"
        U1[情感分析]
        U2[意图识别]
        U3[关键词提取]
        U4[主题分类]
        U5[标签标注]
    end
    
    subgraph "数据存储层"
        V1[MySQL基础库]
        V2[MySQL客户库]
        V3[StarRocks仓库]
        V4[Redis缓存]
        V5[Elasticsearch]
    end
    
    subgraph "数据服务层"
        W1[实时查询]
        W2[批量分析]
        W3[报表生成]
        W4[数据导出]
        W5[数据API]
    end
    
    subgraph "应用层"
        X1[报表应用]
        X2[分析应用]
        X3[洞察应用]
        X4[管理应用]
    end
    
    R1 --> S1
    R2 --> S5
    R3 --> S1
    R4 --> S2
    R5 --> S3
    
    S1 --> T1
    S2 --> T1
    S3 --> T1
    S4 --> T1
    S5 --> T1
    
    T1 --> T2
    T2 --> T3
    T3 --> T4
    T4 --> T5
    
    T5 --> U1
    T5 --> U2
    T5 --> U3
    T5 --> U4
    T5 --> U5
    
    U1 --> V1
    U2 --> V1
    U3 --> V1
    U4 --> V1
    U5 --> V1
    
    V1 --> V2
    V2 --> V3
    V3 --> V4
    V3 --> V5
    
    V1 --> W1
    V2 --> W1
    V3 --> W2
    V3 --> W3
    V4 --> W1
    V5 --> W4
    
    W1 --> X1
    W2 --> X2
    W3 --> X3
    W4 --> X4
    W5 --> X1
    W5 --> X2
    W5 --> X3
    W5 --> X4
```

### 5.2 数据同步架构

```mermaid
graph LR
    subgraph "基础库 vdp_ms_be"
        Y1[用户数据]
        Y2[客户数据]
        Y3[品牌数据]
        Y4[渠道数据]
        Y5[字典数据]
    end
    
    subgraph "同步服务"
        Z1[数据同步服务]
        Z2[增量同步]
        Z3[全量同步]
        Z4[实时同步]
    end
    
    subgraph "客户库 vdp_ms_td"
        AA1[用户角色]
        AA2[项目数据]
        AA3[权限数据]
        AA4[业务数据]
    end
    
    subgraph "数据仓库 StarRocks"
        BB1[汇总数据]
        BB2[维度数据]
        BB3[事实数据]
        BB4[视图数据]
    end
    
    Y1 --> Z1
    Y2 --> Z1
    Y3 --> Z1
    Y4 --> Z1
    Y5 --> Z1
    
    Z1 --> Z2
    Z1 --> Z3
    Z1 --> Z4
    
    Z2 --> AA1
    Z3 --> AA2
    Z4 --> AA3
    
    AA1 --> BB1
    AA2 --> BB2
    AA3 --> BB3
    AA4 --> BB4
```

## 6. 性能优化架构

### 6.1 缓存架构

```mermaid
graph TB
    subgraph "应用层缓存"
        CC1[本地缓存]
        CC2[分布式缓存]
        CC3[CDN缓存]
    end
    
    subgraph "数据层缓存"
        DD1[查询缓存]
        DD2[结果缓存]
        DD3[会话缓存]
        DD4[权限缓存]
    end
    
    subgraph "缓存策略"
        EE1[LRU策略]
        EE2[TTL策略]
        EE3[LFU策略]
        EE4[随机策略]
    end
    
    subgraph "缓存存储"
        FF1[Redis主从]
        FF2[Redis集群]
        FF3[本地内存]
        FF4[文件缓存]
    end
    
    CC1 --> DD1
    CC2 --> DD2
    CC3 --> DD3
    
    DD1 --> EE1
    DD2 --> EE2
    DD3 --> EE3
    DD4 --> EE4
    
    EE1 --> FF1
    EE2 --> FF2
    EE3 --> FF3
    EE4 --> FF4
```

### 6.2 索引架构

```mermaid
graph LR
    subgraph "索引类型"
        GG1[主键索引]
        GG2[唯一索引]
        GG3[普通索引]
        GG4[复合索引]
        GG5[全文索引]
    end
    
    subgraph "索引策略"
        HH1[查询优化]
        HH2[排序优化]
        HH3[分组优化]
        HH4[连接优化]
    end
    
    subgraph "索引维护"
        II1[索引重建]
        II2[索引更新]
        II3[索引监控]
        II4[索引分析]
    end
    
    GG1 --> HH1
    GG2 --> HH2
    GG3 --> HH3
    GG4 --> HH4
    GG5 --> HH1
    
    HH1 --> II1
    HH2 --> II2
    HH3 --> II3
    HH4 --> II4
```

## 7. 安全架构

### 7.1 数据安全架构

```mermaid
graph TB
    subgraph "访问控制"
        JJ1[身份认证]
        JJ2[权限验证]
        JJ3[会话管理]
        JJ4[审计日志]
    end
    
    subgraph "数据加密"
        KK1[传输加密]
        KK2[存储加密]
        KK3[字段加密]
        KK4[备份加密]
    end
    
    subgraph "数据脱敏"
        LL1[姓名脱敏]
        LL2[手机号脱敏]
        LL3[身份证脱敏]
        LL4[地址脱敏]
    end
    
    subgraph "安全监控"
        MM1[异常检测]
        MM2[入侵检测]
        MM3[数据泄露检测]
        MM4[合规检查]
    end
    
    JJ1 --> KK1
    JJ2 --> KK2
    JJ3 --> KK3
    JJ4 --> KK4
    
    KK1 --> LL1
    KK2 --> LL2
    KK3 --> LL3
    KK4 --> LL4
    
    LL1 --> MM1
    LL2 --> MM2
    LL3 --> MM3
    LL4 --> MM4
```

## 8. 监控运维架构

### 8.1 监控架构

```mermaid
graph LR
    subgraph "监控对象"
        NN1[数据库性能]
        NN2[应用性能]
        NN3[系统资源]
        NN4[业务指标]
    end
    
    subgraph "监控工具"
        OO1[Prometheus]
        OO2[Grafana]
        OO3[AlertManager]
        OO4[ELK Stack]
    end
    
    subgraph "告警策略"
        PP1[性能告警]
        PP2[容量告警]
        PP3[错误告警]
        PP4[业务告警]
    end
    
    subgraph "运维操作"
        QQ1[自动扩容]
        QQ2[故障转移]
        QQ3[数据备份]
        QQ4[性能优化]
    end
    
    NN1 --> OO1
    NN2 --> OO2
    NN3 --> OO3
    NN4 --> OO4
    
    OO1 --> PP1
    OO2 --> PP2
    OO3 --> PP3
    OO4 --> PP4
    
    PP1 --> QQ1
    PP2 --> QQ2
    PP3 --> QQ3
    PP4 --> QQ4
```

## 9. 扩展性架构

### 9.1 水平扩展架构

```mermaid
graph TB
    subgraph "负载均衡"
        RR1[应用负载均衡]
        RR2[数据库负载均衡]
        RR3[缓存负载均衡]
        RR4[存储负载均衡]
    end
    
    subgraph "分片策略"
        SS1[水平分片]
        SS2[垂直分片]
        SS3[混合分片]
        SS4[动态分片]
    end
    
    subgraph "集群管理"
        TT1[主从复制]
        TT2[读写分离]
        TT3[故障转移]
        TT4[数据同步]
    end
    
    subgraph "扩展策略"
        UU1[自动扩容]
        UU2[手动扩容]
        UU3[弹性伸缩]
        UU4[资源调度]
    end
    
    RR1 --> SS1
    RR2 --> SS2
    RR3 --> SS3
    RR4 --> SS4
    
    SS1 --> TT1
    SS2 --> TT2
    SS3 --> TT3
    SS4 --> TT4
    
    TT1 --> UU1
    TT2 --> UU2
    TT3 --> UU3
    TT4 --> UU4
```

## 10. 总结

### 10.1 架构特点

1. **分层设计**: 采用清晰的分层架构，职责明确，便于维护和扩展
2. **数据分离**: 基础数据和业务数据分离存储，提高安全性和性能
3. **权限控制**: 多层次权限控制体系，确保数据安全
4. **高可用性**: 支持主从复制、故障转移等机制
5. **可扩展性**: 支持水平扩展和垂直扩展
6. **性能优化**: 多级缓存、索引优化、查询优化等策略

### 10.2 技术栈

- **数据库**: MySQL 8.0, StarRocks
- **缓存**: Redis
- **搜索引擎**: Elasticsearch
- **消息队列**: Kafka
- **监控**: Prometheus, Grafana
- **容器化**: Docker, Kubernetes

### 10.3 最佳实践

1. **数据建模**: 遵循星型模型和雪花模型
2. **索引设计**: 根据查询模式设计合适的索引
3. **分区策略**: 按时间和范围进行分区
4. **备份策略**: 定期备份，异地存储
5. **监控告警**: 实时监控，及时告警
6. **安全防护**: 多层次安全防护机制 