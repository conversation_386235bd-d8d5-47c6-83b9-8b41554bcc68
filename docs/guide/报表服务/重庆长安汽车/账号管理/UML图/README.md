# 账号管理UML图集

## 📋 图表概述

本目录包含了账号管理系统的核心架构图和业务流程图，以SVG格式提供，便于在文档中引用和展示。

## 🏗️ 图表列表

### 1. 账号管理系统架构图
**文件名**: `账号管理系统架构图.svg`

**图表描述**: 
展示了账号管理系统的完整技术架构，从前端用户界面到后端数据存储的各个层次，包括：

- **前端层**: 管理后台、用户界面、权限管理界面、部门管理界面
- **网关层**: API Gateway (Spring Cloud Gateway)
- **服务层**: 账号管理服务、安全认证服务、角色权限服务、部门管理服务
- **业务逻辑层**: 账号信息服务、用户角色服务、部门同步服务
- **数据访问层**: 各种DAO接口实现
- **数据存储层**: MySQL数据库、Redis缓存、Elasticsearch日志
- **外部系统**: 企业AD域、OA系统、邮件系统

**适用场景**:
- 系统架构设计评审
- 新员工技术培训
- 系统部署规划
- 技术文档编写

### 2. 账号管理核心业务流程图
**文件名**: `账号管理核心业务流程图.svg`

**图表描述**:
详细展示了账号管理的四个核心业务流程：

1. **创建账号流程**:
   - 验证账号信息完整性
   - 检查账号唯一性
   - 调用安全服务创建用户
   - 分配角色权限和部门关联
   - 记录操作日志

2. **更新账号流程**:
   - 验证用户权限
   - 获取原账号信息
   - 更新账号信息、角色权限、部门关联
   - 记录变更日志

3. **查询账号流程**:
   - 构建查询条件
   - 执行分页查询
   - 组装返回数据

4. **删除账号流程**:
   - 验证删除权限
   - 执行逻辑删除
   - 清理相关权限
   - 记录删除日志

**适用场景**:
- 业务流程梳理
- 开发需求分析
- 测试用例设计
- 用户操作手册编写

## 🎨 图表设计说明

### 颜色编码
- **绿色** (#4caf50): 开始/结束节点、成功操作
- **橙色** (#ff9800): 决策判断节点
- **蓝色** (#2196f3): 处理过程节点
- **红色** (#f44336): 错误处理节点
- **浅蓝色** (#e1f5fe): 前端层组件
- **紫色** (#f3e5f5): 网关层组件
- **浅绿色** (#e8f5e8): 服务层组件
- **橙色** (#fff3e0): 业务逻辑层组件
- **粉色** (#fce4ec): 数据访问层组件
- **浅绿色** (#f1f8e9): 数据存储层组件
- **黄色** (#fff8e1): 外部系统组件

### 图形符号说明
- **椭圆**: 开始/结束节点
- **矩形**: 处理过程
- **菱形**: 决策判断
- **圆柱体**: 数据存储
- **箭头**: 数据流向或控制流

## 📖 使用指南

### 在Markdown中引用
```markdown
![账号管理系统架构图](./UML图/账号管理系统架构图.svg)
![账号管理核心业务流程图](./UML图/账号管理核心业务流程图.svg)
```

### 在HTML中引用
```html
<img src="./UML图/账号管理系统架构图.svg" alt="账号管理系统架构图" width="100%">
<img src="./UML图/账号管理核心业务流程图.svg" alt="账号管理核心业务流程图" width="100%">
```

### 在文档工具中使用
- **Confluence**: 直接上传SVG文件或复制SVG代码
- **GitBook**: 使用相对路径引用
- **Notion**: 上传为图片文件
- **Word/PowerPoint**: 插入为图片或对象

## 🔧 图表维护

### 更新原则
1. **版本控制**: 每次更新都要记录版本信息
2. **向后兼容**: 保持图表的基本结构不变
3. **文档同步**: 图表更新后及时更新相关文档
4. **评审机制**: 重大变更需要团队评审

### 更新流程
1. 识别需要更新的内容
2. 修改SVG源文件
3. 验证图表的正确性和美观性
4. 更新相关文档引用
5. 提交版本控制系统

### 质量标准
- **清晰度**: 文字清晰可读，线条明确
- **一致性**: 颜色、字体、样式保持一致
- **完整性**: 包含所有必要的组件和连接
- **准确性**: 反映真实的系统架构和业务流程

## 📊 图表统计信息

| 图表名称 | 文件大小 | 组件数量 | 连接线数量 | 最后更新 |
|----------|----------|----------|------------|----------|
| 账号管理系统架构图 | ~15KB | 25个组件 | 20条连接线 | 2024-03-01 |
| 账号管理核心业务流程图 | ~18KB | 30个节点 | 35条连接线 | 2024-03-01 |

## 🔄 版本历史

### v1.0.0 (2024-03-01)
- ✨ 初始版本发布
- ✨ 创建系统架构图
- ✨ 创建核心业务流程图
- 📚 完善图表说明文档

## 📞 技术支持

如果您在使用这些图表时遇到问题，或者有改进建议，请联系：

- **邮箱**: <EMAIL>
- **项目地址**: [VOC项目仓库]
- **文档维护**: VOC开发团队

## 📄 许可证

这些图表文件遵循项目的整体许可证协议。

---

**最后更新**: 2024-03-01  
**维护团队**: VOC开发团队
