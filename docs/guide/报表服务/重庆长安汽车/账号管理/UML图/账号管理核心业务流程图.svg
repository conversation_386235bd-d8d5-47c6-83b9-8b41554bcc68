<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1400" height="1000" viewBox="0 0 1400 1000">
  <defs>
    <style>
      .start-end { fill: #4caf50; stroke: #2e7d32; stroke-width: 2; }
      .decision { fill: #ff9800; stroke: #f57c00; stroke-width: 2; }
      .process { fill: #2196f3; stroke: #1976d2; stroke-width: 2; }
      .error { fill: #f44336; stroke: #d32f2f; stroke-width: 2; }
      .success { fill: #4caf50; stroke: #388e3c; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 11px; text-anchor: middle; fill: white; font-weight: bold; }
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .label { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; fill: #333; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" class="title">账号管理核心业务流程图</text>
  
  <!-- 开始节点 -->
  <ellipse cx="700" cy="80" rx="40" ry="20" class="start-end"/>
  <text x="700" y="85" class="text">开始</text>
  
  <!-- 操作类型判断 -->
  <polygon points="700,120 750,150 700,180 650,150" class="decision"/>
  <text x="700" y="155" class="text">操作类型</text>
  
  <!-- 创建账号分支 -->
  <rect x="100" y="220" width="100" height="40" class="process" rx="5"/>
  <text x="150" y="245" class="text">验证账号信息</text>
  
  <polygon points="150,290 200,320 150,350 100,320" class="decision"/>
  <text x="150" y="315" class="text">信息是否</text>
  <text x="150" y="330" class="text">完整?</text>
  
  <rect x="50" y="380" width="100" height="40" class="error" rx="5"/>
  <text x="100" y="405" class="text">返回错误信息</text>
  
  <rect x="200" y="380" width="100" height="40" class="process" rx="5"/>
  <text x="250" y="405" class="text">检查账号唯一性</text>
  
  <polygon points="250,450 300,480 250,510 200,480" class="decision"/>
  <text x="250" y="475" class="text">账号是否</text>
  <text x="250" y="490" class="text">存在?</text>
  
  <rect x="150" y="540" width="120" height="40" class="error" rx="5"/>
  <text x="210" y="565" class="text">返回账号已存在错误</text>
  
  <rect x="320" y="540" width="120" height="40" class="process" rx="5"/>
  <text x="380" y="555" class="text">调用安全服务</text>
  <text x="380" y="570" class="text">创建用户</text>
  
  <rect x="320" y="600" width="100" height="40" class="process" rx="5"/>
  <text x="370" y="625" class="text">分配角色权限</text>
  
  <rect x="320" y="660" width="100" height="40" class="process" rx="5"/>
  <text x="370" y="685" class="text">关联部门信息</text>
  
  <rect x="320" y="720" width="100" height="40" class="process" rx="5"/>
  <text x="370" y="745" class="text">设置账号状态</text>
  
  <rect x="320" y="780" width="100" height="40" class="success" rx="5"/>
  <text x="370" y="805" class="text">记录操作日志</text>
  
  <rect x="320" y="840" width="100" height="40" class="success" rx="5"/>
  <text x="370" y="865" class="text">返回创建成功</text>
  
  <!-- 更新账号分支 -->
  <rect x="500" y="220" width="100" height="40" class="process" rx="5"/>
  <text x="550" y="245" class="text">验证用户权限</text>
  
  <polygon points="550,290 600,320 550,350 500,320" class="decision"/>
  <text x="550" y="315" class="text">权限是否</text>
  <text x="550" y="330" class="text">足够?</text>
  
  <rect x="450" y="380" width="120" height="40" class="error" rx="5"/>
  <text x="510" y="405" class="text">返回权限不足错误</text>
  
  <rect x="620" y="380" width="100" height="40" class="process" rx="5"/>
  <text x="670" y="405" class="text">获取原账号信息</text>
  
  <rect x="620" y="440" width="100" height="40" class="process" rx="5"/>
  <text x="670" y="465" class="text">更新账号信息</text>
  
  <rect x="620" y="500" width="100" height="40" class="process" rx="5"/>
  <text x="670" y="525" class="text">更新角色权限</text>
  
  <rect x="620" y="560" width="100" height="40" class="process" rx="5"/>
  <text x="670" y="585" class="text">更新部门关联</text>
  
  <rect x="620" y="620" width="100" height="40" class="success" rx="5"/>
  <text x="670" y="645" class="text">记录变更日志</text>
  
  <rect x="620" y="680" width="100" height="40" class="success" rx="5"/>
  <text x="670" y="705" class="text">返回更新成功</text>
  
  <!-- 查询账号分支 -->
  <rect x="800" y="220" width="100" height="40" class="process" rx="5"/>
  <text x="850" y="245" class="text">构建查询条件</text>
  
  <rect x="800" y="280" width="100" height="40" class="process" rx="5"/>
  <text x="850" y="305" class="text">执行分页查询</text>
  
  <rect x="800" y="340" width="100" height="40" class="process" rx="5"/>
  <text x="850" y="365" class="text">组装返回数据</text>
  
  <rect x="800" y="400" width="100" height="40" class="success" rx="5"/>
  <text x="850" y="425" class="text">返回查询结果</text>
  
  <!-- 删除账号分支 -->
  <rect x="1000" y="220" width="100" height="40" class="process" rx="5"/>
  <text x="1050" y="245" class="text">验证删除权限</text>
  
  <polygon points="1050,290 1100,320 1050,350 1000,320" class="decision"/>
  <text x="1050" y="315" class="text">是否有删除</text>
  <text x="1050" y="330" class="text">权限?</text>
  
  <rect x="950" y="380" width="120" height="40" class="error" rx="5"/>
  <text x="1010" y="405" class="text">返回权限不足错误</text>
  
  <rect x="1120" y="380" width="100" height="40" class="process" rx="5"/>
  <text x="1170" y="405" class="text">执行逻辑删除</text>
  
  <rect x="1120" y="440" width="100" height="40" class="process" rx="5"/>
  <text x="1170" y="465" class="text">清理相关权限</text>
  
  <rect x="1120" y="500" width="100" height="40" class="success" rx="5"/>
  <text x="1170" y="525" class="text">记录删除日志</text>
  
  <rect x="1120" y="560" width="100" height="40" class="success" rx="5"/>
  <text x="1170" y="585" class="text">返回删除成功</text>
  
  <!-- 结束节点 -->
  <ellipse cx="700" cy="950" rx="40" ry="20" class="start-end"/>
  <text x="700" y="955" class="text">结束</text>
  
  <!-- 连接线 -->
  <!-- 开始到操作类型 -->
  <line x1="700" y1="100" x2="700" y2="120" class="arrow"/>
  
  <!-- 操作类型到各分支 -->
  <line x1="650" y1="150" x2="150" y2="220" class="arrow"/>
  <text x="400" y="180" class="label">创建账号</text>
  
  <line x1="680" y1="160" x2="550" y2="220" class="arrow"/>
  <text x="610" y="185" class="label">更新账号</text>
  
  <line x1="720" y1="160" x2="850" y2="220" class="arrow"/>
  <text x="785" y="185" class="label">查询账号</text>
  
  <line x1="750" y1="150" x2="1050" y2="220" class="arrow"/>
  <text x="900" y="180" class="label">删除账号</text>
  
  <!-- 创建账号流程 -->
  <line x1="150" y1="260" x2="150" y2="290" class="arrow"/>
  <line x1="100" y1="320" x2="100" y2="380" class="arrow"/>
  <text x="75" y="350" class="label">否</text>
  
  <line x1="200" y1="320" x2="250" y2="380" class="arrow"/>
  <text x="230" y="350" class="label">是</text>
  
  <line x1="250" y1="420" x2="250" y2="450" class="arrow"/>
  
  <line x1="200" y1="480" x2="210" y2="540" class="arrow"/>
  <text x="180" y="510" class="label">是</text>
  
  <line x1="300" y1="480" x2="380" y2="540" class="arrow"/>
  <text x="340" y="510" class="label">否</text>
  
  <line x1="380" y1="580" x2="370" y2="600" class="arrow"/>
  <line x1="370" y1="640" x2="370" y2="660" class="arrow"/>
  <line x1="370" y1="700" x2="370" y2="720" class="arrow"/>
  <line x1="370" y1="760" x2="370" y2="780" class="arrow"/>
  <line x1="370" y1="820" x2="370" y2="840" class="arrow"/>
  
  <!-- 更新账号流程 -->
  <line x1="550" y1="260" x2="550" y2="290" class="arrow"/>
  <line x1="500" y1="320" x2="510" y2="380" class="arrow"/>
  <text x="480" y="350" class="label">否</text>
  
  <line x1="600" y1="320" x2="670" y2="380" class="arrow"/>
  <text x="635" y="350" class="label">是</text>
  
  <line x1="670" y1="420" x2="670" y2="440" class="arrow"/>
  <line x1="670" y1="480" x2="670" y2="500" class="arrow"/>
  <line x1="670" y1="540" x2="670" y2="560" class="arrow"/>
  <line x1="670" y1="600" x2="670" y2="620" class="arrow"/>
  <line x1="670" y1="660" x2="670" y2="680" class="arrow"/>
  
  <!-- 查询账号流程 -->
  <line x1="850" y1="260" x2="850" y2="280" class="arrow"/>
  <line x1="850" y1="320" x2="850" y2="340" class="arrow"/>
  <line x1="850" y1="380" x2="850" y2="400" class="arrow"/>
  
  <!-- 删除账号流程 -->
  <line x1="1050" y1="260" x2="1050" y2="290" class="arrow"/>
  <line x1="1000" y1="320" x2="1010" y2="380" class="arrow"/>
  <text x="980" y="350" class="label">否</text>
  
  <line x1="1100" y1="320" x2="1170" y2="380" class="arrow"/>
  <text x="1135" y="350" class="label">是</text>
  
  <line x1="1170" y1="420" x2="1170" y2="440" class="arrow"/>
  <line x1="1170" y1="480" x2="1170" y2="500" class="arrow"/>
  <line x1="1170" y1="540" x2="1170" y2="560" class="arrow"/>
  
  <!-- 所有分支到结束 -->
  <line x1="100" y1="420" x2="700" y2="930" class="arrow"/>
  <line x1="210" y1="580" x2="700" y2="930" class="arrow"/>
  <line x1="370" y1="880" x2="700" y2="930" class="arrow"/>
  <line x1="510" y1="420" x2="700" y2="930" class="arrow"/>
  <line x1="670" y1="720" x2="700" y2="930" class="arrow"/>
  <line x1="850" y1="440" x2="700" y2="930" class="arrow"/>
  <line x1="1010" y1="420" x2="700" y2="930" class="arrow"/>
  <line x1="1170" y1="600" x2="700" y2="930" class="arrow"/>
  
</svg>
