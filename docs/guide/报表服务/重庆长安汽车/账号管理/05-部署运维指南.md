# 账号管理部署运维指南

## 1. 部署架构概述

账号管理服务采用微服务架构，包含多个独立的服务组件。本指南详细说明了服务的部署方式、配置要求、监控策略和运维操作。

### 1.1 服务组件架构

```
┌─────────────────────────────────────────────────────────────┐
│                        负载均衡层                              │
│                    (Nginx/HAProxy)                          │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                        API网关层                              │
│                   (Spring Cloud Gateway)                    │
└─────────────────────────────────────────────────────────────┘
                                │
        ┌───────────────────────┼───────────────────────┐
        │                       │                       │
┌───────────────┐    ┌───────────────┐    ┌───────────────┐
│   账号管理服务   │    │   安全认证服务   │    │   报表服务      │
│   (Account)    │    │   (Security)    │    │   (Report)     │
└───────────────┘    └───────────────┘    └───────────────┘
        │                       │                       │
        └───────────────────────┼───────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                        数据存储层                              │
│              MySQL + Redis + Elasticsearch                  │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 部署环境要求

**硬件要求**:
- CPU: 4核心以上
- 内存: 8GB以上
- 磁盘: 100GB以上SSD
- 网络: 千兆网卡

**软件要求**:
- 操作系统: CentOS 7.6+ / Ubuntu 18.04+
- Java: OpenJDK 17+
- Docker: 20.10+
- Docker Compose: 1.29+

## 2. 环境配置

### 2.1 开发环境配置

**application-dev.yml**:
```yaml
server:
  port: 8080

spring:
  datasource:
    url: ***********************************************************************************************************
    username: voc_user
    password: voc_password
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  redis:
    host: localhost
    port: 6379
    password: redis_password
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
        group: VOC_GROUP
        
logging:
  level:
    com.voc.service.insights.report: DEBUG
    org.springframework.security: DEBUG
  file:
    name: logs/account-service.log
    
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

### 2.2 生产环境配置

**application-prod.yml**:
```yaml
server:
  port: 8080
  tomcat:
    max-threads: 200
    min-spare-threads: 20
    max-connections: 8192

spring:
  datasource:
    url: ***************************************************************************************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      
  redis:
    cluster:
      nodes: 
        - redis-node1:6379
        - redis-node2:6379
        - redis-node3:6379
    password: ${REDIS_PASSWORD}
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 50
        max-idle: 20
        min-idle: 10
        
  cloud:
    nacos:
      discovery:
        server-addr: nacos-cluster:8848
        namespace: prod
        group: VOC_GROUP
        
logging:
  level:
    root: INFO
    com.voc.service.insights.report: INFO
  file:
    name: /var/log/voc/account-service.log
    max-size: 100MB
    max-history: 30
    
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
```

## 3. Docker部署

### 3.1 Dockerfile

```dockerfile
FROM openjdk:17-jre-slim

LABEL maintainer="VOC Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="VOC Account Management Service"

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用目录
WORKDIR /app

# 复制应用文件
COPY target/voc-report-*.jar app.jar

# 创建日志目录
RUN mkdir -p /var/log/voc

# 设置JVM参数
ENV JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/voc/"

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

### 3.2 Docker Compose配置

**docker-compose.yml**:
```yaml
version: '3.8'

services:
  account-service:
    build: .
    image: voc/account-service:latest
    container_name: voc-account-service
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - NACOS_SERVER=${NACOS_SERVER}
    volumes:
      - ./logs:/var/log/voc
      - ./config:/app/config
    networks:
      - voc-network
    depends_on:
      - mysql
      - redis
      - nacos
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  mysql:
    image: mysql:8.0
    container_name: voc-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=voc_account
      - MYSQL_USER=${DB_USERNAME}
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - mysql-data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - voc-network

  redis:
    image: redis:7-alpine
    container_name: voc-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - voc-network

  nacos:
    image: nacos/nacos-server:v2.2.0
    container_name: voc-nacos
    restart: unless-stopped
    environment:
      - MODE=standalone
      - SPRING_DATASOURCE_PLATFORM=mysql
      - MYSQL_SERVICE_HOST=mysql
      - MYSQL_SERVICE_DB_NAME=nacos
      - MYSQL_SERVICE_USER=${DB_USERNAME}
      - MYSQL_SERVICE_PASSWORD=${DB_PASSWORD}
    volumes:
      - nacos-data:/home/<USER>/data
    ports:
      - "8848:8848"
    networks:
      - voc-network
    depends_on:
      - mysql

volumes:
  mysql-data:
  redis-data:
  nacos-data:

networks:
  voc-network:
    driver: bridge
```

## 4. Kubernetes部署

### 4.1 Deployment配置

**account-deployment.yaml**:
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: account-service
  namespace: voc
  labels:
    app: account-service
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: account-service
  template:
    metadata:
      labels:
        app: account-service
        version: v1.0.0
    spec:
      containers:
      - name: account-service
        image: voc/account-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: password
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: log-volume
          mountPath: /var/log/voc
      volumes:
      - name: config-volume
        configMap:
          name: account-config
      - name: log-volume
        persistentVolumeClaim:
          claimName: account-logs-pvc
```

### 4.2 Service配置

**account-service.yaml**:
```yaml
apiVersion: v1
kind: Service
metadata:
  name: account-service
  namespace: voc
  labels:
    app: account-service
spec:
  selector:
    app: account-service
  ports:
  - name: http
    port: 80
    targetPort: 8080
    protocol: TCP
  type: ClusterIP
```

## 5. 监控配置

### 5.1 Prometheus监控

**prometheus.yml**:
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'account-service'
    static_configs:
      - targets: ['account-service:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s
```

### 5.2 Grafana仪表板

**关键监控指标**:
- JVM内存使用率
- CPU使用率
- 请求响应时间
- 请求成功率
- 数据库连接池状态
- Redis连接状态
- 业务指标（登录次数、账号创建数量等）

## 6. 日志管理

### 6.1 日志配置

**logback-spring.xml**:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/var/log/voc/account-service.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/var/log/voc/account-service.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <maxFileSize>100MB</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
                <maxHistory>30</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            </encoder>
        </appender>
        
        <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
            <appender-ref ref="FILE"/>
            <queueSize>1024</queueSize>
            <discardingThreshold>0</discardingThreshold>
            <includeCallerData>false</includeCallerData>
        </appender>
        
        <root level="INFO">
            <appender-ref ref="ASYNC"/>
        </root>
    </springProfile>
</configuration>
```

### 6.2 ELK日志收集

**filebeat.yml**:
```yaml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/voc/*.log
  fields:
    service: account-service
    environment: production
  fields_under_root: true
  multiline.pattern: '^\d{4}-\d{2}-\d{2}'
  multiline.negate: true
  multiline.match: after

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "voc-account-service-%{+yyyy.MM.dd}"
  
processors:
- add_host_metadata:
    when.not.contains.tags: forwarded
```

## 7. 备份策略

### 7.1 数据库备份

**备份脚本**:
```bash
#!/bin/bash

# 数据库备份脚本
DB_HOST="mysql-cluster"
DB_USER="backup_user"
DB_PASS="backup_password"
DB_NAME="voc_account"
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -h$DB_HOST -u$DB_USER -p$DB_PASS \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  $DB_NAME > $BACKUP_DIR/voc_account_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/voc_account_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "Database backup completed: voc_account_$DATE.sql.gz"
```

### 7.2 配置文件备份

**配置备份脚本**:
```bash
#!/bin/bash

CONFIG_DIR="/app/config"
BACKUP_DIR="/backup/config"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份配置文件
tar -czf $BACKUP_DIR/config_$DATE.tar.gz -C $CONFIG_DIR .

# 删除30天前的备份
find $BACKUP_DIR -name "config_*.tar.gz" -mtime +30 -delete

echo "Configuration backup completed: config_$DATE.tar.gz"
```

## 8. 故障排查

### 8.1 常见问题及解决方案

**问题1: 服务启动失败**
```bash
# 检查日志
docker logs voc-account-service

# 检查配置
docker exec -it voc-account-service cat /app/config/application.yml

# 检查端口占用
netstat -tlnp | grep 8080
```

**问题2: 数据库连接失败**
```bash
# 检查数据库状态
docker exec -it voc-mysql mysql -u root -p -e "SHOW PROCESSLIST;"

# 检查网络连通性
docker exec -it voc-account-service ping mysql

# 检查连接池状态
curl http://localhost:8080/actuator/metrics/hikaricp.connections.active
```

**问题3: Redis连接异常**
```bash
# 检查Redis状态
docker exec -it voc-redis redis-cli ping

# 检查Redis连接数
docker exec -it voc-redis redis-cli info clients

# 检查应用Redis配置
curl http://localhost:8080/actuator/configprops | grep redis
```

### 8.2 性能调优

**JVM调优参数**:
```bash
-Xms4g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:G1HeapRegionSize=16m
-XX:+G1UseAdaptiveIHOP
-XX:G1MixedGCCountTarget=8
-XX:G1MixedGCLiveThresholdPercent=85
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/var/log/voc/
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
-Xloggc:/var/log/voc/gc.log
```

**数据库连接池调优**:
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
```

## 9. 安全加固

### 9.1 网络安全
- 使用HTTPS协议
- 配置防火墙规则
- 限制访问IP范围
- 启用网络隔离

### 9.2 应用安全
- 定期更新依赖包
- 启用安全头配置
- 实施访问控制
- 加密敏感数据

### 9.3 运维安全
- 使用专用运维账号
- 启用操作审计日志
- 定期安全扫描
- 建立应急响应机制

## 10. 运维自动化

### 10.1 CI/CD流水线

**Jenkins Pipeline**:
```groovy
pipeline {
    agent any
    
    stages {
        stage('Build') {
            steps {
                sh 'mvn clean package -DskipTests'
            }
        }
        
        stage('Test') {
            steps {
                sh 'mvn test'
            }
        }
        
        stage('Docker Build') {
            steps {
                sh 'docker build -t voc/account-service:${BUILD_NUMBER} .'
            }
        }
        
        stage('Deploy') {
            steps {
                sh 'kubectl set image deployment/account-service account-service=voc/account-service:${BUILD_NUMBER}'
            }
        }
    }
}
```

### 10.2 自动化运维脚本

**健康检查脚本**:
```bash
#!/bin/bash

SERVICE_URL="http://localhost:8080/actuator/health"
ALERT_EMAIL="<EMAIL>"

# 检查服务健康状态
response=$(curl -s -o /dev/null -w "%{http_code}" $SERVICE_URL)

if [ $response -ne 200 ]; then
    echo "Service health check failed: HTTP $response"
    echo "Account service is down" | mail -s "Service Alert" $ALERT_EMAIL
    exit 1
else
    echo "Service is healthy"
    exit 0
fi
```
