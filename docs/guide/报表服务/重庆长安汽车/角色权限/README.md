# 重庆长安汽车报表服务文档

## 文档概述

本目录包含重庆长安汽车报表服务的详细技术文档，主要涵盖系统的核心功能模块、接口说明、数据模型等内容。

## 文档结构

### 1. 角色权限功能说明
- **文件名**: `角色权限功能说明.md`
- **内容概述**: 详细介绍了报表服务中的角色权限管理功能
- **主要章节**:
  - 功能概述与权限控制维度
  - 核心接口详细说明
  - 数据模型定义
  - REST API接口文档
  - 数据库表结构
  - 业务流程图
  - 常见问题与解决方案
  - 最佳实践建议

## 核心功能模块

### 角色权限管理
角色权限功能是报表服务的核心权限管理模块，支持：

#### 权限控制维度
- **菜单权限**: 控制用户可访问的菜单和页面
- **功能权限**: 控制导出、下载等功能操作
- **数据权限**: 多维度数据访问控制
  - 车系权限：限制可查看的车系数据
  - 渠道权限：限制可查看的渠道数据
  - 区域权限：限制可查看的区域数据
  - 业务标签权限：限制可查看的业务标签数据
  - 质量标签权限：限制可查看的质量标签数据
  - 服务标签权限：限制可查看的服务标签数据

#### 主要服务接口
- `IInsReportRoleService`: 角色管理核心服务
- `IInsReportUserRoleService`: 用户角色关联服务
- `IInsReportSysRoleSeriesService`: 车系权限服务
- `IInsReportSysRoleChannelService`: 渠道权限服务
- `IInsReportSysRoleAreaService`: 区域权限服务
- `IInsReportSysRoleBusinessTagService`: 业务标签权限服务
- `IInsReportSysRoleQualityTagService`: 质量标签权限服务
- `IInsReportSysRoleServiceTagService`: 服务标签权限服务
- `IInsReportSysRolePermissionService`: 菜单权限服务

## 系统架构

### 分层架构
```
前端层 (角色管理页面、权限配置页面、用户管理页面)
    ↓
控制器层 (InsReportRoleController)
    ↓
服务层 (角色管理服务、数据权限服务)
    ↓
数据层 (角色表、权限关联表、数据权限表)
    ↓
缓存层 (Redis缓存、用户权限缓存)
```

### 核心数据表
- `sta_sys_role`: 角色基本信息表
- `sta_sys_user_role`: 用户角色关联表
- `sta_sys_role_permission`: 角色权限关联表
- `sta_sys_menu_permission`: 菜单权限表
- `sta_sys_role_series`: 角色车系关联表
- `sta_sys_role_channel`: 角色渠道关联表
- `sta_sys_role_area`: 角色区域关联表
- `sta_sys_role_business_tag`: 角色业务标签关联表
- `sta_sys_role_quality_tag`: 角色质量标签关联表
- `sta_sys_role_service_tag`: 角色服务标签关联表

## 关键业务流程

### 1. 角色创建流程
1. 填写角色基本信息
2. 选择菜单权限
3. 配置数据权限（车系、渠道、区域、标签等）
4. 设置功能权限（导出、下载等）
5. 保存角色配置
6. 更新权限关联表
7. 清除相关缓存

### 2. 用户权限验证流程
1. 用户发起请求
2. 检查用户登录状态
3. 获取用户角色信息
4. 查询角色权限配置
5. 验证菜单权限
6. 验证功能权限
7. 应用数据权限过滤
8. 返回过滤后的结果

### 3. 权限数据同步流程
1. 权限配置变更
2. 更新角色权限表
3. 更新数据权限关联表
4. 清除用户权限缓存
5. 通知相关服务
6. 重新加载权限配置

## API接口概览

### 角色管理接口
- `POST /role/list` - 分页查询角色列表
- `POST /role/queryMenuPermissionList` - 获取权限菜单下拉
- `POST /role/saveOrUpdateRole` - 保存或更新角色
- `POST /role/queryUserPermission` - 查询用户权限

### 用户角色关联接口
- `POST /userRole/assign` - 分配用户角色
- `POST /userRole/info` - 查询用户角色信息

### 权限验证接口
- `POST /userPermissions` - 获取用户权限

## 技术特性

### 安全特性
- 多维度权限控制
- 数据权限隔离
- 客户数据隔离
- 操作日志记录

### 性能特性
- Redis缓存支持
- 权限信息缓存
- 数据库索引优化
- 分页查询支持

### 扩展特性
- 插件化权限服务
- 灵活的权限配置
- 支持多品牌管理
- 权限继承机制

## 开发指南

### 环境要求
- Java 8+
- Spring Boot 2.x
- MyBatis Plus
- Redis
- MySQL 8.0+

### 配置说明
- 数据源配置：支持多数据源切换
- 缓存配置：Redis缓存配置
- 权限配置：菜单权限初始化

### 部署注意事项
- 数据库表初始化
- 权限数据迁移
- 缓存预热
- 监控配置

## 最佳实践

### 权限设计原则
1. **最小权限原则**: 只授予完成工作所需的最小权限
2. **职责分离**: 不同职责的用户应该有不同的角色
3. **权限继承**: 合理设计角色层次结构
4. **定期审查**: 定期审查和清理不必要的权限

### 安全建议
1. **权限验证**: 所有敏感操作都必须进行权限验证
2. **日志记录**: 记录所有权限相关的操作日志
3. **数据隔离**: 确保不同客户的数据完全隔离
4. **定期备份**: 定期备份权限配置数据

### 性能优化
1. **缓存策略**: 合理使用Redis缓存权限信息
2. **索引优化**: 为权限查询添加必要的数据库索引
3. **查询优化**: 优化权限相关的SQL查询
4. **批量操作**: 权限批量更新时使用事务

## 故障排查

### 常见问题
1. **权限不生效**: 检查缓存是否清除，权限配置是否正确
2. **性能问题**: 检查索引配置，优化查询SQL
3. **数据权限过滤不准确**: 验证权限配置和过滤逻辑

### 监控指标
- 权限验证响应时间
- 缓存命中率
- 权限配置变更频率
- 用户权限查询频率

## 版本历史

### v1.0.0
- 基础角色权限功能
- 菜单权限控制
- 基本数据权限

### v1.1.0
- 增加多维度数据权限
- 支持标签权限控制
- 优化权限验证性能

### v1.2.0
- 增加权限缓存机制
- 支持批量权限操作
- 完善权限审计功能

## 联系方式

如有问题或建议，请联系开发团队：
- 技术支持：<EMAIL>
- 文档维护：<EMAIL>

---

*最后更新时间：2024年12月*
