<svg aria-roledescription="sequence" role="graphics-document document" viewBox="-50 -10 2305 2683" style="max-width: 2305px;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00"><g><rect class="actor actor-bottom" ry="3" rx="3" name="Cache" height="65" width="150" stroke="#666" fill="#eaeaea" y="2597" x="2055"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2629.5" x="2130"><tspan dy="0" x="2130">Redis缓存</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="Database" height="65" width="150" stroke="#666" fill="#eaeaea" y="2597" x="1855"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2629.5" x="1930"><tspan dy="0" x="1930">数据库</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="ChannelService" height="65" width="265" stroke="#666" fill="#eaeaea" y="2597" x="1540"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2629.5" x="1672.5"><tspan dy="0" x="1672.5">IInsReportSysRoleChannelService</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="SeriesService" height="65" width="251" stroke="#666" fill="#eaeaea" y="2597" x="1239"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2629.5" x="1364.5"><tspan dy="0" x="1364.5">IInsReportSysRoleSeriesService</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="UserRoleService" height="65" width="214" stroke="#666" fill="#eaeaea" y="2597" x="975"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2629.5" x="1082"><tspan dy="0" x="1082">IInsReportUserRoleService</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="RoleService" height="65" width="180" stroke="#666" fill="#eaeaea" y="2597" x="669"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2629.5" x="759"><tspan dy="0" x="759">IInsReportRoleService</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="Controller" height="65" width="193" stroke="#666" fill="#eaeaea" y="2597" x="285.5"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2629.5" x="382"><tspan dy="0" x="382">InsReportRoleController</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="Frontend" height="65" width="150" stroke="#666" fill="#eaeaea" y="2597" x="0"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2629.5" x="75"><tspan dy="0" x="75">前端页面</tspan></text></g><g><line name="Cache" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="2597" x2="2130" y1="65" x1="2130" id="actor7"></line><g id="root-7"><rect class="actor actor-top" ry="3" rx="3" name="Cache" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="2055"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="2130"><tspan dy="0" x="2130">Redis缓存</tspan></text></g></g><g><line name="Database" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="2597" x2="1930" y1="65" x1="1930" id="actor6"></line><g id="root-6"><rect class="actor actor-top" ry="3" rx="3" name="Database" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="1855"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1930"><tspan dy="0" x="1930">数据库</tspan></text></g></g><g><line name="ChannelService" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="2597" x2="1672.5" y1="65" x1="1672.5" id="actor5"></line><g id="root-5"><rect class="actor actor-top" ry="3" rx="3" name="ChannelService" height="65" width="265" stroke="#666" fill="#eaeaea" y="0" x="1540"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1672.5"><tspan dy="0" x="1672.5">IInsReportSysRoleChannelService</tspan></text></g></g><g><line name="SeriesService" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="2597" x2="1364.5" y1="65" x1="1364.5" id="actor4"></line><g id="root-4"><rect class="actor actor-top" ry="3" rx="3" name="SeriesService" height="65" width="251" stroke="#666" fill="#eaeaea" y="0" x="1239"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1364.5"><tspan dy="0" x="1364.5">IInsReportSysRoleSeriesService</tspan></text></g></g><g><line name="UserRoleService" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="2597" x2="1082" y1="65" x1="1082" id="actor3"></line><g id="root-3"><rect class="actor actor-top" ry="3" rx="3" name="UserRoleService" height="65" width="214" stroke="#666" fill="#eaeaea" y="0" x="975"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1082"><tspan dy="0" x="1082">IInsReportUserRoleService</tspan></text></g></g><g><line name="RoleService" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="2597" x2="759" y1="65" x1="759" id="actor2"></line><g id="root-2"><rect class="actor actor-top" ry="3" rx="3" name="RoleService" height="65" width="180" stroke="#666" fill="#eaeaea" y="0" x="669"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="759"><tspan dy="0" x="759">IInsReportRoleService</tspan></text></g></g><g><line name="Controller" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="2597" x2="382" y1="65" x1="382" id="actor1"></line><g id="root-1"><rect class="actor actor-top" ry="3" rx="3" name="Controller" height="65" width="193" stroke="#666" fill="#eaeaea" y="0" x="285.5"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="382"><tspan dy="0" x="382">InsReportRoleController</tspan></text></g></g><g><line name="Frontend" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="2597" x2="75" y1="65" x1="75" id="actor0"></line><g id="root-0"><rect class="actor actor-top" ry="3" rx="3" name="Frontend" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="0"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="75"><tspan dy="0" x="75">前端页面</tspan></text></g></g><style>#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .error-icon{fill:#a44141;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .edge-thickness-normal{stroke-width:1px;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .marker.cross{stroke:lightgrey;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 p{margin:0;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .actor{stroke:#ccc;fill:#1f2020;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 text.actor&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .actor-line{stroke:#ccc;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .messageLine0{stroke-width:1.5;stroke-dasharray:none;stroke:lightgrey;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .messageLine1{stroke-width:1.5;stroke-dasharray:2,2;stroke:lightgrey;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 #arrowhead path{fill:lightgrey;stroke:lightgrey;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .sequenceNumber{fill:black;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 #sequencenumber{fill:lightgrey;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 #crosshead path{fill:lightgrey;stroke:lightgrey;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .messageText{fill:lightgrey;stroke:none;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .labelBox{stroke:#ccc;fill:#1f2020;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .labelText,#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .labelText&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .loopText,#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .loopText&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .loopLine{stroke-width:2px;stroke-dasharray:2,2;stroke:#ccc;fill:#ccc;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .note{stroke:hsl(180, 0%, 18.3529411765%);fill:hsl(180, 1.5873015873%, 28.3529411765%);}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .noteText,#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .noteText&gt;tspan{fill:rgb(183.8476190475, 181.5523809523, 181.5523809523);stroke:none;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .activation0{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .activation1{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .activation2{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .actorPopupMenu{position:absolute;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .actorPopupMenuPanel{position:absolute;fill:#1f2020;box-shadow:0px 8px 16px 0px rgba(0,0,0,0.2);filter:drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .actor-man line{stroke:#ccc;fill:#1f2020;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 .actor-man circle,#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 line{stroke:#ccc;fill:#1f2020;stroke-width:2px;}#mermaid-72426644-8c70-43ee-b1c2-4da75d9e8b00 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g></g><defs><symbol height="24" width="24" id="computer"><path d="M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z" transform="scale(.5)"></path></symbol></defs><defs><symbol clip-rule="evenodd" fill-rule="evenodd" id="database"><path d="M12.258.001l.256.004.255.005.253.008.251.01.249.012.247.015.246.016.242.019.241.02.239.023.236.024.233.027.231.028.229.031.225.032.223.034.22.036.217.038.214.04.211.041.208.043.205.045.201.046.198.048.194.05.191.051.187.053.183.054.18.056.175.057.172.059.168.06.163.061.16.063.155.064.15.066.074.033.073.033.071.034.07.034.069.035.068.035.067.035.066.035.064.036.064.036.062.036.06.036.06.037.058.037.058.037.055.038.055.038.053.038.052.038.051.039.05.039.048.039.047.039.045.04.044.04.043.04.041.04.04.041.039.041.037.041.036.041.034.041.033.042.032.042.03.042.029.042.027.042.026.043.024.043.023.043.021.043.02.043.018.044.017.043.015.044.013.044.012.044.011.045.009.044.007.045.006.045.004.045.002.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z" transform="scale(.5)"></path></symbol></defs><defs><symbol height="24" width="24" id="clock"><path d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z" transform="scale(.5)"></path></symbol></defs><defs><marker orient="auto-start-reverse" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="7.9" id="arrowhead"><path d="M -1 0 L 10 5 L 0 10 z"></path></marker></defs><defs><marker refY="4.5" refX="4" orient="auto" markerHeight="8" markerWidth="15" id="crosshead"><path style="stroke-dasharray: 0, 0;" d="M 1,2 L 6,7 M 6,2 L 1,7" stroke-width="1pt" stroke="#000000" fill="none"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="15.5" id="filled-head"><path d="M 18,7 L9,13 L14,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="40" markerWidth="60" refY="15" refX="15" id="sequencenumber"><circle r="6" cy="15" cx="15"></circle></marker></defs><g><rect class="note" height="39" width="2105" stroke="#666" fill="#EDF2AE" y="75" x="50"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="80" x="1103"><tspan x="1103">场景1: 创建角色并分配权限</tspan></text></g><g><rect class="activation0" height="460" width="10" stroke="#666" fill="#EDF2AE" y="160" x="377"></rect></g><g><rect class="activation0" height="366" width="10" stroke="#666" fill="#EDF2AE" y="208" x="754"></rect></g><g><rect class="activation0" height="44" width="10" stroke="#666" fill="#EDF2AE" y="346" x="1359.5"></rect></g><g><rect class="activation0" height="44" width="10" stroke="#666" fill="#EDF2AE" y="438" x="1667.5"></rect></g><g><rect class="note" height="39" width="2105" stroke="#666" fill="#EDF2AE" y="630" x="50"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="635" x="1103"><tspan x="1103">场景2: 为用户分配角色</tspan></text></g><g><rect class="activation0" height="276" width="10" stroke="#666" fill="#EDF2AE" y="715" x="377"></rect></g><g><rect class="activation0" height="182" width="10" stroke="#666" fill="#EDF2AE" y="763" x="1077"></rect></g><g><rect class="note" height="39" width="2105" stroke="#666" fill="#EDF2AE" y="1001" x="50"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1006" x="1103"><tspan x="1103">场景3: 查询用户权限</tspan></text></g><g><rect class="activation0" height="882" width="10" stroke="#666" fill="#EDF2AE" y="1086" x="377"></rect></g><g><rect class="activation0" height="788" width="10" stroke="#666" fill="#EDF2AE" y="1134" x="754"></rect></g><g><rect class="activation0" height="90" width="10" stroke="#666" fill="#EDF2AE" y="1362" x="1077"></rect></g><g><rect class="activation0" height="90" width="10" stroke="#666" fill="#EDF2AE" y="1592" x="1359.5"></rect></g><g><rect class="activation0" height="90" width="10" stroke="#666" fill="#EDF2AE" y="1730" x="1667.5"></rect></g><g><line class="loopLine" y2="1188" x2="2141" y1="1188" x1="744"></line><line class="loopLine" y2="1876" x2="2141" y1="1188" x1="2141"></line><line class="loopLine" y2="1876" x2="2141" y1="1876" x1="744"></line><line class="loopLine" y2="1876" x2="744" y1="1188" x1="744"></line><line style="stroke-dasharray: 3, 3;" class="loopLine" y2="1284" x2="2141" y1="1284" x1="744"></line><polygon class="labelBox" points="744,1188 794,1188 794,1201 785.6,1208 744,1208"></polygon><text style="font-size: 16px; font-weight: 400;" class="labelText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1201" x="769">alt</text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="1206" x="1467.5"><tspan x="1467.5">[缓存存在]</tspan></text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="1302" x="1442.5">[缓存不存在]</text></g><g><rect class="note" height="39" width="2105" stroke="#666" fill="#EDF2AE" y="1978" x="50"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1983" x="1103"><tspan x="1103">场景4: 权限验证</tspan></text></g><g><rect class="activation0" height="514" width="10" stroke="#666" fill="#EDF2AE" y="2063" x="377"></rect></g><g><rect class="activation0" height="466" width="10" stroke="#666" fill="#EDF2AE" y="2111" x="754"></rect></g><g><line class="loopLine" y2="2211" x2="1941" y1="2211" x1="64"></line><line class="loopLine" y2="2577" x2="1941" y1="2211" x1="1941"></line><line class="loopLine" y2="2577" x2="1941" y1="2577" x1="64"></line><line class="loopLine" y2="2577" x2="64" y1="2211" x1="64"></line><line style="stroke-dasharray: 3, 3;" class="loopLine" y2="2445" x2="1941" y1="2445" x1="64"></line><polygon class="labelBox" points="64,2211 114,2211 114,2224 105.6,2231 64,2231"></polygon><text style="font-size: 16px; font-weight: 400;" class="labelText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2224" x="89">alt</text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="2229" x="1027.5"><tspan x="1027.5">[有权限]</tspan></text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="2463" x="1002.5">[无权限]</text></g><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="129" x="227">POST /role/saveOrUpdateRole</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="160" x2="378" y1="160" x1="76"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="175" x="571">saveOrUpdateRole(clientId, roleModelList)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="206" x2="755" y1="206" x1="387"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="221" x="1345">保存角色基本信息</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="252" x2="1926" y1="252" x1="764"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="267" x="1345">保存菜单权限关联</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="298" x2="1926" y1="298" x1="764"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="313" x="1062">saveOrUpdateData(roleId, model)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="344" x2="1360.5" y1="344" x1="764"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="359" x="1648">保存车系权限关联</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="390" x2="1926" y1="390" x1="1369.5"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="405" x="1216">saveOrUpdateData(roleId, model)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="436" x2="1668.5" y1="436" x1="764"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="451" x="1802">保存渠道权限关联</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="482" x2="1926" y1="482" x1="1677.5"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="497" x="1445">清除相关权限缓存</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="528" x2="2126" y1="528" x1="764"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="543" x="572">返回操作结果</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="574" x2="390" y1="574" x1="754"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="589" x="228">返回成功响应</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="620" x2="79" y1="620" x1="377"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="684" x="227">POST /userRole/assign</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="715" x2="378" y1="715" x1="76"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="730" x="733">saveOrUpdate(userRoleModel)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="761" x2="1078" y1="761" x1="387"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="776" x="1507">查询现有用户角色</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="807" x2="1926" y1="807" x1="1087"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="822" x="1507">保存或更新用户角色关联</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="853" x2="1926" y1="853" x1="1087"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="868" x="1607">清除用户权限缓存</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="899" x2="2126" y1="899" x1="1087"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="914" x="734">返回操作结果</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="945" x2="390" y1="945" x1="1077"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="960" x="228">返回成功响应</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="991" x2="79" y1="991" x1="377"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1055" x="227">POST /role/queryUserPermission</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1086" x2="378" y1="1086" x1="76"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1101" x="571">queryUserPermission(userModel)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1132" x2="755" y1="1132" x1="387"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1147" x="1445">检查权限缓存</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1178" x2="2126" y1="1178" x1="764"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1238" x="1448">返回缓存的权限信息</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1269" x2="767" y1="1269" x1="2129"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1329" x="921">getRoleIdByUserId(clientId, userId)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1360" x2="1078" y1="1360" x1="764"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1375" x="1507">查询用户角色关联</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1406" x2="1926" y1="1406" x1="1087"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1421" x="922">返回角色ID</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1452" x2="767" y1="1452" x1="1077"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1467" x="1345">查询角色权限配置</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1498" x2="1926" y1="1498" x1="764"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1513" x="1345">查询菜单权限</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1544" x2="1926" y1="1544" x1="764"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1559" x="1062">getRoleSeriesList(model)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1590" x2="1360.5" y1="1590" x1="764"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1605" x="1648">查询车系权限</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1636" x2="1926" y1="1636" x1="1369.5"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1651" x="1063">返回车系权限列表</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1682" x2="767" y1="1682" x1="1359.5"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1697" x="1216">getRoleChanneList(model)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1728" x2="1668.5" y1="1728" x1="764"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1743" x="1802">查询渠道权限</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1774" x2="1926" y1="1774" x1="1677.5"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1789" x="1217">返回渠道权限列表</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1820" x2="767" y1="1820" x1="1667.5"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1835" x="1445">缓存权限信息</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1866" x2="2126" y1="1866" x1="764"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1891" x="572">返回用户权限信息</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1922" x2="390" y1="1922" x1="754"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1937" x="228">返回权限数据</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1968" x2="79" y1="1968" x1="377"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2032" x="227">业务请求 (带权限验证)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2063" x2="378" y1="2063" x1="76"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2078" x="571">验证用户权限</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2109" x2="755" y1="2109" x1="387"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2124" x="1445">获取用户权限缓存</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2155" x2="2126" y1="2155" x1="764"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2170" x="1448">返回权限信息</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2201" x2="767" y1="2201" x1="2129"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2261" x="572">权限验证通过</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2292" x2="390" y1="2292" x1="754"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2307" x="1157">执行业务逻辑 (应用数据权限过滤)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2338" x2="1926" y1="2338" x1="387"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2353" x="1160">返回过滤后的数据</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2384" x2="390" y1="2384" x1="1929"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2399" x="228">返回业务数据</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2430" x2="79" y1="2430" x1="377"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2490" x="572">权限验证失败</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2521" x2="390" y1="2521" x1="754"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2536" x="228">返回403错误</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2567" x2="79" y1="2567" x1="377"></line></svg>