<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1093.87890625 3375.375" style="max-width: 1093.87890625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-59588aeb-6016-4831-814e-ab74f17bf181"><style>#mermaid-59588aeb-6016-4831-814e-ab74f17bf181{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .error-icon{fill:#a44141;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .edge-thickness-normal{stroke-width:1px;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .marker.cross{stroke:lightgrey;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 p{margin:0;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .cluster-label text{fill:#F9FFFE;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .cluster-label span{color:#F9FFFE;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .cluster-label span p{background-color:transparent;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .label text,#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 span{fill:#ccc;color:#ccc;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .node rect,#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .node circle,#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .node ellipse,#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .node polygon,#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .rough-node .label text,#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .node .label text,#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .image-shape .label,#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .icon-shape .label{text-anchor:middle;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .rough-node .label,#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .node .label,#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .image-shape .label,#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .icon-shape .label{text-align:center;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .node.clickable{cursor:pointer;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .arrowheadPath{fill:lightgrey;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .cluster text{fill:#F9FFFE;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .cluster span{color:#F9FFFE;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 rect.text{fill:none;stroke-width:0;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .icon-shape,#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .icon-shape p,#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .icon-shape rect,#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-59588aeb-6016-4831-814e-ab74f17bf181 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="数据权限过滤" class="cluster"><rect height="416" width="226" y="2639.375" x="859.87890625" style=""></rect><g transform="translate(924.87890625, 2639.375)" class="cluster-label"><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>数据权限过滤</p></span></div></foreignObject></g></g><g data-look="classic" id="权限缓存检查" class="cluster"><rect height="667.75" width="305.171875" y="549.875" x="627.5859375" style=""></rect><g transform="translate(732.171875, 549.875)" class="cluster-label"><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>权限缓存检查</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M481.793,62L481.793,66.167C481.793,70.333,481.793,78.667,481.863,86.417C481.933,94.167,482.074,101.334,482.144,104.917L482.215,108.501"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M421.895,207.977L365.912,224.127C309.93,240.276,197.965,272.576,141.982,299.392C86,326.208,86,347.542,86,366.875C86,386.208,86,403.542,86,420.875C86,438.208,86,455.542,86,472.875C86,490.208,86,507.542,86,520.375C86,533.208,86,541.542,86,554.375C86,567.208,86,584.542,86,601.875C86,619.208,86,636.542,86,662.365C86,688.188,86,722.5,86,758.813C86,795.125,86,833.438,86,863.26C86,893.083,86,914.417,86,935.75C86,957.083,86,978.417,86,1008.24C86,1038.063,86,1076.375,86,1112.688C86,1149,86,1183.313,86,1206.635C86,1229.958,86,1242.292,86,1259.125C86,1275.958,86,1297.292,86,1316.625C86,1335.958,86,1353.292,86,1370.625C86,1387.958,86,1405.292,86,1422.625C86,1439.958,86,1457.292,86,1484.448C86,1511.604,86,1548.583,86,1587.563C86,1626.542,86,1667.521,86,1698.677C86,1729.833,86,1751.167,86,1770.5C86,1789.833,86,1807.167,86,1834.323C86,1861.479,86,1898.458,86,1937.438C86,1976.417,86,2017.396,86,2048.552C86,2079.708,86,2101.042,86,2120.375C86,2139.708,86,2157.042,86,2174.375C86,2191.708,86,2209.042,86,2226.375C86,2243.708,86,2261.042,86,2278.375C86,2295.708,86,2313.042,86,2330.375C86,2347.708,86,2365.042,86,2382.375C86,2399.708,86,2417.042,86,2436.375C86,2455.708,86,2477.042,86,2498.375C86,2519.708,86,2541.042,86,2560.375C86,2579.708,86,2597.042,86,2609.875C86,2622.708,86,2631.042,86,2643.875C86,2656.708,86,2674.042,86,2691.375C86,2708.708,86,2726.042,86,2743.375C86,2760.708,86,2778.042,86,2795.375C86,2812.708,86,2830.042,86,2847.375C86,2864.708,86,2882.042,86,2899.375C86,2916.708,86,2934.042,86,2951.375C86,2968.708,86,2986.042,86,3003.375C86,3020.708,86,3038.042,86,3050.875C86,3063.708,86,3072.042,86,3084.875C86,3097.708,86,3115.042,86,3132.375C86,3149.708,86,3167.042,86,3179.208C86,3191.375,86,3198.375,86,3201.875L86,3205.375"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_D_2" d="M519.868,230.8L531.356,243.146C542.843,255.492,565.818,280.183,577.305,298.029C588.793,315.875,588.793,326.875,588.793,332.375L588.793,337.875"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_3" d="M588.793,395.875L588.793,400.042C588.793,404.208,588.793,412.542,588.793,420.208C588.793,427.875,588.793,434.875,588.793,438.375L588.793,441.875"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_4" d="M494.793,489.801L462.327,495.646C429.862,501.492,364.931,513.184,332.465,523.196C300,533.208,300,541.542,300,554.375C300,567.208,300,584.542,300,601.875C300,619.208,300,636.542,300,662.365C300,688.188,300,722.5,300,758.813C300,795.125,300,833.438,300,863.26C300,893.083,300,914.417,300,935.75C300,957.083,300,978.417,300.074,994.667C300.149,1010.917,300.298,1022.084,300.372,1027.667L300.447,1033.25"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_5" d="M300.5,1193.125L300.417,1197.208C300.333,1201.292,300.167,1209.458,300.083,1219.708C300,1229.958,300,1242.292,300,1259.125C300,1275.958,300,1297.292,300,1316.625C300,1335.958,300,1353.292,300,1370.625C300,1387.958,300,1405.292,300,1422.625C300,1439.958,300,1457.292,300,1484.448C300,1511.604,300,1548.583,300,1587.563C300,1626.542,300,1667.521,300,1698.677C300,1729.833,300,1751.167,300,1770.5C300,1789.833,300,1807.167,300,1834.323C300,1861.479,300,1898.458,300,1937.438C300,1976.417,300,2017.396,300,2048.552C300,2079.708,300,2101.042,300,2120.375C300,2139.708,300,2157.042,300,2174.375C300,2191.708,300,2209.042,300,2226.375C300,2243.708,300,2261.042,300,2278.375C300,2295.708,300,2313.042,300,2330.375C300,2347.708,300,2365.042,300,2382.375C300,2399.708,300,2417.042,300,2436.375C300,2455.708,300,2477.042,300,2498.375C300,2519.708,300,2541.042,300,2560.375C300,2579.708,300,2597.042,300,2609.875C300,2622.708,300,2631.042,300,2643.875C300,2656.708,300,2674.042,300,2691.375C300,2708.708,300,2726.042,300,2743.375C300,2760.708,300,2778.042,300,2795.375C300,2812.708,300,2830.042,300,2847.375C300,2864.708,300,2882.042,300,2899.375C300,2916.708,300,2934.042,300,2951.375C300,2968.708,300,2986.042,300,3003.375C300,3020.708,300,3038.042,300,3050.875C300,3063.708,300,3072.042,300,3084.875C300,3097.708,300,3115.042,300,3132.375C300,3149.708,300,3167.042,300,3179.208C300,3191.375,300,3198.375,300,3201.875L300,3205.375"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_H_6" d="M329.341,1164.284L334.529,1173.174C339.717,1182.064,350.093,1199.845,355.281,1214.902C360.469,1229.958,360.469,1242.292,391.84,1255.475C423.211,1268.659,485.952,1282.693,517.323,1289.709L548.694,1296.726"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_7" d="M646.598,1345.625L646.598,1349.792C646.598,1353.958,646.598,1362.292,646.598,1369.958C646.598,1377.625,646.598,1384.625,646.598,1388.125L646.598,1391.625"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_8" d="M646.598,1449.625L646.598,1453.792C646.598,1457.958,646.598,1466.292,646.668,1474.042C646.738,1481.792,646.879,1488.959,646.949,1492.542L647.019,1496.126"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_9" d="M601.959,1626.862L586.731,1640.468C571.502,1654.075,541.044,1681.287,525.815,1705.56C510.586,1729.833,510.586,1751.167,510.586,1770.5C510.586,1789.833,510.586,1807.167,510.586,1834.323C510.586,1861.479,510.586,1898.458,510.586,1937.438C510.586,1976.417,510.586,2017.396,510.586,2048.552C510.586,2079.708,510.586,2101.042,510.586,2120.375C510.586,2139.708,510.586,2157.042,510.586,2174.375C510.586,2191.708,510.586,2209.042,510.586,2226.375C510.586,2243.708,510.586,2261.042,510.586,2278.375C510.586,2295.708,510.586,2313.042,510.586,2330.375C510.586,2347.708,510.586,2365.042,510.586,2382.375C510.586,2399.708,510.586,2417.042,510.586,2436.375C510.586,2455.708,510.586,2477.042,510.586,2498.375C510.586,2519.708,510.586,2541.042,510.586,2560.375C510.586,2579.708,510.586,2597.042,510.586,2609.875C510.586,2622.708,510.586,2631.042,510.586,2643.875C510.586,2656.708,510.586,2674.042,510.586,2691.375C510.586,2708.708,510.586,2726.042,510.586,2743.375C510.586,2760.708,510.586,2778.042,510.586,2795.375C510.586,2812.708,510.586,2830.042,510.586,2847.375C510.586,2864.708,510.586,2882.042,510.586,2899.375C510.586,2916.708,510.586,2934.042,510.586,2951.375C510.586,2968.708,510.586,2986.042,510.586,3003.375C510.586,3020.708,510.586,3038.042,510.586,3050.875C510.586,3063.708,510.586,3072.042,510.586,3084.875C510.586,3097.708,510.586,3115.042,510.586,3132.375C510.586,3149.708,510.586,3167.042,510.586,3179.208C510.586,3191.375,510.586,3198.375,510.586,3201.875L510.586,3205.375"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_L_10" d="M684.176,1634.922L693.461,1647.185C702.747,1659.448,721.319,1683.974,730.605,1701.737C739.891,1719.5,739.891,1730.5,739.891,1736L739.891,1741.5"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_M_11" d="M739.891,1799.5L739.891,1803.667C739.891,1807.833,739.891,1816.167,739.961,1823.917C740.031,1831.667,740.172,1838.834,740.242,1842.417L740.312,1846.001"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_N_12" d="M708.786,1990.27L702.051,2001.621C695.317,2012.972,681.848,2035.673,675.113,2057.691C668.379,2079.708,668.379,2101.042,668.379,2120.375C668.379,2139.708,668.379,2157.042,668.379,2174.375C668.379,2191.708,668.379,2209.042,668.379,2226.375C668.379,2243.708,668.379,2261.042,668.379,2278.375C668.379,2295.708,668.379,2313.042,668.379,2330.375C668.379,2347.708,668.379,2365.042,668.379,2382.375C668.379,2399.708,668.379,2417.042,668.379,2436.375C668.379,2455.708,668.379,2477.042,668.379,2498.375C668.379,2519.708,668.379,2541.042,668.379,2560.375C668.379,2579.708,668.379,2597.042,668.379,2609.875C668.379,2622.708,668.379,2631.042,668.379,2643.875C668.379,2656.708,668.379,2674.042,668.379,2691.375C668.379,2708.708,668.379,2726.042,668.379,2743.375C668.379,2760.708,668.379,2778.042,668.379,2795.375C668.379,2812.708,668.379,2830.042,668.379,2847.375C668.379,2864.708,668.379,2882.042,668.379,2899.375C668.379,2916.708,668.379,2934.042,668.379,2951.375C668.379,2968.708,668.379,2986.042,668.379,3003.375C668.379,3020.708,668.379,3038.042,668.379,3050.875C668.379,3063.708,668.379,3072.042,668.379,3079.708C668.379,3087.375,668.379,3094.375,668.379,3097.875L668.379,3101.375"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_O_13" d="M789.825,1972.441L809.251,1986.763C828.676,2001.085,867.528,2029.73,886.953,2049.553C906.379,2069.375,906.379,2080.375,906.379,2085.875L906.379,2091.375"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_P_14" d="M906.379,2149.375L906.379,2153.542C906.379,2157.708,906.379,2166.042,906.379,2173.708C906.379,2181.375,906.379,2188.375,906.379,2191.875L906.379,2195.375"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P_Q_15" d="M906.379,2253.375L906.379,2257.542C906.379,2261.708,906.379,2270.042,906.379,2277.708C906.379,2285.375,906.379,2292.375,906.379,2295.875L906.379,2299.375"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_R_16" d="M906.379,2357.375L906.379,2361.542C906.379,2365.708,906.379,2374.042,906.379,2381.708C906.379,2389.375,906.379,2396.375,906.379,2399.875L906.379,2403.375"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_S_17" d="M906.379,2461.375L906.379,2467.542C906.379,2473.708,906.379,2486.042,906.379,2497.708C906.379,2509.375,906.379,2520.375,906.379,2525.875L906.379,2531.375"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_T_18" d="M849.783,2589.375L841.049,2593.542C832.315,2597.708,814.847,2606.042,806.113,2614.375C797.379,2622.708,797.379,2631.042,797.379,2643.875C797.379,2656.708,797.379,2674.042,797.379,2691.375C797.379,2708.708,797.379,2726.042,797.379,2743.375C797.379,2760.708,797.379,2778.042,797.379,2795.375C797.379,2812.708,797.379,2830.042,797.379,2847.375C797.379,2864.708,797.379,2882.042,797.379,2899.375C797.379,2916.708,797.379,2934.042,797.379,2951.375C797.379,2968.708,797.379,2986.042,797.379,3003.375C797.379,3020.708,797.379,3038.042,797.379,3050.875C797.379,3063.708,797.379,3072.042,805.511,3080.088C813.643,3088.134,829.908,3095.893,838.04,3099.773L846.173,3103.653"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_U_19" d="M678.687,499.875L692.559,504.042C706.432,508.208,734.177,516.542,748.049,524.875C761.922,533.208,761.922,541.542,761.922,549.208C761.922,556.875,761.922,563.875,761.922,567.375L761.922,570.875"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_V_20" d="M761.922,628.875L761.922,633.042C761.922,637.208,761.922,645.542,761.992,653.292C762.062,661.042,762.203,668.209,762.273,671.792L762.343,675.376"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_V_W_21" d="M788.107,809.564L793.16,819.929C798.212,830.293,808.317,851.021,813.369,866.886C818.422,882.75,818.422,893.75,818.422,899.25L818.422,904.75"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_V_H_22" d="M736.736,809.564L731.517,819.929C726.298,830.293,715.86,851.021,710.641,872.052C705.422,893.083,705.422,914.417,705.422,935.75C705.422,957.083,705.422,978.417,705.422,1008.24C705.422,1038.063,705.422,1076.375,705.422,1112.688C705.422,1149,705.422,1183.313,705.422,1206.635C705.422,1229.958,705.422,1242.292,700.205,1254.134C694.988,1265.977,684.555,1277.328,679.338,1283.004L674.121,1288.68"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_W_X_23" d="M818.422,962.75L818.422,968.917C818.422,975.083,818.422,987.417,818.422,1007.573C818.422,1027.729,818.422,1055.708,818.422,1069.698L818.422,1083.688"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_H_24" d="M818.422,1141.688L818.422,1154.344C818.422,1167,818.422,1192.313,818.422,1211.135C818.422,1229.958,818.422,1242.292,802.491,1254.392C786.559,1266.493,754.697,1278.361,738.766,1284.295L722.834,1290.229"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_Y_25" d="M940.908,2589.375L946.236,2593.542C951.565,2597.708,962.222,2606.042,967.55,2614.375C972.879,2622.708,972.879,2631.042,972.879,2638.708C972.879,2646.375,972.879,2653.375,972.879,2656.875L972.879,2660.375"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Y_Z_26" d="M972.879,2718.375L972.879,2722.542C972.879,2726.708,972.879,2735.042,972.879,2742.708C972.879,2750.375,972.879,2757.375,972.879,2760.875L972.879,2764.375"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Z_AA_27" d="M972.879,2822.375L972.879,2826.542C972.879,2830.708,972.879,2839.042,972.879,2846.708C972.879,2854.375,972.879,2861.375,972.879,2864.875L972.879,2868.375"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AA_BB_28" d="M972.879,2926.375L972.879,2930.542C972.879,2934.708,972.879,2943.042,972.879,2950.708C972.879,2958.375,972.879,2965.375,972.879,2968.875L972.879,2972.375"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BB_T_29" d="M972.879,3030.375L972.879,3034.542C972.879,3038.708,972.879,3047.042,972.879,3055.375C972.879,3063.708,972.879,3072.042,968.076,3079.964C963.272,3087.887,953.665,3095.399,948.862,3099.155L944.059,3102.911"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_CC_30" d="M86,3263.375L86,3267.542C86,3271.708,86,3280.042,121.478,3291.229C156.956,3302.415,227.913,3316.456,263.391,3323.476L298.869,3330.496"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_CC_31" d="M300,3263.375L300,3267.542C300,3271.708,300,3280.042,303.454,3287.889C306.907,3295.736,313.814,3303.097,317.268,3306.778L320.721,3310.458"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_CC_32" d="M510.586,3263.375L510.586,3267.542C510.586,3271.708,510.586,3280.042,491.922,3290.207C473.258,3300.372,435.929,3312.369,417.265,3318.368L398.601,3324.367"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_DD_33" d="M668.379,3159.375L668.379,3163.542C668.379,3167.708,668.379,3176.042,673.57,3183.983C678.761,3191.924,689.143,3199.473,694.333,3203.248L699.524,3207.023"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_DD_34" d="M906.379,3159.375L906.379,3163.542C906.379,3167.708,906.379,3176.042,889.601,3185.449C872.822,3194.856,839.265,3205.337,822.487,3210.577L805.709,3215.818"></path><path marker-end="url(#mermaid-59588aeb-6016-4831-814e-ab74f17bf181_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DD_CC_35" d="M739.891,3263.375L739.891,3267.542C739.891,3271.708,739.891,3280.042,683.035,3291.768C626.18,3303.494,512.469,3318.613,455.613,3326.172L398.758,3333.732"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(86, 1935.4375)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(588.79296875, 304.875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(300, 2330.375)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(360.46875, 1254.625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(510.5859375, 2498.375)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(739.890625, 1708.5)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(668.37890625, 2562.375)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(906.37890625, 2058.375)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(818.421875, 871.75)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(705.421875, 999.75)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(481.79296875, 35)" id="flowchart-A-75" class="node default"><rect height="54" width="156" y="-27" x="-78" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户发起请求</p></span></div></foreignObject></g></g><g transform="translate(481.79296875, 189.9375)" id="flowchart-B-76" class="node default"><polygon style="fill:#fff3e0 !important" transform="translate(-77.9375,77.9375)" class="label-container" points="77.9375,0 155.875,-77.9375 77.9375,-155.875 0,-77.9375"></polygon><g transform="translate(-50.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="101.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户是否登录?</p></span></div></foreignObject></g></g><g transform="translate(86, 3236.375)" id="flowchart-C-78" class="node default"><rect height="54" width="156" y="-27" x="-78" style="fill:#ffebee !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>跳转登录页面</p></span></div></foreignObject></g></g><g transform="translate(588.79296875, 368.875)" id="flowchart-D-80" class="node default"><rect height="54" width="138.265625" y="-27" x="-69.1328125" style="" class="basic label-container"></rect><g transform="translate(-39.1328125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="78.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取用户ID</p></span></div></foreignObject></g></g><g transform="translate(588.79296875, 472.875)" id="flowchart-E-82" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询用户角色信息</p></span></div></foreignObject></g></g><g transform="translate(300, 1114.6875)" id="flowchart-F-84" class="node default"><polygon style="fill:#fff3e0 !important" transform="translate(-77.9375,77.9375)" class="label-container" points="77.9375,0 155.875,-77.9375 77.9375,-155.875 0,-77.9375"></polygon><g transform="translate(-50.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="101.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>角色是否存在?</p></span></div></foreignObject></g></g><g transform="translate(300, 3236.375)" id="flowchart-G-86" class="node default"><rect height="54" width="172" y="-27" x="-86" style="fill:#ffebee !important" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>返回无权限错误</p></span></div></foreignObject></g></g><g transform="translate(646.59765625, 1318.625)" id="flowchart-H-88" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取角色权限配置</p></span></div></foreignObject></g></g><g transform="translate(646.59765625, 1422.625)" id="flowchart-I-90" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查菜单权限</p></span></div></foreignObject></g></g><g transform="translate(646.59765625, 1585.5625)" id="flowchart-J-92" class="node default"><polygon style="fill:#fff3e0 !important" transform="translate(-85.9375,85.9375)" class="label-container" points="85.9375,0 171.875,-85.9375 85.9375,-171.875 0,-85.9375"></polygon><g transform="translate(-58.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="117.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>是否有菜单权限?</p></span></div></foreignObject></g></g><g transform="translate(510.5859375, 3236.375)" id="flowchart-K-94" class="node default"><rect height="54" width="149.171875" y="-27" x="-74.5859375" style="fill:#ffebee !important" class="basic label-container"></rect><g transform="translate(-44.5859375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="89.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>返回403错误</p></span></div></foreignObject></g></g><g transform="translate(739.890625, 1772.5)" id="flowchart-L-96" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查功能权限</p></span></div></foreignObject></g></g><g transform="translate(739.890625, 1935.4375)" id="flowchart-M-98" class="node default"><polygon style="fill:#fff3e0 !important" transform="translate(-85.9375,85.9375)" class="label-container" points="85.9375,0 171.875,-85.9375 85.9375,-171.875 0,-85.9375"></polygon><g transform="translate(-58.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="117.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>是否有功能权限?</p></span></div></foreignObject></g></g><g transform="translate(668.37890625, 3132.375)" id="flowchart-N-100" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>隐藏相关功能按钮</p></span></div></foreignObject></g></g><g transform="translate(906.37890625, 2122.375)" id="flowchart-O-102" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查数据权限</p></span></div></foreignObject></g></g><g transform="translate(906.37890625, 2226.375)" id="flowchart-P-104" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取数据权限配置</p></span></div></foreignObject></g></g><g transform="translate(906.37890625, 2330.375)" id="flowchart-Q-106" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>构建数据过滤条件</p></span></div></foreignObject></g></g><g transform="translate(906.37890625, 2434.375)" id="flowchart-R-108" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>执行业务逻辑</p></span></div></foreignObject></g></g><g transform="translate(906.37890625, 2562.375)" id="flowchart-S-110" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>应用数据权限过滤</p></span></div></foreignObject></g></g><g transform="translate(906.37890625, 3132.375)" id="flowchart-T-112" class="node default"><rect height="54" width="188" y="-27" x="-94" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>返回过滤后的数据</p></span></div></foreignObject></g></g><g transform="translate(761.921875, 601.875)" id="flowchart-U-113" class="node default"><rect height="54" width="161.34375" y="-27" x="-80.671875" style="" class="basic label-container"></rect><g transform="translate(-50.671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="101.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查Redis缓存</p></span></div></foreignObject></g></g><g transform="translate(761.921875, 756.8125)" id="flowchart-V-114" class="node default"><polygon style="fill:#f3e5f5 !important" transform="translate(-77.9375,77.9375)" class="label-container" points="77.9375,0 155.875,-77.9375 77.9375,-155.875 0,-77.9375"></polygon><g transform="translate(-50.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="101.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>缓存是否存在?</p></span></div></foreignObject></g></g><g transform="translate(818.421875, 935.75)" id="flowchart-W-115" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>从数据库查询</p></span></div></foreignObject></g></g><g transform="translate(818.421875, 1114.6875)" id="flowchart-X-116" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新缓存</p></span></div></foreignObject></g></g><g transform="translate(972.87890625, 2691.375)" id="flowchart-Y-129" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>车系权限过滤</p></span></div></foreignObject></g></g><g transform="translate(972.87890625, 2795.375)" id="flowchart-Z-130" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>渠道权限过滤</p></span></div></foreignObject></g></g><g transform="translate(972.87890625, 2899.375)" id="flowchart-AA-131" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>区域权限过滤</p></span></div></foreignObject></g></g><g transform="translate(972.87890625, 3003.375)" id="flowchart-BB-132" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>标签权限过滤</p></span></div></foreignObject></g></g><g transform="translate(348.79296875, 3340.375)" id="flowchart-CC-144" class="node default"><rect height="54" width="92" y="-27" x="-46" style="" class="basic label-container"></rect><g transform="translate(-16, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>结束</p></span></div></foreignObject></g></g><g transform="translate(739.890625, 3236.375)" id="flowchart-DD-150" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>继续执行</p></span></div></foreignObject></g></g></g></g></g></svg>