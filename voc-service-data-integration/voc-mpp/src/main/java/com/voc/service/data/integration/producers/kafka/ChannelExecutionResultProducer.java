package com.voc.service.data.integration.producers.kafka;

import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.voc.service.analysis.dto.MessageDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CatListener
 * @createTime 2024年03月14日 9:46
 * @Copyright cuick
 * @Description 再推送数据清洗服务前的【数据结果表】
 */
@Component("dataIntegrationRecord.producer.kafka")
public class ChannelExecutionResultProducer {
    public static final String TOPIC_MPP_INPUT_EXECUTION = "VDP_voc2_processing_data_record";
    @Autowired
    KafkaTemplate<String, String> kafkaTemplate;

    public void push(MessageDTO msg) {
        List<Object> list = (List<Object>) msg.getData();
//        Assert.isTrue(StrUtil.isNotBlank(msg.getSource()), "source cannot be empty");
//        List<Object> list = (List<Object>) msg.getData();
        for (Object obj : list) {
            String sendText = JSONUtil.toJsonStr(obj, JSONConfig.create().setDateFormat("yyyy-MM-dd HH:mm:ss").setIgnoreNullValue(false));
            kafkaTemplate.send(TOPIC_MPP_INPUT_EXECUTION, sendText);
        }
    }
}

