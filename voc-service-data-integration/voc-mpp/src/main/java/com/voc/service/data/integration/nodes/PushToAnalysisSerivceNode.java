package com.voc.service.data.integration.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.voc.service.common.response.Result;
import com.voc.service.common.util.ServiceContextHolder;
import com.voc.service.data.integration.api.IMppInputDataService;
import com.voc.service.data.integration.api.model.ChannelMetaDataModel;
import com.voc.service.data.integration.api.model.DataIntegrationRecordModel;
import com.voc.service.data.integration.config.DataIntegrationConfig;
import com.voc.service.data.integration.enums.ErrorDataMsgEnums;
import com.voc.service.data.integration.nodes.context.ChannelDatasetContext;
import com.voc.service.insights.engine.api.clients.IAysCoreServiceClient;
import com.voc.service.insights.engine.model.data.InsDataSourceModel;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.ToString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Title: LoadChannelDatasetNode
 * @Package: com.voc.service.data.integration.nodes
 * @Description:
 * @Author: cuick
 * @Date: 2024/9/24 17:14
 * @Version:1.0
 */
@LiteflowComponent(id = "pushToAnalysisSerivceNode", name = "推送数据到数据清洗服务")
public class PushToAnalysisSerivceNode extends NodeComponent {
    public static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
    private static final Logger log = LoggerFactory.getLogger(PushToAnalysisSerivceNode.class);

    @Autowired
    DataIntegrationConfig config;
    @Autowired
    IMppInputDataService inputDataService;
    @Autowired
    IAysCoreServiceClient aysCoreServiceClient;


    @Override
    public void process() throws Exception {
        ChannelDatasetContext context = this.getRequestData();
        try {
            if (CollUtil.isEmpty(context.getSuccessfulDataset())) {
                throw new Exception("【".concat(context.getChannelType()).concat("】数据验证后无成功数据："));
            } else {
                log.info("【".concat(context.getChannelType()).concat("】数据验证成功，成功数据：".concat(String.valueOf(context.getSuccessfulDataset().size()))));
                //保存记录成功数据集
                final List<ChannelMetaDataModel> list = context.getSuccessfulDataset().stream().map(item -> {
                    JSONObject jsonObj = JSONUtil.parseObj(item.getData());
                    return JSONUtil.toBean(jsonObj, ChannelMetaDataModel.class);
                }).collect(Collectors.toList());
                this.pushSuccessfulData(context.getWorkId(), context.getClientId(), context.getChannelType(), list);
            }
        } catch (Exception e) {
            log.info("推送数据错误信息：",e);
            //出现服务异常时，将所有数据归为异常处理数据集
            final List<DataIntegrationRecordModel> errorList = context.getSuccessfulDataset().stream().map(data -> {
                data.setErrorCode(ErrorDataMsgEnums.PushServiceHasFailed.getCode());
                data.setErrorMsg(ErrorDataMsgEnums.PushServiceHasFailed.getText());
                return data;
            }).collect(Collectors.toList());
            context.getFailedDataset().addAll(errorList);
            context.setSuccessfulDataset(null);
//            throw new Exception(e.getMessage(), e);
        }
    }

    /**
     * 保存记录成功数据集
     */
    private Set<String> pushSuccessfulData(final String workId, final String clientId, String channelType, List<ChannelMetaDataModel> successfulDataset) {
        if (CollUtil.isEmpty(successfulDataset)) {
            throw new RuntimeException("【".concat(channelType).concat("】未读取到新数据"));
        }
        if (StrUtil.isBlank(config.getRequestAuthorizationTokens())) {
            throw new RuntimeException("【".concat(channelType).concat("】未配置token"));
        }

        for (List<ChannelMetaDataModel> list : CollUtil.split(successfulDataset, config.getBatchPushApiDataSetSize())) {
            log.debug("【{}】开始执行数据推送：{}/{}", channelType, list.size(), successfulDataset.size());
            ServiceContextHolder.setToken(config.getRequestAuthorizationTokens());

            List<Object> data = new ArrayList<>();
            for (ChannelMetaDataModel model : list) {
                JSONObject jsonObj = JSONUtil.parseObj(model);

                if (ObjectUtil.isNotNull(model.getExtAttrs())) {
                    JSONObject jsonExtObj = JSONUtil.parseObj(model.getExtAttrs());
                    final Map attrsMap;
                    if (CollUtil.isNotEmpty(jsonExtObj.values())) {
                        attrsMap = JSONUtil.toBean(jsonExtObj, Map.class);
                        jsonObj.remove("extAttrs");
                    } else {
                        attrsMap = null;
                    }
                    jsonObj.putOnce("bizExtAttrs", attrsMap);
                }
                if (ObjectUtil.isNotNull(model.getExtAttrs2())) {
                    JSONObject jsonExtObj = JSONUtil.parseObj(model.getExtAttrs2());
                    final Map attrsMap;
                    if (CollUtil.isNotEmpty(jsonExtObj.values())) {
                        attrsMap = JSONUtil.toBean(jsonExtObj, Map.class);
                        jsonObj.remove("extAttrs2");
                    } else {
                        attrsMap = null;
                    }
                    jsonObj.putOnce("bizExtAttrs2", attrsMap);
                }

                Map attrsMap = Collections.synchronizedMap(new HashMap());
                try {
                    if (ObjectUtil.isNotNull(model.getExtAttrs3())) {
                        JSONObject jsonExtObj = JSONUtil.parseObj(model.getExtAttrs3());
                        if (CollUtil.isNotEmpty(jsonExtObj.values())) {
                            attrsMap = JSONUtil.toBean(jsonExtObj, Map.class);
                        }
                    }
                } finally {
                    attrsMap.put("channel_biz", model.getChannelBiz());
                    attrsMap.put("channel_dc", model.getChannelDc());
                    attrsMap.put("create_time", model.getCreateTime());
                    attrsMap.put("car_series_code", model.getCarSeriesCode());
                    attrsMap.put("customer_name", model.getCustomerName());
                    attrsMap.put("customer_sex", model.getCustomerSex());
                    attrsMap.put("license_plate", model.getLicensePlate());
                    attrsMap.put("dealership_code_delivery", model.getDealershipCodeDelivery());
                    attrsMap.put("dealership_code_purchase", model.getDealershipCodePurchase());
                    attrsMap.put("dealership_code_return", model.getDealershipCodeReturn());
                    attrsMap.put("voc_data_source", config.getVocDataSource());   //voc配置的数据ID
                    attrsMap.put("project_id", config.getProjectId());   //voc配置的数据ID
                    jsonObj.remove("extAttrs3");
                    jsonObj.putOnce("bizExtAttrs3", attrsMap);
                }

                //原始数据ID
                jsonObj.set("id", model.getId());
                jsonObj.set("dataId", model.getDataId());
                //设置数据清洗接口必填字段  ->data
                jsonObj.set("type", config.getProcessContentType());  //加载配置
                jsonObj.set("channelId", channelType);
                jsonObj.set("publish_time", ObjUtil.isNull(model.getBizCreateTime().format(formatter)) ? null : model.getBizCreateTime().format(formatter));

                data.add(jsonObj);

            }
            final InsDataSourceModel pushData = InsDataSourceModel.builder()
                    .requestId(workId)
                    .clientId(clientId)
//                    .modelType("3")
                    .modelType(config.getProcessModelType())    //加载配置
                    .data(data)
                    .build();
//            log.info("{}", JSONUtil.toJsonStr(pushData, JSONConfig.create().setIgnoreNullValue(true)));

            final Result<?> rs = aysCoreServiceClient.batchPushData(pushData);
            log.info("本次成功推送到数据清洗服务【{}】[{}]",channelType ,data.size());
            if (StrUtil.equals(rs.getCode(), "200")) {
                try {
                    JSONObject jsonObj = JSONUtil.parseObj(rs.getResult());
                    log.info("requestId:{} workId:{}", workId, jsonObj.get("workId"));
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
                //将处理ids做记录
                final Set<String> ids = this.getIds(successfulDataset);
//                loadDataIdsCache.putAll(ids.stream().collect(Collectors.toMap(id -> id, id -> "1", (v1, v2) -> v2)));
                return ids;
            } else {
                throw new RuntimeException("【".concat(channelType).concat("】向数据清洗推送数据失败：").concat(rs+""));
            }
        }
        return Collections.EMPTY_SET;
    }

    /**
     * 记录成功数据标识
     */
   /* private void modifyDataStatus(String channelType, Set<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            throw new RuntimeException("【".concat(channelType).concat("】未读取到新数据"));
        }
        if (StrUtil.isBlank(channelType)) {
            throw new RuntimeException("【".concat(channelType).concat("】未配置token"));
        }

        //修改数据状态- 成功完成
//        inputDataService.modifyDataStatusToSuccessful(channelType, ids);
    }*/
    private Set<String> getIds(List<ChannelMetaDataModel> list) {
        return list.stream()
                .map(item -> {
                    JSONObject jsonObj = JSONUtil.parseObj(item);
                    return this.getId(jsonObj, "id");
                })
                .collect(Collectors.toSet());
    }

    private String getId(JSONObject jsonObj, String field) {
        return jsonObj.getStr(field);
    }

    @Override
    public boolean isAccess() {
        ChannelDatasetContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getWorkId()), "getWorkId cannot be empty");

        return true;
    }

    public static void main(String[] args) {
        /*Test2 t2 = new Test2();
        t2.setName("test222222222");
        Test t = new Test();
        t.setBizExt(new HashMap<>());
        t.setTest(t2);

        System.out.println(JSONUtil.toJsonStr(t));*/

        String json = "{\"biz_ext\": {\"brand1\": \"日产\",\"series1\": \"第14代轩逸\",\"desc1\": \"\"},}";

        JSONObject jsonObject = JSONUtil.parseObj(json);
        Test t3 = JSONUtil.toBean(jsonObject, Test.class);
        System.out.println(t3);
    }

    @ToString
    public static class Test {
        Test() {
        }

        Map<String, String> bizExt;

        public Map<String, String> getBizExt() {
            return bizExt;
        }

        public void setBizExt(Map<String, String> bizExt) {
            this.bizExt = bizExt;
        }

        public Test2 getTest() {
            return test;
        }

        public void setTest(Test2 test) {
            this.test = test;
        }

        Test2 test;


    }

    public static class Test2 {
        Test2() {
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        String name;
    }
}
