package com.voc.service.data.integration.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.voc.service.data.integration.api.ChannelExecutionResultService;
import com.voc.service.data.integration.api.IMppInputDataService;
import com.voc.service.data.integration.api.model.DataIntegrationRecordModel;
import com.voc.service.data.integration.nodes.context.ChannelDatasetContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.List;

/**
 * @Title: LoadChannelDatasetNode
 * @Package: com.voc.service.data.integration.nodes
 * @Description:
 * @Author: cuick
 * @Date: 2024/9/24 17:14
 * @Version:1.0
 */
@LiteflowComponent(id = "getChannelDatasetNode", name = "获取渠道数据集节点")
public class GetChannelDatasetNode extends NodeComponent {
    private static final Logger log = LoggerFactory.getLogger(GetChannelDatasetNode.class);
    @Autowired
    IMppInputDataService inputDataService;
    @Autowired
    ChannelExecutionResultService dataIntegrationRecordService;

    @Override
    public void process() throws Exception {
        ChannelDatasetContext context = this.getRequestData();
//        final List<DataIntegrationRecordModel> result = this.getCurrLoopObj();
        final List<DataIntegrationRecordModel> result = context.getChannelDataset();
        if (CollUtil.isEmpty(result)) {
            //是否结束整个流程
            super.setIsEnd(true);
            return;
        }
        /*final Set<DataIntegrationRecordModel> list = result.stream().map(item -> {
            //生成唯一ID  【渠道+数据ID】
            final String id = DigestUtil.md5Hex(new StringBuffer().append(item.getChannelType()).append(item.getDataId()).toString());

            return DataIntegrationRecordModel.builder()
                    .id(id)
                    .data(item.getData())
                    .dataId(item.getDataId())
                    .channelType(context.getChannelType())
                    .createTime(item.getCreateTime())
                    .workId(context.getWorkId())
                    .build();
        }).collect(Collectors.toSet());
        //本次需要处理的ID数据集
        final Set<String> ids = list.stream().map(DataIntegrationRecordModel::getId).collect(Collectors.toSet());
        //与数据库已完成数据进行比较 ，获取重复id集合
        final List<DataIntegrationRecordModel> finshishIdsData = dataIntegrationRecordService.finshishData(context.getChannelType(), ids, context.getStartDate(), context.getEndDate());
        final Map<String, DataIntegrationRecordModel> finshishIdsDataMap = finshishIdsData.stream().collect(Collectors.toMap(DataIntegrationRecordModel::getId, item -> item));
        final Set<String> finshishIds = finshishIdsData.stream().map(DataIntegrationRecordModel::getId).collect(Collectors.toSet());
        log.info("本地处理的ID集：{}，获取DB重复ID集合：{}", ids.size(), finshishIds.size());

        //过滤重复数据，获得本次执行ID集
//        final List<String> executeIds = CollUtil.subtractToList(ids, finshishIds);
        log.info("本次执行ID集：{}", finshishIds.size());
        final List<DataIntegrationRecordModel> executeList = list.stream()
                .filter(item -> {
                    if(finshishIdsDataMap.containsKey(item.getId()) && finshishIdsDataMap.get(item.getId()).getRetryCount() >= 3){ // 过滤掉已执行过的数据 //过滤掉重试3次的数据
                        return false;
                    }
                    return true;
                 })
                .map(item -> {                                          // 保留重试次数数据
                    if (finshishIdsDataMap.containsKey(item.getId())) {
                        item.setRetryCount(finshishIdsDataMap.get(item.getId()).getRetryCount());
                        item.setCreateTime(finshishIdsDataMap.get(item.getId()).getCreateTime());
                        log.debug("本条数据重复执行 {}", item.getId(), item.getRetryCount());
                    }
                    return item;
                })
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(executeList)) {
            log.info("数据过滤后，本次无可以执行数据集：{}，{}，{}，{}", result.size(), finshishIdsData.size(), finshishIds.size(), executeList.size());
            super.setIsEnd(true);
            return;
        }*/

        log.info("【{}】本次执行数据集：{}", context.getChannelType(), result.size());
        context.setChannelDataset(result);
    }

    private String getId(JSONObject jsonObj, String field) {
        return jsonObj.getStr(field);
    }


    @Override
    public boolean isAccess() {
        ChannelDatasetContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getChannelType()), "getChannelType cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getClientId()), "getChannelId cannot be empty");

        return true;
    }

}
