---
description: 
globs: .java
alwaysApply: false
---
# Java 17 企业级开发规范

## 核心原则
- 使用Java 17 LTS版本的现代语法特性提升开发效率
- 确保代码在SonarQube扫描中零问题（Bugs、Vulnerabilities、Security Hotspots、Code Smells）
- 测试覆盖率≥85%，重复代码率≤3%
- 遵循阿里巴巴Java开发手册规范
    - 官方文档链接：https://github.com/alibaba/p3c/blob/master/Java%E5%BC%80%E5%8F%91%E6%89%8B%E5%86%8C(%E9%BB%84%E5%B1%B1%E7%89%88).md

## 代码风格规范
- 代码缩进使用4个空格，禁止使用Tab
- 每行代码不超过120个字符
- 方法体不超过50行（SonarQube推荐）
- 类长度不超过500行
- 注释比例不低于15%
- 使用JavaDoc风格的类和方法注释
- 代码块必须使用大括号，即使只有一行
- 所有实体属性必须使用`@Schema`注解说明类或字段含义

## Java 17现代语法最佳实践

### 1. Records（记录类）- 不可变数据载体
```java
// ✅ 推荐：使用Records替代传统数据类
public record UserInfo(
    @NotNull String name,
    @Min(0) int age,
    @Email String email
) {
    // 紧凑构造器用于验证
    public UserInfo {
        if (name == null || name.isBlank()) {
            throw new IllegalArgumentException("Name cannot be null or blank");
        }
        Objects.requireNonNull(email, "Email cannot be null");
    }

    // 静态工厂方法
    public static UserInfo of(String name, int age, String email) {
        return new UserInfo(name, age, email);
    }
}

// ❌ 避免：传统的冗长数据类
public class UserInfo {
    private final String name;
    private final int age;
    private final String email;
    // ... 大量样板代码
}
```

### 2. Sealed Classes（密封类）- 受控继承
```java
// ✅ 推荐：使用密封类限制继承，提升类型安全
public sealed interface ApiResult<T> permits Success, Error, Loading {
    // 使用传统的if-else或多态方法替代pattern matching
    default String getStatus() {
        if (this instanceof Success) {
            return "SUCCESS";
        } else if (this instanceof Error) {
            return "ERROR";
        } else if (this instanceof Loading) {
            return "LOADING";
        }
        throw new IllegalStateException("Unknown ApiResult type");
    }
}

public final record Success<T>(@NotNull T data) implements ApiResult<T> {}
public final record Error<T>(@NotNull String message, @Nullable Throwable cause) implements ApiResult<T> {}
public final record Loading<T>() implements ApiResult<T> {}

// 业务异常也使用密封类
public sealed interface BusinessException extends Exception
    permits ValidationException, BusinessRuleException, ResourceNotFoundException {
    String getErrorCode();
    String getErrorMessage();
}
```

### 3. Switch Expressions - 简洁的分支处理
```java
// ✅ 推荐：使用switch表达式（Java 14+）
public String processApiResult(ApiResult<?> result) {
    if (result instanceof Success<?>success) {
        return "Success with data: " + success.data();
    } else if (result instanceof Error<?> error) {
        return "Error: " + error.message() +
            (error.cause() != null ? " (caused by: " + error.cause().getMessage() + ")" : "");
    } else if (result instanceof Loading<?>) {
        return "Still loading...";
    }
    throw new IllegalArgumentException("Unknown result type");
}

// ✅ 推荐：使用switch表达式处理枚举
public String getStatusMessage(OrderStatus status) {
    return switch (status) {
        case PENDING -> "Order is being processed";
        case CONFIRMED -> "Order has been confirmed";
        case SHIPPED -> "Order has been shipped";
        case DELIVERED -> "Order has been delivered";
        case CANCELLED -> "Order has been cancelled";
    };
}

// ✅ 推荐：类型检查和转换
public String categorizeInput(Object input) {
    if (input == null) {
        return "Null value";
    }

    if (input instanceof Integer i) {
        if (i > 0) return "Positive integer: " + i;
        if (i < 0) return "Negative integer: " + i;
        return "Zero";
    }

    if (input instanceof String s) {
        if (s.matches("\\d+")) return "Numeric string: " + s;
        if (!s.isBlank()) return "Non-empty string: " + s;
        return "Empty or blank string";
    }

    return "Unknown type: " + input.getClass().getSimpleName();
}
```

### 4. Text Blocks（文本块）- 多行字符串处理
```java
// ✅ 推荐：使用文本块处理SQL和JSON（Java 15+）
public class UserRepository {

    private static final String FIND_USERS_SQL = """
        SELECT u.id, u.name, u.email, u.created_at,
               p.title as profile_title, p.description
        FROM users u
        LEFT JOIN user_profiles p ON u.id = p.user_id
        WHERE u.active = true
          AND u.email IS NOT NULL
        ORDER BY u.created_at DESC
        LIMIT ?
        """;

    public String formatUserAsJson(User user) {
        return """
            {
                "id": %d,
                "name": "%s",
                "email": "%s",
                "profile": {
                    "title": "%s"
                },
                "created_at": "%s"
            }
            """.formatted(
                user.id(),
                escapeJson(user.name()),
                escapeJson(user.email()),
                escapeJson(user.profile().title()),
                user.createdAt().toString()
            );
    }

    private String escapeJson(String value) {
        return value != null ? value.replace("\"", "\\\"") : "";
    }
}
```

### 5. 并发处理最佳实践 - 高效的异步编程
```java
// ✅ 推荐：使用CompletableFuture和线程池处理并发任务
@Service
public class DataProcessingService {

    private static final Logger log = LoggerFactory.getLogger(DataProcessingService.class);
    private final ExecutorService executorService;

    public DataProcessingService() {
        this.executorService = Executors.newFixedThreadPool(
            Runtime.getRuntime().availableProcessors() * 2
        );
    }

    public CompletableFuture<List<ProcessResult>> processDataConcurrently(List<DataItem> items) {
        var futures = items.stream()
            .map(item -> CompletableFuture.supplyAsync(() -> {
                try {
                    return processItem(item);
                } catch (Exception e) {
                    log.error("Failed to process item: {}", item.id(), e);
                    return ProcessResult.error(item.id(), e.getMessage());
                }
            }, executorService))
            .collect(Collectors.toList());

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenApply(v -> futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList()));
    }

    private ProcessResult processItem(DataItem item) {
        // 模拟IO操作
        return ProcessResult.success(item.id(), "Processed");
    }

    @PreDestroy
    public void shutdown() {
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
```

## SonarQube质量标准 - 零问题保证

### 1. Bugs预防 - 消除所有代码错误
```java
// ✅ 空值安全处理
public class UserService {
    
    private final UserRepository userRepository;
    
    public UserService(UserRepository userRepository) {
        this.userRepository = Objects.requireNonNull(userRepository);
    }
    
    public Optional<User> findUser(String id) {
        if (id == null || id.isBlank()) {
            return Optional.empty();
        }
        
        try {
            return userRepository.findById(id);
        } catch (Exception e) {
            log.error("Failed to find user with id: {}", id, e);
            return Optional.empty();
        }
    }
    
    // ✅ 集合安全处理
    public List<User> getActiveUsers(List<String> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        return userIds.stream()
            .filter(Objects::nonNull)
            .filter(id -> !id.isBlank())
            .map(this::findUser)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .filter(User::isActive)
            .toList();
    }
}

// ✅ 资源自动管理
public class FileProcessor {
    
    public String readFileContent(Path filePath) throws IOException {
        Objects.requireNonNull(filePath, "File path cannot be null");
        
        if (!Files.exists(filePath)) {
            throw new FileNotFoundException("File not found: " + filePath);
        }
        
        // 自动资源管理
        try (var reader = Files.newBufferedReader(filePath, StandardCharsets.UTF_8)) {
            return reader.lines()
                .collect(Collectors.joining(System.lineSeparator()));
        }
    }
}

// ✅ 线程安全的单例
public enum ConfigManager {
    INSTANCE;
    
    private final Map<String, String> config = new ConcurrentHashMap<>();
    
    public String getProperty(String key) {
        return config.get(key);
    }
}
```

### 2. Vulnerabilities预防 - 安全第一
```java
// ✅ 安全的密码处理
@Service
public class AuthenticationService {
    
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder(12);
    private final SecureRandom secureRandom = new SecureRandom();
    
    public boolean validatePassword(char[] rawPassword, String encodedPassword) {
        if (rawPassword == null || rawPassword.length == 0) {
            return false;
        }
        
        try {
            var password = new String(rawPassword);
            return passwordEncoder.matches(password, encodedPassword);
        } finally {
            // 清除敏感数据
            Arrays.fill(rawPassword, '\0');
        }
    }
    
    public String hashPassword(char[] rawPassword) {
        if (rawPassword == null || rawPassword.length == 0) {
            throw new IllegalArgumentException("Password cannot be null or empty");
        }
        
        try {
            var password = new String(rawPassword);
            return passwordEncoder.encode(password);
        } finally {
            Arrays.fill(rawPassword, '\0');
        }
    }
    
    // ✅ 安全的令牌生成
    public String generateSecureToken() {
        var bytes = new byte[32];
        secureRandom.nextBytes(bytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes);
    }
}

// ✅ SQL注入防护
@Repository
public class UserRepository {
    
    private final JdbcTemplate jdbcTemplate;
    
    public UserRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
    
    // 参数化查询防止SQL注入
    public List<User> findUsersByName(String name) {
        if (name == null || name.isBlank()) {
            return Collections.emptyList();
        }
        
        var sql = """
            SELECT id, name, email, created_at 
            FROM users 
            WHERE name LIKE ? 
            AND active = true
            ORDER BY name
            """;
        
        return jdbcTemplate.query(sql, 
            (rs, rowNum) -> new User(
                rs.getLong("id"),
                rs.getString("name"),
                rs.getString("email"),
                rs.getTimestamp("created_at").toLocalDateTime()
            ),
            "%" + name + "%");
    }
}

// ✅ 输入验证和清理
@Component
public class InputSanitizer {
    
    private static final Pattern SAFE_STRING = Pattern.compile("^[a-zA-Z0-9\\s\\-_.@]+$");
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    
    public String sanitizeString(String input) {
        if (input == null) {
            return null;
        }
        
        var trimmed = input.trim();
        if (trimmed.isEmpty()) {
            return trimmed;
        }
        
        if (!SAFE_STRING.matcher(trimmed).matches()) {
            throw new ValidationException("Invalid characters in input");
        }
        
        // HTML转义防XSS
        return HtmlUtils.htmlEscape(trimmed);
    }
    
    public boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }
}
```

### 3. Security Hotspots预防 - 安全热点处理
```java
// ✅ 加密和解密
@Service
public class CryptoService {
    
    private static final String ALGORITHM = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 12;
    private static final int GCM_TAG_LENGTH = 16;
    
    private final SecretKey secretKey;
    private final SecureRandom secureRandom;
    
    public CryptoService(@Value("${app.encryption.key}") String base64Key) {
        this.secretKey = new SecretKeySpec(Base64.getDecoder().decode(base64Key), "AES");
        this.secureRandom = new SecureRandom();
    }
    
    public String encrypt(String plaintext) throws GeneralSecurityException {
        var cipher = Cipher.getInstance(ALGORITHM);
        var iv = new byte[GCM_IV_LENGTH];
        secureRandom.nextBytes(iv);
        
        var parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, parameterSpec);
        
        var ciphertext = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
        
        // 将IV和密文合并
        var encryptedData = new byte[GCM_IV_LENGTH + ciphertext.length];
        System.arraycopy(iv, 0, encryptedData, 0, GCM_IV_LENGTH);
        System.arraycopy(ciphertext, 0, encryptedData, GCM_IV_LENGTH, ciphertext.length);
        
        return Base64.getEncoder().encodeToString(encryptedData);
    }
}
```

### 4. Code Smells预防 - 代码质量保证
```java
// ✅ 降低圈复杂度，方法职责单一
@Service
public class OrderProcessingService {
    
    private final PaymentService paymentService;
    private final InventoryService inventoryService;
    
    // 复杂度 < 10
    public ApiResult<Order> processOrder(OrderRequest request) {
        return validateOrder(request)
            .flatMap(this::checkInventory)
            .flatMap(this::processPayment)
            .flatMap(this::createOrder);
    }
    
    private ApiResult<OrderRequest> validateOrder(OrderRequest request) {
        if (request == null) {
            return new Error<>("Order request cannot be null", null);
        }
        
        if (request.items().isEmpty()) {
            return new Error<>("Order must contain at least one item", null);
        }
        
        return new Success<>(request);
    }
    
    private ApiResult<OrderRequest> checkInventory(OrderRequest request) {
        for (var item : request.items()) {
            if (!inventoryService.isAvailable(item.productId(), item.quantity())) {
                return new Error<>("Insufficient inventory for product: " + item.productId(), null);
            }
        }
        return new Success<>(request);
    }
}

// ✅ 消除重复代码 - 抽取基类
public abstract class BaseService<T, ID> {
    
    protected final JpaRepository<T, ID> repository;
    protected final Validator validator;
    protected final Logger log;
    
    protected BaseService(JpaRepository<T, ID> repository, Validator validator) {
        this.repository = repository;
        this.validator = validator;
        this.log = LoggerFactory.getLogger(getClass());
    }
    
    public ApiResult<T> save(T entity) {
        var violations = validator.validate(entity);
        if (!violations.isEmpty()) {
            var errorMessage = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
            return new Error<>("Validation failed: " + errorMessage, null);
        }
        
        try {
            var saved = repository.save(entity);
            log.info("Entity saved successfully: {}", saved);
            return new Success<>(saved);
        } catch (Exception e) {
            log.error("Failed to save entity: {}", entity, e);
            return new Error<>("Save operation failed", e);
        }
    }
}
```

### 5. Coverage要求 - 测试覆盖率85%+
```java
// ✅ 完整的单元测试
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private Validator validator;
    
    @InjectMocks
    private UserService userService;
    
    @Test
    @DisplayName("Should create user successfully when valid input provided")
    void shouldCreateUserSuccessfully() {
        // Given
        var userRequest = new CreateUserRequest("John Doe", "<EMAIL>");
        var expectedUser = new User(1L, "John Doe", "<EMAIL>");
        
        when(validator.validate(any())).thenReturn(Collections.emptySet());
        when(userRepository.save(any(User.class))).thenReturn(expectedUser);
        
        // When
        var result = userService.createUser(userRequest);
        
        // Then
        assertThat(result)
            .isInstanceOf(Success.class)
            .extracting(Success::data)
            .usingRecursiveComparison()
            .isEqualTo(expectedUser);
        
        verify(userRepository).save(any(User.class));
        verify(validator).validate(any());
    }
    
    @ParameterizedTest
    @ValueSource(strings = {"", " ", "invalid-email"})
    @DisplayName("Should return error when invalid email provided")
    void shouldReturnErrorForInvalidEmail(String email) {
        // Given
        var userRequest = new CreateUserRequest("John", email);
        var violation = mock(ConstraintViolation.class);
        when(violation.getMessage()).thenReturn("Invalid email format");
        when(validator.validate(any())).thenReturn(Set.of(violation));
        
        // When
        var result = userService.createUser(userRequest);
        
        // Then
        assertThat(result)
            .isInstanceOf(Error.class)
            .extracting(Error::message)
            .asString()
            .contains("Validation failed");
        
        verify(userRepository, never()).save(any());
    }
    
    @Test
    @DisplayName("Should handle repository exception gracefully")
    void shouldHandleRepositoryException() {
        // Given
        var userRequest = new CreateUserRequest("John", "<EMAIL>");
        when(validator.validate(any())).thenReturn(Collections.emptySet());
        when(userRepository.save(any())).thenThrow(new DataAccessException("DB Error") {});
        
        // When
        var result = userService.createUser(userRequest);
        
        // Then
        assertThat(result)
            .isInstanceOf(Error.class)
            .extracting(Error::message)
            .isEqualTo("Save operation failed");
    }
}
```

## Spring Boot 3.x最佳实践

### 配置类现代化
```java
@Configuration
@EnableConfigurationProperties(AppProperties.class)
public class AppConfiguration {
    
    // 使用Records作为配置属性
    @ConfigurationProperties(prefix = "app")
    public record AppProperties(
        String name,
        String version,
        Security security,
        Database database
    ) {
        public record Security(String algorithm, int keyLength, Duration tokenExpiry) {}
        public record Database(String url, int maxConnections) {}
    }
    
    @Bean
    @ConditionalOnProperty(name = "app.feature.enable-async", havingValue = "true")
    public TaskExecutor taskExecutor() {
        var executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("app-async-");
        executor.initialize();
        return executor;
    }
}
```

### REST控制器现代化
```java
@RestController
@RequestMapping("/api/v1/users")
@Validated
@RequiredArgsConstructor
@Slf4j
public class UserController {
    
    private final UserService userService;
    
    @Operation(summary = "Create new user", description = "Creates a new user account")
    @ApiResponse(responseCode = "200", description = "查询成功")
    @PostMapping
    public ResponseEntity<ApiResponse<UserResponse>> createUser(
            @Valid @RequestBody CreateUserRequest request) {

        log.info("Creating user with email: {}", request.email());

        var result = userService.createUser(request);

        if (result instanceof Success<User> success) {
            log.info("User created successfully: {}", success.data().id());
            return ResponseEntity.ok(ApiResponse.success(UserResponse.from(success.data())));
        } else if (result instanceof Error<User> error) {
            log.warn("Failed to create user: {}", error.message());
            return ResponseEntity.badRequest().body(ApiResponse.error(error.message()));
        } else if (result instanceof Loading<User>) {
            return ResponseEntity.accepted().build();
        }

        throw new IllegalStateException("Unknown result type");
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<UserResponse>> getUser(
            @PathVariable @Min(1) Long id) {
        
        return userService.findById(id)
            .map(UserResponse::from)
            .map(user -> ResponseEntity.ok(ApiResponse.success(user)))
            .orElse(ResponseEntity.notFound().build());
    }
}
```

### 异常处理现代化
```java
// ✅ 统一异常处理
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ErrorResponse> handleBusinessException(BusinessException ex) {
        log.warn("Business exception: {}", ex.getErrorMessage());

        if (ex instanceof ValidationException validationEx) {
            return ResponseEntity.badRequest().body(
                ErrorResponse.validation(validationEx.getErrorCode(),
                    validationEx.getErrorMessage(), validationEx.getFields())
            );
        } else if (ex instanceof BusinessRuleException businessRuleEx) {
            return ResponseEntity.unprocessableEntity().body(
                ErrorResponse.businessRule(businessRuleEx.getErrorCode(),
                    businessRuleEx.getErrorMessage())
            );
        } else if (ex instanceof ResourceNotFoundException notFoundEx) {
            return ResponseEntity.notFound().body(
                ErrorResponse.notFound(notFoundEx.getErrorCode(),
                    notFoundEx.getErrorMessage(), notFoundEx.getResource())
            );
        }

        // 默认处理
        return ResponseEntity.internalServerError().body(
            ErrorResponse.internal(ex.getErrorCode(), ex.getErrorMessage())
        );
    }
    
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ErrorResponse> handleValidation(ConstraintViolationException ex) {
        var fieldErrors = ex.getConstraintViolations().stream()
            .collect(Collectors.toMap(
                violation -> violation.getPropertyPath().toString(),
                ConstraintViolation::getMessage
            ));
        
        return ResponseEntity.badRequest().body(
            ErrorResponse.validation("VALIDATION_ERROR", "Validation failed", fieldErrors)
        );
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGeneral(Exception ex) {
        log.error("Unexpected error", ex);
        return ResponseEntity.internalServerError().body(
            ErrorResponse.internal("INTERNAL_ERROR", "An unexpected error occurred")
        );
    }
}
```

## 命名规范
- **类名**：PascalCase（大驼峰）- `UserService`, `OrderController`
- **方法名**：camelCase（小驼峰）- `findUserById`, `processOrder`
- **变量名**：camelCase（小驼峰）- `userName`, `orderTotal`
- **常量**：UPPER_SNAKE_CASE - `MAX_RETRY_ATTEMPTS`, `DEFAULT_TIMEOUT`
- **包名**：全小写 - `com.company.service.user`
- **Records**：名词形式 - `UserInfo`, `OrderRequest`
- **密封类**：业务术语 - `PaymentResult`, `ValidationError`
- **Boolean变量**：`is`, `has`, `can`前缀 - `isActive`, `hasPermission`

## 代码质量保证指标
- **单元测试覆盖率**: ≥ 85%
- **集成测试覆盖率**: ≥ 70%
- **圈复杂度**: ≤ 10
- **方法长度**: ≤ 50行
- **类长度**: ≤ 500行
- **重复代码率**: ≤ 3%
- **技术债务比例**: ≤ 5%
- **代码异味**: 0个高级别问题
- **安全漏洞**: 0个
- **Bug**: 0个

## Java 17 LTS特性应用

### 1. 强封装和模块化
```java
// ✅ 推荐：使用模块化设计
module com.voc.user.service {
    requires java.base;
    requires spring.boot;
    requires spring.data.jpa;

    exports com.voc.user.service.api;

    // 不导出内部实现
    // com.voc.user.service.impl 包保持私有
}
```

### 2. 改进的NullPointerException信息
```java
// ✅ Java 17提供更详细的NPE信息
public class UserProcessor {

    public String processUser(User user) {
        // 如果user为null，Java 17会显示：
        // "Cannot invoke 'User.getName()' because 'user' is null"
        return user.getName().toUpperCase();
    }

    // ✅ 推荐：主动使用Objects.requireNonNull获得更好的错误信息
    public String processUserSafely(User user) {
        Objects.requireNonNull(user, "User cannot be null");
        Objects.requireNonNull(user.getName(), "User name cannot be null");
        return user.getName().toUpperCase();
    }
}
```

### 3. 性能优化特性
```java
// ✅ 推荐：利用Java 17的性能改进
@Service
public class DataService {

    // 使用更高效的随机数生成器
    private final RandomGenerator random = RandomGeneratorFactory
        .getDefault()
        .create();

    // 利用改进的垃圾收集器（G1GC默认优化）
    public List<String> generateLargeDataSet(int size) {
        return random.ints(size)
            .mapToObj(String::valueOf)
            .collect(Collectors.toList());
    }
}
```

## 工具生态推荐

### 开发工具
- **IDE**: IntelliJ IDEA 2023.3+（完整支持Java 17特性）
- **IDE插件**: SonarLint, SpotBugs, CheckStyle, Error Prone, Lombok
- **构建工具**: Gradle 8.5+ / Maven 3.9+
- **JDK发行版**: Eclipse Temurin 17 LTS, Amazon Corretto 17

### 代码质量
- **静态分析**: SonarQube 9.9+, SpotBugs 4.7+, PMD 6.55+, CheckStyle 10.12+
- **代码格式化**: Google Java Format, Prettier Java
- **依赖分析**: OWASP Dependency Check, Snyk, GitHub Dependabot

### 测试框架
- **单元测试**: JUnit 5.10+, TestNG 7.8+
- **Mock框架**: Mockito 5.5+, PowerMock（谨慎使用）
- **集成测试**: TestContainers 1.19+, WireMock 3.0+
- **性能测试**: JMH (Java Microbenchmark Harness)

### 监控和分析
- **性能监控**: Micrometer 1.11+, JProfiler, VisualVM, JFR
- **APM工具**: SkyWalking, Zipkin, Jaeger
- **JVM调优**: GCEasy, Eclipse MAT, JConsole

## Java 17 企业级部署建议

### JVM参数优化
```bash
# ✅ 推荐的Java 17生产环境JVM参数
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UseStringDeduplication
-XX:+OptimizeStringConcat
-Xms2g -Xmx4g
-XX:NewRatio=2
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/var/log/app/
-XX:+UseCompressedOops
-XX:+UseCompressedClassPointers
```

### 容器化最佳实践
```dockerfile
# ✅ 推荐的Java 17 Docker配置
FROM eclipse-temurin:17-jre-alpine

# 设置JVM参数以适应容器环境
ENV JAVA_OPTS="-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0"

# 使用非root用户运行应用
RUN addgroup -g 1001 appgroup && adduser -u 1001 -G appgroup -s /bin/sh -D appuser
USER appuser

COPY --chown=appuser:appgroup app.jar /app/app.jar
WORKDIR /app

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 安全配置建议
```java
// ✅ 推荐：Java 17安全配置
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .csrf(csrf -> csrf.disable())
            .headers(headers -> headers
                .frameOptions().deny()
                .contentTypeOptions().and()
                .httpStrictTransportSecurity(hsts -> hsts
                    .maxAgeInSeconds(31536000)
                    .includeSubdomains(true)))
            .build();
    }
}
```

## 迁移指南：从Java 8/11到Java 17

### 1. 语法升级建议
```java
// ❌ Java 8风格
List<String> names = users.stream()
    .map(user -> user.getName())
    .filter(name -> name != null && !name.isEmpty())
    .collect(Collectors.toList());

// ✅ Java 17风格
List<String> names = users.stream()
    .map(User::getName)
    .filter(Objects::nonNull)
    .filter(Predicate.not(String::isBlank))
    .toList(); // Java 16+的便捷方法
```

### 2. 异常处理现代化
```java
// ❌ 传统异常处理
public String readFile(String path) {
    try {
        return Files.readString(Paths.get(path));
    } catch (IOException e) {
        throw new RuntimeException("Failed to read file: " + path, e);
    }
}

// ✅ 现代异常处理
public String readFile(String path) {
    try {
        return Files.readString(Path.of(path), StandardCharsets.UTF_8);
    } catch (IOException e) {
        throw new UncheckedIOException("Failed to read file: " + path, e);
    }
}
```

### 3. 集合操作优化
```java
// ❌ 传统集合操作
Map<String, List<User>> usersByDepartment = new HashMap<>();
for (User user : users) {
    usersByDepartment.computeIfAbsent(user.getDepartment(), k -> new ArrayList<>())
        .add(user);
}

// ✅ 现代集合操作
Map<String, List<User>> usersByDepartment = users.stream()
    .collect(Collectors.groupingBy(User::getDepartment));
```

## 总结

Java 17作为LTS版本，提供了稳定性和现代化特性的完美平衡。通过采用Records、Sealed Classes、Text Blocks、Switch Expressions等特性，可以显著提升代码的可读性和维护性。同时，严格遵循SonarQube质量标准和测试覆盖率要求，确保代码质量达到企业级标准。
