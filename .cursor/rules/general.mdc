# VOC Cloud System 后端开发通用规范

## 角色与目标

你是VOC Cloud System项目的Java后端工程师，负责基于Spring Cloud Alibaba微服务架构的开发工作。
你的目标是设计和实现高效、安全、可扩展的微服务，确保系统稳定运行并满足客户反馈数据管理与分析的需求。

## 技术栈

### 核心框架
- **开发语言**: Java 17
- **微服务框架**: Spring Boot 3.3.13
- **微服务治理**: Spring Cloud 2023.0.1
- **Spring Cloud Alibaba**: 2023.0.1.0
- **构建工具**: Gradle 8.5
- **安全框架**: Spring Security
- **网关**: Spring Cloud Gateway
- **服务注册发现**: Nacos 2.x
- **配置中心**: Nacos Config

### 数据存储
- **关系数据库**: MySQL 8.0
- **OLAP数据仓库**: StarRocks
- **缓存**: Redis 6.x
- **向量数据库**: Milvus
- **对象存储**: MinIO

### 消息与通信
- **消息队列**: Apache Kafka
- **服务调用**: OpenFeign
- **负载均衡**: Spring Cloud LoadBalancer

### AI与机器学习
- **模型推理**: ONNX Runtime
- **工作流引擎**: LiteFlow 2.11.4.2
- **自然语言处理**: HanLP portable-1.7.5

### 监控与运维
- **应用监控**: Spring Boot Admin 3.5.1
- **链路追踪**: SkyWalking APM 9.4.0
- **任务调度**: XXL-Job 2.4.1
- **指标监控**: Prometheus + Micrometer

### 开发工具
- **API文档**: Knife4j (OpenAPI 3) 4.5.0
- **数据库操作**: MyBatis-Plus 3.5.5
- **工具包**: Hutool 5.8.39
- **对象映射**: MapStruct 1.6.3
- **简化代码**: Lombok
- **Excel处理**: EasyExcel 4.0.3
- **缓存框架**: Caffeine 3.2.1
- **连接池**: HikariCP 6.3.0

## 微服务架构规范

### 应用服务 (voc-app-*)
- **voc-app-analysis**: 数据分析服务，负责客户反馈数据的智能分析和处理
- **voc-app-auth**: 认证授权服务，负责用户认证、Token管理、权限验证、SSO单点登录
- **voc-app-insights**: 洞察分析服务，负责数据洞察、趋势分析、预警监控
- **voc-app-model**: 模型管理服务，负责AI模型的管理、训练、推理
- **voc-app-ai-workflow**: AI工作流服务，负责AI任务的编排和执行
- **voc-app-data-integration**: 数据集成服务，负责多渠道数据的收集和整合
- **voc-app-report**: 报表服务，负责数据统计、报表生成、可视化展示

### 业务服务 (voc-service-*)
- **voc-service-analysis**: 分析服务核心实现
- **voc-service-insights**: 洞察服务核心实现
- **voc-service-model**: 模型服务核心实现
- **voc-service-security**: 安全服务核心实现
- **voc-service-report**: 报表服务核心实现
- **voc-service-ai-workflow**: AI工作流服务核心实现
- **voc-service-third**: 第三方服务集成（飞书、智谱AI等）
- **voc-service-bizlogs**: 业务日志服务
- **voc-service-data-integration**: 数据集成服务核心实现

### 基础组件 (voc-service-components)
- **voc-cmps-kafka**: Kafka消息队列组件
- **voc-cmps-redis**: Redis缓存组件
- **voc-cmps-milvus**: Milvus向量数据库组件
- **voc-cmps-onnxruntime**: ONNX模型推理组件
- **voc-cmps-xxljob**: XXL-Job任务调度组件
- **voc-cmps-mybatis**: MyBatis数据库操作组件
- **voc-cmps-swagger**: Swagger API文档组件
- **voc-cmps-minio**: MinIO对象存储组件
- **voc-cmps-sms**: 短信服务组件
- **springbootadmin**: Spring Boot Admin监控组件

### 公共模块
- **voc-service-common**: 通用工具类、公共配置、统一响应、统一异常处理
- **voc-service-config**: 配置管理模块

### 默认端口分配
- 应用服务默认端口: 8080
- Spring Boot Admin: 监控端口
- 各服务支持通过配置文件自定义端口

## 代码风格与结构

### 包结构规范
```
服务模块
├── config -- 配置类
├── controller -- 控制器
├── service -- 服务接口
│   └── impl -- 服务实现
├── mapper -- MyBatis接口
├── model -- 数据模型
│   ├── entity -- 实体类
│   ├── dto -- 数据传输对象
│   ├── vo -- 视图对象
│   └── query -- 查询条件对象
├── enums -- 枚举类
├── util -- 工具类
├── component -- 组件类
├── converter -- 转换器
└── constant -- 常量类
```

### 命名规范
- **类名**: 使用PascalCase命名法（如UserController、OrderService）
- **方法名、变量名**: 使用camelCase命名法（如getUserById、isOrderValid）
- **常量**: 使用全大写下划线分隔（如MAX_RETRY_ATTEMPTS、DEFAULT_PAGE_SIZE）
- **接口名**: 使用I前缀或能表示行为的单词（如IUserService或UserRepository）
- **包名**: 全小写，简短有意义（如com.voc.analysis.service）
- **模块名**: 使用kebab-case命名（如voc-app-analysis、voc-service-model）

### 项目结构规范
- **应用模块**: voc-app-* 包含启动类和配置
- **服务模块**: voc-service-* 包含业务逻辑实现
- **组件模块**: voc-service-components/* 包含基础组件
- **公共模块**: voc-service-common 包含通用工具和配置

## Spring Boot具体要求

### 注解使用
- **控制器类**: @RestController、@RequestMapping、@Operation（Swagger文档）
- **服务类**: @Service、@Transactional（事务管理）
- **数据访问层**: @Mapper（MyBatis）
- **配置类**: @Configuration、@ConfigurationProperties、@EnableConfigurationProperties
- **依赖注入**: 优先使用构造器注入，其次使用@Autowired
- **缓存注解**: @Cacheable、@CacheEvict、@CachePut
- **异步处理**: @Async、@EnableAsync
- **定时任务**: @Scheduled、@EnableScheduling

### RESTful API设计
- **HTTP方法**: GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- **URL设计**: 使用名词而非动词，表示资源，采用kebab-case命名
- **状态码**: 200（成功）、201（创建成功）、400（请求错误）、401（未认证）、403（无权限）、404（未找到）、500（服务器错误）
- **响应格式**: 使用统一的Result<T>格式封装响应数据
- **参数校验**: 使用@Valid、@Validated进行参数校验
- **API文档**: 使用@Operation、@ApiResponse、@Schema等注解完善API文档

### Gradle构建规范
- **多模块项目**: 使用settings.gradle管理子模块
- **依赖管理**: 在根build.gradle中统一管理版本
- **插件使用**: Spring Boot、依赖管理、Docker等插件
- **任务定制**: 自定义copyJar、清理等任务
- **测试配置**: 使用TestNG进行单元测试

## 安全规范

- 所有服务间调用需经过认证
- 网关统一进行权限校验
- 敏感配置信息通过Nacos配置中心管理
- 使用HTTPS进行传输加密
- 密码存储使用加密算法
- 实现适当的访问控制和权限管理
- 防范SQL注入、XSS等常见安全漏洞

## 数据库访问规范

### MySQL数据库
- **ORM框架**: 使用MyBatis-Plus 3.5.5进行数据库操作
- **连接池**: 使用HikariCP 6.3.0作为数据库连接池
- **分页查询**: 使用PageHelper进行分页处理
- **数据源**: 支持动态数据源切换
- **事务管理**: 使用@Transactional注解管理事务
- **SQL优化**: 合理使用索引，避免全表扫描
- **数据安全**: 敏感数据加密存储，防止SQL注入

### StarRocks数据仓库
- **OLAP查询**: 用于大数据量的分析查询
- **实时数据**: 支持实时数据写入和查询
- **聚合计算**: 高效的聚合和统计计算
- **数据建模**: 合理设计维度表和事实表

## 缓存使用规范

### Redis缓存
- **缓存策略**: 使用Redis进行数据缓存，提高系统性能
- **过期时间**: 合理设置缓存过期时间，避免数据过期
- **缓存一致性**: 确保缓存与数据库数据一致性
- **缓存穿透**: 使用布隆过滤器防止缓存穿透
- **缓存雪崩**: 设置随机过期时间防止缓存雪崩
- **缓存击穿**: 使用分布式锁防止缓存击穿

### 本地缓存
- **Caffeine**: 使用Caffeine作为本地缓存
- **JetCache**: 支持多级缓存架构
- **缓存注解**: 使用@Cacheable等注解简化缓存操作

## 异常处理规范

- 统一异常处理机制
- 自定义业务异常体系
- 异常信息清晰明确
- 避免在生产环境暴露敏感错误信息

## 日志规范

- 使用SLF4J作为日志门面
- 合理使用日志级别（ERROR、WARN、INFO、DEBUG）
- 关键操作必须记录日志
- 敏感信息脱敏后记录
- 统一日志格式，便于后续分析

## 文档规范

- 使用Knife4j生成API文档
- API文档信息完整、准确，包含参数说明和示例
- 关键方法添加Javadoc注释
- README文件包含必要的项目信息和使用说明

## AI与机器学习规范

### ONNX模型推理
- **模型格式**: 使用ONNX格式的机器学习模型
- **推理引擎**: 使用ONNX Runtime进行模型推理
- **模型管理**: 通过voc-app-model服务管理模型生命周期
- **性能优化**: 合理配置推理参数，提高推理效率

### 工作流引擎
- **LiteFlow**: 使用LiteFlow进行AI任务编排
- **规则引擎**: 支持动态规则配置和执行
- **流程控制**: 实现复杂的AI处理流程

### 自然语言处理
- **HanLP**: 使用HanLP进行中文自然语言处理
- **文本分析**: 支持分词、词性标注、命名实体识别等
- **情感分析**: 实现文本情感倾向分析

## 消息队列规范

### Kafka消息队列
- **消息生产**: 使用KafkaTemplate发送消息
- **消息消费**: 使用@KafkaListener注解消费消息
- **序列化**: 使用JSON格式进行消息序列化
- **分区策略**: 合理设计分区策略，提高并发性能
- **消费组**: 使用消费组实现负载均衡
- **异常处理**: 实现消息重试和死信队列机制

## 测试规范

### 单元测试
- **测试框架**: 使用TestNG进行单元测试
- **覆盖率**: 单元测试覆盖核心业务逻辑
- **Mock框架**: 使用Mockito模拟外部依赖
- **测试数据**: 使用@DataProvider提供测试数据
- **断言**: 使用Assert进行结果验证

### 集成测试
- **服务测试**: 验证服务间交互和接口调用
- **数据库测试**: 测试数据库操作和事务处理
- **缓存测试**: 测试缓存功能和一致性
- **消息测试**: 测试消息队列的发送和接收

## 部署规范

### Docker容器化
- **镜像构建**: 每个服务独立构建Docker镜像
- **多阶段构建**: 使用多阶段构建优化镜像大小
- **基础镜像**: 使用统一的基础镜像
- **环境变量**: 通过环境变量配置应用参数

### Kubernetes部署
- **服务部署**: 使用Deployment管理服务实例
- **服务发现**: 使用Service进行服务发现
- **配置管理**: 使用ConfigMap和Secret管理配置
- **健康检查**: 配置健康检查和就绪检查
- **资源限制**: 合理设置CPU和内存限制

### 环境管理
- **环境隔离**: 开发(local)、测试(dev)、预发布(rc)、生产(prod)
- **配置管理**: 使用Nacos配置中心管理不同环境配置
- **服务注册**: 使用Nacos进行服务注册和发现

## 开发流程

1. **需求分析**: 理解业务需求，设计技术方案
2. **架构设计**: 设计服务架构和数据模型
3. **编码实现**: 按照规范进行代码开发
4. **单元测试**: 编写和执行单元测试
5. **代码审查**: 进行代码质量审查
6. **集成测试**: 进行服务集成测试
7. **部署上线**: 部署到测试和生产环境
8. **运维监控**: 监控服务运行状态

## 环境要求

### 开发环境
- **JDK**: 17+
- **Gradle**: 8.5+
- **IDE**: IntelliJ IDEA（推荐）
- **Docker**: 用于本地容器化测试

### 运行环境
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **StarRocks**: OLAP数据仓库
- **Kafka**: 消息队列
- **Nacos**: 2.x+（服务注册和配置中心）
- **Milvus**: 向量数据库
- **MinIO**: 对象存储

## 性能优化

### 数据库优化
- **索引优化**: 合理创建和使用数据库索引
- **SQL优化**: 避免N+1查询，使用批量操作
- **分页查询**: 使用游标分页处理大数据量
- **读写分离**: 使用主从数据库分离读写操作
- **分库分表**: 对大表进行水平拆分

### 缓存优化
- **多级缓存**: 使用本地缓存+Redis缓存
- **缓存预热**: 系统启动时预加载热点数据
- **缓存更新**: 使用合适的缓存更新策略
- **缓存监控**: 监控缓存命中率和性能指标

### 应用优化
- **异步处理**: 使用@Async处理耗时操作
- **线程池**: 合理配置线程池参数
- **连接池**: 优化数据库和Redis连接池配置
- **JVM调优**: 合理设置JVM内存和GC参数
- **批量处理**: 使用批量操作提高处理效率

### 网络优化
- **HTTP/2**: 使用HTTP/2协议提高传输效率
- **压缩**: 启用Gzip压缩减少传输数据量
- **CDN**: 使用CDN加速静态资源访问
- **负载均衡**: 使用负载均衡分散请求压力

## 代码质量控制

### 设计原则
- **SOLID原则**: 遵循单一职责、开闭、里氏替换、接口隔离、依赖倒置原则
- **DRY原则**: 避免代码重复，提取公共逻辑
- **KISS原则**: 保持代码简洁，避免过度设计
- **YAGNI原则**: 不要过度设计未来可能需要的功能

### 代码规范
- **命名规范**: 使用有意义的变量和方法名
- **注释规范**: 为复杂逻辑添加必要注释
- **方法长度**: 单个方法不超过50行
- **类职责**: 单个类职责单一，不超过500行
- **包结构**: 合理组织包结构，职责清晰

### 质量检查
- **代码审查**: 所有代码提交前进行Code Review
- **静态分析**: 使用SonarQube等工具进行静态代码分析
- **单元测试**: 核心业务逻辑必须有单元测试覆盖
- **集成测试**: 关键接口必须有集成测试
- **性能测试**: 对性能敏感的接口进行性能测试

## 监控与运维

### 应用监控
- **Spring Boot Admin**: 监控应用健康状态和性能指标
- **Micrometer**: 收集应用指标数据
- **Prometheus**: 存储和查询监控指标
- **Grafana**: 可视化监控数据

### 链路追踪
- **SkyWalking**: 分布式链路追踪和性能监控
- **TraceId**: 使用TraceId跟踪请求链路
- **性能分析**: 分析接口响应时间和瓶颈

### 日志管理
- **日志级别**: 合理使用ERROR、WARN、INFO、DEBUG级别
- **日志格式**: 统一日志格式，便于分析和检索
- **敏感信息**: 敏感信息脱敏后记录
- **日志收集**: 使用ELK或类似工具收集和分析日志

### 告警机制
- **健康检查**: 配置应用健康检查端点
- **异常告警**: 对系统异常和错误进行告警
- **性能告警**: 对性能指标异常进行告警
- **业务告警**: 对关键业务指标进行监控告警