---
description: 
globs: .yml
alwaysApply: false
---
# Spring Boot YAML 配置规范

## 配置文件类型
- `bootstrap.yml`: 用于应用程序上下文的引导阶段配置
- `application.yml`: 应用程序的主要配置文件
- `application-{profile}.yml`: 特定环境的配置文件（如 dev, test, prod）

## 通用结构和原则
- 使用缩进表示层级关系（2个空格）
- 使用有意义的命名和结构组织配置
- 避免重复配置，利用配置继承
- 敏感信息使用外部配置或环境变量
- 使用注释说明配置的用途
- 遵循 YAML 语法，注意空格和缩进

## bootstrap.yml 内容规范
- 配置服务名称、配置中心等引导信息
- 保持精简，只包含启动必需的配置
- 常见配置项：
  ```yaml
  spring:
    application:
      name: service-name
    cloud:
      nacos:
        config:
          server-addr: 127.0.0.1:8848
          file-extension: yaml
  ```

## application.yml 结构规范
- 按照功能模块划分配置块
- 常用配置块顺序：
  1. 服务器配置
  2. Spring 核心配置
  3. 数据源配置
  4. 缓存配置
  5. 安全配置
  6. 日志配置
  7. 自定义配置

## 服务器配置示例
```yaml
server:
  port: 8080
  servlet:
    context-path: /api
  tomcat:
    max-threads: 200
    connection-timeout: 5000
```

## Spring 配置示例
```yaml
spring:
  application:
    name: service-name
  profiles:
    active: dev
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
```

## 数据源配置示例
```yaml
spring:
  datasource:
    url: ******************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
```

## MyBatis-Plus 配置示例
```yaml
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.futong.domain
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

## Redis 配置示例
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password:
    database: 0
    timeout: 10000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
```

## 日志配置示例
```yaml
logging:
  level:
    root: info
    com.futong: debug
  file:
    name: logs/application.log
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
```

## 微服务配置示例
```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
    gateway:
      routes:
        - id: service-route
          uri: lb://service-name
          predicates:
            - Path=/service/**
```

## 安全配置示例
```yaml
spring:
  security:
    user:
      name: admin
      password: admin
```

## 多环境配置
- 使用 Spring profiles 实现多环境配置
- 在 application.yml 中指定激活的环境
- 为不同环境创建独立的配置文件：
  - application-dev.yml
  - application-test.yml
  - application-prod.yml

## 配置优先级
优先级从高到低：
1. 命令行参数
2. java:comp/env 的 JNDI 属性
3. Java 系统属性 (System.getProperties())
4. 操作系统环境变量
5. application-{profile}.yml 配置
6. application.yml 配置
7. bootstrap-{profile}.yml 配置
8. bootstrap.yml 配置

## 最佳实践
- 避免硬编码配置，使用配置外部化
- 敏感信息（密码、密钥）不要明文配置
- 使用占位符引用其他配置项 `${property.name}`
- 合理使用默认值 `${property.name:default}`
- 配置项命名使用小写字母和连字符
- 相关配置项放在一起，便于维护

