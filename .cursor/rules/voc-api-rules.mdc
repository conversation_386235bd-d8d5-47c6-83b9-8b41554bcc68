---
description: 
globs: 
alwaysApply: true
---
# VOC API开发规范

## 1. 接口通用规范

### 1.1 接口响应规范
- 统一使用`Result.OK`作为响应模型
- 接口返回说明必须使用 @ApiResponse(responseCode = "200", description = "查询成功")
- 响应数据必须使用VO类封装，且字段数量必须严格匹配需求

### 1.2 接口命名规范
- 接口方法名使用`get`前缀
- 接口URL使用kebab-case命名方式

### 1.3 接口文档规范
- 必须使用`@Operation`注解说明接口功能
- 必须使用`@ApiResponse`注解说明响应结构
- 必须使用`@Schema`注解说明VO类字段含义

## 2. 数据查询规范

### 2.1 报表服务的查询条件规范
- 统一使用`xxxxQueryModel`作为查询条件模型
- 时间维度定义：
  - -1：日
  - 0：周
  - 1：月
  - 2：季
  - 3：年

### 2.2 报表服务的数据计算规范
- 环比计算公式：`[(本期值 - 上期值) / 上期值] × 100%`
- 体验值计算公式：`(正面数 - 负面数) / (正面数 + 负面数) * 100`
- 日均计算公式：(时间维度范围内的数据量) / 时间内的天数
- 所有计算结果保留两位小数（四舍五入）

## 3. 代码实现规范

### 3.1 架构规范
- 遵循Controller-Service-Mapper三层架构
- 使用MyBatis-Plus进行数据库操作
- 【强制】SQL实现规范：
  - 必须使用MyBatis-Plus XML文件来实现所有SQL计算逻辑
  - 禁止在Java代码中拼接SQL语句
  - 禁止使用MyBatis-Plus的Wrapper条件构造器
  - 必须将SQL语句定义在XML文件中
  - 必须提取公共的查询条件到XML文件中
  - 必须提取可复用的SQL片段到XML文件中
  - 所有查询条件必须使用ComQueryModel（公共查询条件）
  - XML文件命名必须与Mapper接口对应
  - XML文件必须放在resources/mapper目录下
  - 必须使用resultMap映射结果集
  - 必须使用parameterType指定参数类型
  - 必须使用动态SQL标签（if、choose、when、otherwise等）
  - 必须添加SQL注释说明查询用途
  - 必须优化SQL性能，避免全表扫描
  - 必须使用索引字段作为查询条件
  - 必须使用分页查询处理大数据量
  - 必须使用EXPLAIN分析SQL执行计划
  - 实体类和VO类必须使用Lombok的@Data注解，禁止手动添加getter和setter方法
  - 使用Lombok时，确保当前开发工具中安装了Lombok插件
  - 使用Lombok的@Builder注解时，必须同时使用@AllArgsConstructor和@NoArgsConstructor注解
  - 【强制】MyBatis结果集接收规范：
    - 必须使用强类型的实体类（VO/POJO/JavaBean）接收XML SQL查询结果集
    - 禁止使用Map<String, Object>接收SQL查询结果
    - 必须为每个查询结果定义对应的实体类或VO类
    - 实体类必须与数据库字段严格对应
    - 必须使用resultMap进行结果映射配置

### 3.2 接口开发规范
- 【强制】VO类规范：
  - 必须严格按照回参字段创建VO
  - 禁止添加额外字段
  - 必须使用`@Schema`注解说明字段含义
  - 必须使用`@Data`注解
  - 必须使用`@Schema`注解说明类用途
  - 禁止使用Result<List<Map<String, Object>>> 作为接口的返回结果集
  - 禁止在Mapper接口方法中使用Map<String, Object>作为返回类型

- 【强制】接口规范：
  - 入参必须使用现有的`xxxxQueryModel`公共查询条件(有特别说明除外)
  - 必须在现有的报表管理相关接口中增加新接口(有特别说明除外)
  - 禁止创建新的Controller和Service接口(有特别说明除外)
  - 接口方法名必须使用`get`前缀
  - 接口URL必须使用kebab-case命名方式

### 3.3 性能优化规范
- 【强制】性能要求：
  - 有多个查询数据库的操作时要用多线程实现
  - 必须保证接口性能达到最高
  - 计算逻辑必须使用SQL实现
  - 必须方便后续复用
  - 必须进行代码优化分析
  - 必须进行接口性能优化
  - 必须使用索引优化查询性能
  - 必须避免重复计算
  - 必须使用缓存优化
  - 必须使用分页查询
  - 必须使用EXPLAIN分析执行计划

### 3.4 测试规范
- 【强制】测试要求：
  - 必须编写单元测试
  - 必须测试不同时间维度的场景
  - 必须测试各种查询条件组合
  - 必须测试边界条件
  - 必须测试异常情况
  - 【强制】接口变更时必须同步修改对应的单元测试
  - 【强制】单元测试修改后必须执行验证，确保测试通过
  - 【强制】单元测试覆盖率不得低于80%
  - 【强制】每个接口的单元测试必须包含正常场景、异常场景、边界值测试
  - 【强制】单元测试必须使用@Test注解，并包含测试方法的描述说明
  - 【强制】单元测试失败时，禁止提交代码到版本库

### 3.5 swagger文档规范
- 【强制】文档要求：
  - 必须swagger更新接口文档
  - 必须用swagger说明接口功能
  - 必须用swagger说明请求参数
  - 必须用swagger说明响应结构

## 4. 数据模型规范

### 4.1 VO类规范
- 类名以VO结尾
- 必须使用`@Data`注解
- 必须使用`@Schema`注解说明类用途
- 字段必须使用`@Schema`注解说明含义
- 【强制】字段命名规范：
  - 字段名称长度不得超过30个字符
  - 优先使用简洁明了的命名，避免冗余词汇
  - 可使用常见缩写替代冗长词汇（如：YoY替代YearOnYear，MoM替代MonthOnMonth）
  - 必须保持命名的可读性和业务含义的准确性

### 4.2 实体类规范
- 必须使用`@TableName`注解指定表名
- 必须使用`@Schema`注解说明类用途
- 字段必须使用`@Schema`注解说明含义
- 【强制】字段命名规范：
  - 字段名称长度不得超过30个字符
  - 优先使用简洁明了的命名，避免冗余词汇
  - 可使用常见缩写替代冗长词汇（如：YoY替代YearOnYear，MoM替代MonthOnMonth）
  - 必须保持命名的可读性和业务含义的准确性

## 5. 测试规范

### 5.1 单元测试规范
- 【强制】必须覆盖所有业务逻辑
- 【强制】必须测试边界条件
- 【强制】必须测试异常情况
- 【强制】单元测试必须独立运行，不依赖外部环境
- 【强制】单元测试必须可重复执行，每次结果一致
- 【强制】单元测试必须使用断言验证结果
- 【强制】单元测试必须包含测试数据的准备和清理
- 【强制】单元测试方法命名必须清晰表达测试意图
- 【强制】单元测试必须使用Mock技术模拟外部依赖
- 【强制】单元测试执行时间不得超过30秒

### 5.2 接口测试规范
- 【强制】必须测试不同时间维度的场景
- 【强制】必须测试各种查询条件组合
- 【强制】必须测试参数校验逻辑
- 【强制】必须测试权限控制
- 【强制】必须测试接口性能指标
- 【强制】必须验证响应数据格式和内容
- 【强制】必须测试接口幂等性（如适用）
- 【强制】必须提供接口测试数据样例
- 【强制】必须验证接口文档与实际实现的一致性
- 【强制】接口新建或修改后都要执行 mvn clean test 命令验证是否能成功，未通过则必须修复，直到验证成功

### 5.3 测试变更管理规范
- 【强制】接口新增时，必须同时新增对应的单元测试
- 【强制】接口修改时，必须同步修改相关的单元测试
- 【强制】接口删除时，必须同时删除相关的单元测试
- 【强制】VO类变更时，必须更新相关的测试数据
- 【强制】SQL逻辑修改时，必须验证相关测试用例
- 【强制】测试变更必须经过Code Review
- 【强制】测试变更必须在CI/CD流程中验证通过
- 【强制】破坏性变更必须提供向后兼容的测试方案

### 5.4 测试执行规范
- 【强制】每次代码提交前必须执行全部单元测试
- 【强制】CI/CD流程中测试失败必须阻止部署
- 【强制】每日必须执行完整的回归测试
- 【强制】测试失败时必须立即修复，不得延后处理
- 【强制】测试环境必须与生产环境保持一致
- 【强制】测试数据必须与生产数据结构保持一致
- 【强制】性能测试必须在类生产环境中执行
- 【强制】测试结果必须记录并可追溯

## 6. 文档规范

### 6.1 接口文档规范
- 必须说明接口功能
- 必须说明请求参数
- 必须说明响应结构
- 必须提供示例


## 7. 体验指数评价规则

### 7.1 情感体验指数评价
- 表现不佳：-100 ≤ 体验指数 ≤ -60
- 有待提高：-60 < 体验指数 ≤ 0
- 表现良好：0 < 体验指数 ≤ 20
- 表现优秀：20 < 体验指数 ≤ 100

### 7.2 服务体验指数
- 计算范围：所有业务标签（一级标签除智能化体验和产品体验）的声音
- 保留两位小数四舍五入

### 7.3 产品体验指数
- 计算范围：一级标签为智能化体验和产品体验标签的声音
- 保留两位小数四舍五入

## 8. 接口注解与路由命名规范

### 8.1 响应注解统一
- 所有接口返回说明必须统一使用：
  `@ApiResponse(responseCode = "200", description = "查询成功")`
- 除非有特殊说明或自定义响应结构，否则无需重复写content。
- 仅有一种响应结构时，直接用单个@ApiResponse即可。

### 8.2 路由命名规范
- 接口URL必须使用kebab-case（短横线分隔小写单词）。
- 路由命名应简洁明了，避免过长，建议不超过4个单词，但以表达清楚业务为主。
- 接口方法名建议用`get`前缀，表达查询类接口的意图。

### 8.3 其它相关要求
- 必须使用`@Operation`注解说明接口功能。
- 响应模型统一使用`Result.OK`，响应数据必须用VO类封装，字段严格匹配需求。
- 接口文档必须说明接口功能、请求参数、响应结构，并提供示例。

#### 规范示例

```java
@Operation(summary = "获取用户详情", description = "根据用户ID获取详细信息")
@ApiResponse(responseCode = "200", description = "查询成功")
@PostMapping("/user-detail")
public Result<UserDetailVO> getUserDetail(@RequestBody UserDetailQueryModel queryModel) {
    // ...
}
```
