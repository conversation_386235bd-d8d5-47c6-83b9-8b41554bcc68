spring.application.name: voc-service-stats

spring.cloud.nacos.config.server-addr: ${NACOS_SERVER_ADDR_URLS:nacos-products-headless.middleware-test.svc.cluster.local:8848}
spring.cloud.nacos.config.file-extension: yml
spring.cloud.nacos.config.group: ${NACOS_CONFIG_GROUP:DEFAULT_GROUP}
spring.cloud.nacos.config.namespace: ${NACOS_CONFIG_NAMESPACE:nissan-dndc-test}
spring.cloud.nacos.config.username: develop
spring.cloud.nacos.config.password: qwe#12345
spring.cloud.nacos.config.context-path: /nacos
spring.cloud.nacos.discovery.username: develop
spring.cloud.nacos.discovery.password: qwe#12345
spring.cloud.nacos.config.extension-configs[0].data-id: common-nacos.yml
spring.cloud.nacos.config.extension-configs[1].data-id: common.yml
spring.cloud.nacos.config.extension-configs[2].data-id: common-sentinel.yml
spring.cloud.nacos.config.extension-configs[3].data-id: common-mysql.yml
spring.cloud.nacos.config.extension-configs[4].data-id: common-redis.yml
spring.cloud.nacos.config.extension-configs[5].data-id: common-swagger.yml
spring.cloud.nacos.config.extension-configs[6].data-id: common-security-client.yml
spring.cloud.nacos.config.extension-configs[7].data-id: voc-service-stats.yml
spring.cloud.loadbalancer.ribbon.enabled: false
spring.cloud.nacos.config.enabled: true
spring.cloud.nacos.discovery.enabled: true


com.alipay.sofa.boot.module-start-up-parallel: false
com.alipay.sofa.ark.master.biz: ${spring.application.name}
com.alipay.sofa.rpc.registry.address: nacos://${NACOS_SERVER_ADDR_URLS:************:31111}/sofa-rpc

