target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
.idea/*
*.iws
*.iml
*.ipr
*.log

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/
sentiment-api/.DS_Store
.DS_Store

### gradle ###
# Created by .ignore support plugin (hsz.mobi)
.gradle
.gradle/*
# General build files
**/build/**
**/out/*
gradle/*
*/gradle/*

lib/dokka.jar
lib/quasar.jar

**/logs/*
## Directory-based project format:
/gradle-plugins/.idea/
# Include the -parameters compiler option by default in IntelliJ required for serialization.
!.idea/compiler.xml
/gradlew
/gradlew.bat
