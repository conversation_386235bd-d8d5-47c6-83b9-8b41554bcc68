
**test环境信息**

<a name="heading_0"></a>**1.Kubernetes集群管理工具**

|**工具名称**|**访问地址**|**用户名**|**密码**|**备注**|
| :-: | :-: | :-: | :-: | :-: |
|kubesphere-console|http://172.16.80.16:30880/|(向运维申请开通)|(向运维申请开通)||
<a name="heading_1"></a>**2.DevOps工具**

|**工具名称**|**访问地址**|**用户名**|**密码**|**备注**|
| :-: | :-: | :-: | :-: | :-: |
|kubesphere-devops|http://172.16.80.16:30880/access/workspaces|（同KubeSphere）|(同KubeSphere)||
<a name="heading_2"></a>**3.Nacos-2.2.1（集群）**

一个更易于构建云原生应用的动态服务发现、配置管理和服务管理平台。

产品类的连接信息

|**工具名称**|**集群内访问地址**|**集群外访问地址**|**用户名**|**密码**|**备注**|
| :-: | :-: | :- | :-: | :-: | :-: |
|nacos-server|http://nacos-products-headless.middleware-test.svc.cluster.local:8848|http://172.16.80.16:31111/nacos|develop|qwe#12345||


<a name="heading_3"></a>**4.Mysql-8.0.28**

关系型数据库管理系统。

|**序号**|**业务名称**|**业务类型**|**集群内连接地址**|**集群外连接地址**|数据库|**账户**|**密码**|**备注**|
| :-: | :-: | :-: | :-: | :-: | :- | :-: | :-: | :-: |
|1|linkis|产品|mysql-products-service.middleware-test.svc.cluster.local:3306|172\.16.80.16:31101|linkis|linkis|(不公开)|大数据组linkis申请使用|
|2|qualitis|产品|mysql-products-service.middleware-test.svc.cluster.local:3306|172\.16.80.16:31101|qualitis|qualitis|(不公开)|大数据组qualitis申请使用|
|3|nacos|产品|mysql-products-service.middleware-test.svc.cluster.local:3306|172\.16.80.16:31101|nacos|nacos|(不公开)|nacos使用|
|4|voc-cloud|产品|mysql-products-service.middleware-test.svc.cluster.local:3306|172\.16.80.16:31101|voc|voc|(不公开)|voc-cloud使用|
|5|voc-cloud|产品|mysql-products-service.middleware-test.svc.cluster.local:3306|172\.16.80.16:31101|security\_data|voc|(不公开)|voc-cloud使用|
|6|gmc-data|项目|mysql-project-service.middleware-test.svc.cluster.loal:3306|172\.16.80.16:31100|gmc\_test|gmc|(不公开)|gmc项目使用|

<a name="heading_4"></a>**5.Redis-6.2.14**

高性能的key-value数据库。

|**序号**|**业务名称**|**业务类型**|**集群内连接地址**|**集群外连接地址**|数据库|**账户**|**密码**|**备注**|
| :-: | :-: | :-: | :-: | :-: | :- | :-: | :-: | :-: |
|1|voc-cloud使用|产品|redis-products-service.middleware-test.svc.cluster.local:6379|172\.16.80.16:31102|DB0|（无）|ft2024|voc-cloud使用|

<a name="heading_5"></a>**6.Elasticsearch-8.11.0**

分布式 RESTful 风格的搜索和数据分析引擎。

|**业务类型**|**集群内连接地址**|**集群外连接地址**|**账户**|**密码**|**备注**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|产品|elasticsearch-products-service.middleware-test.svc.cluster.local:9200|172\.16.80.16:31104|elastic|Futong2024||
|项目|elasticsearch-project-service.middleware-test.svc.cluster.local:9200|172\.16.80.16:31106|elastic|Futong2024||

<a name="heading_6"></a>**7.Kibana-8.11.0**

开源的分析和可视化平台,设计用于和Elasticsearch一起工作。

|**业务类型**|**集群内连接地址**|**集群外连接地址**|**账户**|**密码**|**备注**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|产品|kibana-products-service.middleware-test.svc.cluster.local:9200|172\.16.80.16:31108|elastic|Futong2024||
|项目|kinaba-project-service.middleware-test.svc.cluster.local:9200|172\.16.80.16:31109|elastic|Futong2024||
<a name="heading_7"></a>**8.Skywalking-9.7.0**

可观测性分析平台和应用性能管理系统，提供分布式追踪、服务网状遥测分析、指标聚合和可视化一体化解决方案。

|**业务类型**|**集群内连接地址**|**集群外连接地址**|**账户**|**密码**|**备注**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|产品|skywalking-ui.middleware-test.svc.cluster.local:9200|172\.16.80.16:31110||||
<a name="heading_8"></a>**9.Springbootadmin-3.1.8**

非常好用的监控和管理的源软件，够将 Actuator 中的信息进行界面化的展示，也可以监控所有 Spring Boot 应用的健康状况，提供实时警报功能。

|**业务类型**|**集群内连接地址**|**集群外连接地址**|**账户**|**密码**|**备注**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|产品|voc-springboot-admin.middleware-test.svc.cluster.local:9200|172\.16.80.16:31304|admin|Passw0rd|(账户密码代码配置)|

<a name="heading_9"></a>**10.Sentinel-2.0.0**

阿里开源的一款面向分布式、多语言异构化服务架构的流量治理组件。

|**业务类型**|**集群内连接地址**|**集群外连接地址**|**账户**|**密码**|**备注**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|产品|sentinel-products.middleware-test.svc.cluster.local:8080|172\.16.80.16:31112|sentinel|123456|web|
|产品|sentinel-products.middleware-test.svc.cluster.local:8719|172\.16.80.16:31113|sentinel|123456|api|

<a name="heading_10"></a>**11.StarRocks-3.2**

StarRocks 是**新一代极速全场景 MPP (Massively Parallel Processing) 数据库**。StarRocks 的愿景是能够让用户的**数据分析变得更加简单和敏捷**。用户无需经过复杂的预处理，就可以用 StarRocks 来支持多种数据分析场景的极速分析。

StarRocks **架构简洁**，采用了全面向量化引擎，并配备全新设计的 CBO (Cost Based Optimizer) 优化器，**查询速度（尤其是多表关联查询）远超同类产品**。

StarRocks 能很好地支持实时数据分析，并能实现对实时更新数据的高效查询。StarRocks 还支持现代化物化视图，进一步加速查询。

使用 StarRocks，用户可以灵活构建包括大宽表、星型模型、雪花模型在内的各类模型。

StarRocks 兼容 MySQL 协议，支持标准 SQL 语法，易于对接使用，全系统无外部依赖，高可用，易于运维管理。StarRocks 还兼容多种主流 BI 产品，包括 Tableau、Power BI、FineBI 和 Smartbi。


|**业务类型**|**集群内连接地址**|**集群外连接地址**|**账户**|**密码**|**备注**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|产品|starrocks-test-fe-search.middleware-test.svc.cluster.local:9030|172\.16.80.16:31120|voc|futong2024||
<a name="heading_11"></a>**12.MinIO-RELEASE.2024-02-17T01-15-57Z**

MinIO 是一种对象存储解决方案，提供与亚马逊云科技 S3 兼容的 API，并支持所有核心 S3 功能。 MinIO 旨在部署在任何地方 - 公有云或私有云、裸机基础设施、编排环境和边缘基础设施。

|**业务类型**|**集群内连接地址**|**集群外连接地址**|**账户**|**密码**|**备注**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|产品|minio-products.middleware-test.svc.cluster.local:9090|172\.16.80.16:31124|voc-cloud|Qwe#12345|console|
|产品|minio-products.middleware-test.svc.cluster.local:9000|172\.16.80.16:31125|voc-cloud|Qwe#12345|API|


