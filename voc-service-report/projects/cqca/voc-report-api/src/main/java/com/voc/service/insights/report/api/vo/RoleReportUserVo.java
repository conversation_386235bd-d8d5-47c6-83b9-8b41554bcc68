package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoleReportUserVo implements Serializable {

    @Schema(description = "用户ID")
    @NotBlank(message = "用户ID不能为空")
    private String userId;


    @Schema(description = "客户ID")
    @NotBlank(message = "客户ID不能为空")
    private String clientId;
    @Builder.Default
    private Boolean tree = Boolean.FALSE;

    private Boolean admin;
}
