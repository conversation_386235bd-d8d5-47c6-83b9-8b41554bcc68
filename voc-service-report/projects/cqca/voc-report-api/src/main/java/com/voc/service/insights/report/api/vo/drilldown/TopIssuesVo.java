package com.voc.service.insights.report.api.vo.drilldown;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 全旅程top问题VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "全旅程top问题VO")
public class TopIssuesVo {
    
    @Schema(description = "关键词")
    private String keyword;
    
    @Schema(description = "情感")
    private String sentiment;
    
    @Schema(description = "提及量")
    private Long mentions;
    
    @Schema(description = "提及量变化")
    private Long mentionsChange;
    
    @Schema(description = "提及量环比")
    private BigDecimal mentionsMoM;
    
    @Schema(description = "提及率")
    private BigDecimal mentionRate;
} 