package com.voc.service.insights.report.api.vo.drilldown;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 词云图VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "词云图VO")
public class WordCloudVo {
    
    @Schema(description = "提及量")
    private Long mentions;
    
    @Schema(description = "热词名称")
    private String hotWordName;
    
    @Schema(description = "正面提及量")
    private Long positiveMentions;
    
    @Schema(description = "中性提及量")
    private Long neutralMentions;
    
    @Schema(description = "负面提及量")
    private Long negativeMentions;
} 