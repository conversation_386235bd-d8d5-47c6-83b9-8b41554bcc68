package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.exponential.GranularityDetailModel;
import com.voc.service.insights.report.api.vo.GranularityDetailVo;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/28 上午11:38
 * @描述:
 **/
public interface IInsReportExponentialGranularityDetailsService {

    void saveBatchGranularityDetails(String clientId, List<GranularityDetailModel> granularityDetail, String granularityId, List<String> ids);

    List<GranularityDetailVo> findGranularityDetails(String clientId, String id);

    void deleteGranularityDetails(List<String> idList);
}
