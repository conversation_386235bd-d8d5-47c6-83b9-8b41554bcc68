package com.voc.service.insights.report.api.constant;

/**
 * Create by joe on 2018/9/26
 */
public interface Constants {

    public static final int INFINITY_VALUE = 999999;   //标记无穷大
    interface System {
        String osName = java.lang.System.getProperty("os.name");
        boolean isWindows = (osName != null && osName.startsWith("Windows"));
    }

    String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    String YYYY_MM_DD = "yyyy-MM-dd";
    String YYYYMMDD = "yyyy/MM/dd";
    String YYYYMM = "yyyy-MM";

    interface TimeFormat {
        String DEFAULT_FORMAT = "hh:mm:ss";
    }

    interface Link {
        String PUBLIC_TLD_LIB = "https://publicsuffix.org/list/public_suffix_list.dat";
    }

    interface File {
        String TLD_FILE_NAME = "public_suffix_list.txt";
    }

    interface Redis {
        String TEMPLATE_CACHE_PREFIX = "TEM_CACHE_";
    }

    interface Page {
        int MAX_ITERATOR_TIMES = 50;
    }

    interface Reg {
        String MATCH_GROUP_NAME = "CONTENT";
    }

    interface DupNamespace {
        String URL_DEF = "default_namespace";
    }

    String PAGE = "PAGE";
    String START = "START";
    String END = "END";
}
