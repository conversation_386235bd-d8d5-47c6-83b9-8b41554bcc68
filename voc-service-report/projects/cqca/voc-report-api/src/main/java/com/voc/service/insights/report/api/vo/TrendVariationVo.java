package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/4 下午1:00
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrendVariationVo {
    @Schema(description = "预警次数")
    private List<RiskDetailVo> riskNum;
    @Schema(description = "预警记录")
    private List<RiskDetailVo> riskRecord;
    @Schema(description = "预警趋势")
    private List<RiskDetailVo> riskTrend;
}
