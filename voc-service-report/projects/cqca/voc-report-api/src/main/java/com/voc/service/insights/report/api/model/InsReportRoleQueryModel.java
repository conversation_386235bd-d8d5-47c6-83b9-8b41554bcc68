package com.voc.service.insights.report.api.model;

import com.voc.service.common.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class InsReportRoleQueryModel extends Page implements Serializable {

    @Schema(description = "角色状态")
    private String enabled;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "客户ID")
    @Builder.Default
    private String clientId = "0";

    private String roleId;

    private String searchKeyword;

    private String brandCode;

    private String brandName;

    @Builder.Default
    private Boolean checkAdmin = Boolean.FALSE;

    @Builder.Default
    private Boolean selectAll = Boolean.FALSE;

    @Builder.Default
    private List<String> permissionIdList = new ArrayList<>();
    @Schema(description = "标签类型")
    private String tagLibType;

}
