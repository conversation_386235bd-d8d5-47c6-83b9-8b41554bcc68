package com.voc.service.insights.report.api.api.clients;

import com.alibaba.fastjson.JSONObject;
import com.voc.service.insights.report.api.vo.DndcUserInfoVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/16 上午10:27
 * @描述:
 **/
//@FeignClient(name = "service.dndc", url = "${service.analysis.v1}")
//@FeignClient(name = "service.dndc", url = "http://localhost:8888")
public interface IDndcServiceClient {
    @GetMapping("/profile")
    DndcUserInfoVo getUserInfo(@RequestParam("access_token") String accessToken);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/15 下午2:23
     * @描述
     * @param grantType  属性常量 grant_type=authorization_code
     * @param timestamp 当前时间格式(1489739502583)
     * @param clientId 应用注册ID
     * @param clientSecret 应用注册密码
     * @param code 接受到的code值
     * @param redirectUri 应用回调地址,需要http格式化
     * @return java.lang.String
     **/
    @PostMapping("/oauth/token")
    JSONObject getAccessToken(@RequestParam(value = "grant_type",required = true,defaultValue = "authorization_code") String grantType,
                              @RequestParam(value = "oauth_timestamp",required = true) Long timestamp,
                              @RequestParam(value = "client_id",required = true) String clientId,
                              @RequestParam(value = "client_secret",required = true)String clientSecret,
                              @RequestParam(value = "code",required = true)String code,
                              @RequestParam(value = "redirect_uri",required = true)String redirectUri
                          );
}
