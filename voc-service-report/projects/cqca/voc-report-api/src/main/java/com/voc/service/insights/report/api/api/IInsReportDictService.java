package com.voc.service.insights.report.api.api;

import com.github.pagehelper.PageInfo;
import com.voc.service.insights.report.api.model.InsReportDictModel;
import com.voc.service.insights.report.api.vo.dict.DictDetailVo;
import com.voc.service.insights.report.api.vo.DictInfoVo;
import com.voc.service.insights.report.api.vo.dict.DictListVo;

import java.util.List;

/**
 * <p>
 * 字典表 服务类
 * </p>
 *
 * @since 2018-12-28
 */
public interface IInsReportDictService {

    /**
     * 新增字典
     *
     * @param sysDictModel 字典模型
     * @return 新增结果
     */
    Integer save(InsReportDictModel sysDictModel);

    /**
     * 分页查询字典列表
     *
     * @param model 查询条件
     * @return 分页结果
     */
    PageInfo<DictListVo> queryDictList(InsReportDictModel model);

    /**
     * 根据ID查询字典详情
     *
     * @param id 字典ID
     * @return 字典详情
     */
    DictDetailVo getDictById(String id);

    /**
     * 更新字典
     *
     * @param model 字典模型
     * @return 更新结果
     */
    Integer updateDict(InsReportDictModel model);

    /**
     * 删除字典
     *
     * @param id 字典ID
     * @return 删除结果
     */
    Integer deleteDict(String id);

    /**
     * 批量删除字典
     *
     * @param ids 字典ID列表
     * @return 删除结果
     */
    Integer deleteDictBatch(List<String> ids);

    /**
     * 根据编码查询字典信息
     *
     * @param clientId 客户ID
     * @param code 字典编码
     * @return 字典信息列表
     */
    List<DictInfoVo> findDictInfoByCode(String clientId, String code);
}
