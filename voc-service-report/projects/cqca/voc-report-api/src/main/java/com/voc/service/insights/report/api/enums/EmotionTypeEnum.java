package com.voc.service.insights.report.api.enums;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/20 上午10:19
 * @描述:
 **/
public enum EmotionTypeEnum {

    POSITIVE("positive", "正面", 1),
    NEGATIVE("negative", "负面", 3),
    NEUTRAL("neutral", "中性", 2),
    HIGH("high", "高", 1),
    HIGHER("higher", "较高", 2),
    MIDDLE("middle", "中", 3),
    INFERIOR("inferior", "较低", 4),
    LOW("low", "低", 5),
    UNABLE("unable", "无法评估", 6),
    COMPLAIN("complain","抱怨",10),
    COMPLAINT("complaint","投诉",11),
    SUGGEST("suggest","建议",12),
    CONSULT("consult","咨询",13),
    OTHER("other","其他",14);


    private final String text;
    private final String code;
    private final int order;

    EmotionTypeEnum(String code, String text, int order) {
        this.code = code;
        this.text = text;
        this.order = order;
    }

    public String getCode() {
        return this.code;
    }

    public String getText() {
        return this.text;
    }

    public int getOrder() {
        return this.order;
    }


    public static EmotionTypeEnum getByCode(String code) {
        for (EmotionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static EmotionTypeEnum getByText(String text) {
        for (EmotionTypeEnum type : values()) {
            if (type.getText().equals(text)) {
                return type;
            }
        }
        return null;
    }
}
