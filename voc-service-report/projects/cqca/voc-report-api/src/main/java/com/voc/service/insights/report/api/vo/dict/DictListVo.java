package com.voc.service.insights.report.api.vo.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 数据字典列表VO
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Data
@Schema(description = "数据字典列表VO")
public class DictListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "字典ID")
    private String id;

    @Schema(description = "字典名称")
    private String dictName;

    @Schema(description = "字典编码")
    private String dictCode;

    @Schema(description = "字典类型")
    private Integer type;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "创建人")
    private String operator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "字典项数量")
    private Integer itemCount;
}
