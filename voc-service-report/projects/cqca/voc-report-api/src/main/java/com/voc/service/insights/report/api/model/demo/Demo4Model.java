package com.voc.service.insights.report.api.model.demo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * @Title: DemoModel
 * @Package: com.voc.service.insights.engine.model.report
 * @Description:
 * @Author: cuick
 * @Date: 2024/9/12 15:49
 * @Version:1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Demo4Model implements Serializable {
    int row;
    String userId;
    String userName;
    String userType;
    long channelC;       //渠道数
    long mentionC;      //发声数
    long voiceC;      //提及量
    int complain_c;  //
    int complaint_c;  //
    int suggest_c;  //
    int consult_c;  //
    int other_c;  //
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Builder.Default
    BigDecimal nss = BigDecimal.ZERO;   //净情感值
    List<String> channelList;
}
