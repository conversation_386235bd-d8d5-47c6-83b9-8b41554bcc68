package com.voc.service.insights.report.api.vo.drilldown;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 人群特征VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "人群特征VO")
public class DemographicsVo {
    
    @Schema(description = "人群总人数")
    private Long totalUsers;
    
    @Schema(description = "年龄段占比top5")
    private List<AgeDistributionVo> ageDistributions;
    
    @Schema(description = "最近一次购车车龄占比top5")
    private List<CarAgeDistributionVo> carAgeDistributions;
    
    @Schema(description = "客户分类占比top5")
    private List<CustomerTypeDistributionVo> customerTypeDistributions;
    
    @Schema(description = "客户常驻所在省份占比top5")
    private List<ProvinceDistributionVo> provinceDistributions;
    
    @Schema(description = "最高学历占比top5")
    private List<EducationDistributionVo> educationDistributions;
    
    @Schema(description = "性别占比")
    private List<GenderDistributionVo> genderDistributions;
    
    /**
     * 年龄分布VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "年龄分布VO")
    public static class AgeDistributionVo {
        @Schema(description = "年龄段")
        private String ageRange;
        
        @Schema(description = "占比")
        private BigDecimal proportion;
    }
    
    /**
     * 车龄分布VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "车龄分布VO")
    public static class CarAgeDistributionVo {
        @Schema(description = "车龄")
        private String carAge;
        
        @Schema(description = "占比")
        private BigDecimal proportion;
    }
    
    /**
     * 客户类型分布VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "客户类型分布VO")
    public static class CustomerTypeDistributionVo {
        @Schema(description = "客户类型")
        private String customerType;
        
        @Schema(description = "占比")
        private BigDecimal proportion;
    }
    
    /**
     * 省份分布VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "省份分布VO")
    public static class ProvinceDistributionVo {
        @Schema(description = "省份名称")
        private String provinceName;
        
        @Schema(description = "占比")
        private BigDecimal proportion;
    }
    
    /**
     * 学历分布VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "学历分布VO")
    public static class EducationDistributionVo {
        @Schema(description = "学历")
        private String education;
        
        @Schema(description = "占比")
        private BigDecimal proportion;
    }
    
    /**
     * 性别分布VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "性别分布VO")
    public static class GenderDistributionVo {
        @Schema(description = "性别")
        private String gender;
        
        @Schema(description = "占比")
        private BigDecimal proportion;
    }
} 