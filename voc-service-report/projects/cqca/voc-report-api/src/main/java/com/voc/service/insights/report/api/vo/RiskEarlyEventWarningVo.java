package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/30 下午5:59
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskEarlyEventWarningVo {

    @Schema(description = "产品风险预警总数")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal productSumC;
    /**
     * 风险标题
     */
    @Schema(description = "风险标题")
    private String productTitle;
    /**
     * 产品风险预警
     */
    @Schema(description = "产品风险预警")
    private List<RiskEarlyVo> productRiskEarly;

    @Schema(description = "产品风险预警总数")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal serviceSumC;
    /**
     * 风险标题
     */
    @Schema(description = "风险标题")
    private String serviceTitle;
    /**
     * 服务风险预警
     */
    @Schema(description = "服务风险预警")
    private List<RiskEarlyVo> serviceRiskEarly;
    /**
     * 品质风险预警总数
     */
    @Schema(description = "品质风险预警总数")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal qualitySumC;
    /**
     * 品质风险标题
     */
    @Schema(description = "品质风险标题")
    private String qualityTitle;
    /**
     * 品质风险预警
     */
    @Schema(description = "品质风险预警")
    private List<RiskEarlyVo> qualityRiskEarly;

    /**
     * 用户风险预警总数
     */
    @Schema(description = "用户风险预警总数")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal userSumC;
    /**
     * 用户风险标题
     */
    @Schema(description = "用户风险标题")
    private String userTitle;
    /**
     * 用户风险预警
     */
    @Schema(description = "用户风险预警")
    private List<RiskEarlyUserWarningVo> userRiskEarly;
    @Schema(description = "投诉问题分布")
    private List<RiskEarlyVo> complaintsClassify;
    @Schema(description = "投诉问题详情")
    private List<RiskDetailVo> complaintsDetail;
    @Schema(description = "投诉等级分布")
    private RiskEarlyVo levelDistribution;
}
