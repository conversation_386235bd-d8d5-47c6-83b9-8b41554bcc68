package com.voc.service.insights.report.api.model;

import com.voc.service.common.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 数据字典查询模型
 *
 * @since 2018-12-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(description = "数据字典查询模型")
public class InsReportDictModel extends Page  implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "字典ID")
    private String id;

    @Schema(description = "字典类型，0-string类型，1-number类型，2-boolean类型")
    private Integer type;

    @Schema(description = "字典名称")
    private String dictName;

    @Schema(description = "字典编码")
    private String dictCode;

    @Schema(description = "字典描述")
    private String description;

    @Schema(description = "删除状态，0-未删除，1-已删除")
    private Integer delFlag;

    @Schema(description = "创建人")
    private String operator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;


}
