package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/29 下午4:24
 * @描述: 数据简报VO
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataPresentationVo {
    /**
     * 体验指数标题
     */
    @Schema(description = "体验指数标题")
    private String eiTitle;
    /**
     * 体验指数值
     */
    @Schema(description = "体验指数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal eiNsrC;
    /**
     * 体验指数环比值
     */
    @Schema(description = "体验指数环比值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal eiNsrRp;
    /**
     * 体验指数同比值
     */
    @Schema(description = "体验指数同比值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal eiNsrYp;
    /**
     * 体验指数表情
     */
    @Schema(description = "体验指数表情")
    private String eiNsrG;

    /**
     * 产品体验指数标题
     */
    @Schema(description = "产品体验指数标题")
    private String prodTitle;
    /**
     * 产品体验指数值
     */
    @Schema(description = "产品体验指数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal prodNsr;
    /**
     * 产品体验指数环比百分比
     */
    @Schema(description = "产品体验指数环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal prodNsrRp;
    /**
     * 产品体验指数同比百分比
     */
    @Schema(description = "产品体验指数同比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal prodNsrYp;
    /**
     * 产品体验指数表情
     */
    @Schema(description = "产品体验指数表情")
    private String prodNsrG;


    /**
     * 服务体验指数标题
     */
    @Schema(description = "服务体验指数标题")
    private String servTitle;
    /**
     * 服务体验指数值
     */
    @Schema(description = "服务体验指数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal servNsr;
    /**
     * 服务体验指数环比百分比
     */
    @Schema(description = "服务体验指数环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal servNsrRp;
    /**
     * 服务体验指数同比百分比
     */
    @Schema(description = "服务体验指数同比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal servNsrYp;
    /**
     * 服务体验指数表情
     */
    @Schema(description = "服务体验指数表情")
    private String servNsrG;

    /**
     * 观点数标题
     */
    @Schema(description = "观点数标题")
    private String positiveTitle;
    /**
     * 观点数值
     */
    @Schema(description = "观点数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal positiveNsrC;
    /**
     * 观点数环比百分比
     */
    @Schema(description = "观点数环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal positiveNsrRp;
    /**
     * 观点数同比百分比
     */
    @Schema(description = "观点数同比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal positiveNsrYp;
    /**
     * 图标
     */
    @Schema(description = "图标")
    private String positiveNsrG;

    /**
     * 用户数标题
     */
    @Schema(description = "用户数标题")
    private String userTitle;
    /**
     * 用户数值
     */
    @Schema(description = "用户数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal userNsrC;
    /**
     * 用户数环比百分比
     */
    @Schema(description = "用户数环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal userNsrRp;
    /**
     * 用户数同比百分比
     */
    @Schema(description = "用户数同比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal userNsrYp;
    /**
     * 图标
     */
    @Schema(description = "图标")
    private String userNsrG;

    /**
     * 单据数标题
     */
    @Schema(description = "单据数标题")
    private String serverOrderTitle;
    /**
     * 单据数值
     */
    @Schema(description = "单据数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal serverOrderNsrC;
    /**
     * 单据数环比百分比
     */
    @Schema(description = "单据数环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal serverOrderNsrRp;
    /**
     * 单据数同比百分比
     */
    @Schema(description = "单据数同比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal serverOrderNsrYp;
    /**
     * 图标
     */
    @Schema(description = "图标")
    private String serverOrderNsrG;

    private String labelType;
    @Schema(description = "日期")
    private String date;

}
