package com.voc.service.insights.report.api.api;

import com.github.pagehelper.PageInfo;
import com.voc.service.insights.report.api.model.InsReportOperationLogModel;
import com.voc.service.insights.report.api.model.StaSysDepartModel;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;
import java.util.Map;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/11 下午3:05
 * @描述:
 **/
public interface IInsOperationLogService {
    /**
     * @param reportOperationLogModel
     * @return com.github.pagehelper.PageInfo
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/10/11 下午3:16
     * @描述 获取操作日志列表
     **/
    PageInfo findOperationLog(InsReportOperationLogModel reportOperationLogModel);

    void downLoadOperationLog(InsReportOperationLogModel reportOperationLogModel, HttpServletResponse response);

    List<StaSysDepartModel> findDepartList();
}
