package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/1 下午2:23
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UsersInsightVo {
    @Schema(description = "标签名称")
    private String tagName;
    @Schema(description = "标签编码")
    private String tagCode;
    @Schema(description = "标签值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagC;
    @Schema(description = "标签占比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagP;
    @Schema(description = "标签环比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagR;
    @Schema(description = "标签同比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagY;
    @Schema(description = "标签同比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagYp;
    @Schema(description = "标签环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagRp;
    private String labelType;
    @Schema(description = "日期")
    private String date;
    @Schema(description = "标签总数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagSumC;
    @Schema(description = "指数数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nsrC;
    @Schema(description = "图标")
    private String nsrG;
}
