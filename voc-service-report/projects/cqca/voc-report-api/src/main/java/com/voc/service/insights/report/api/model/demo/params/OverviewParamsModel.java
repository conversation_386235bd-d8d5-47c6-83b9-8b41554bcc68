package com.voc.service.insights.report.api.model.demo.params;

import com.voc.service.insights.report.api.annotation.SortField;
import com.voc.service.insights.report.api.annotation.SortFieldConvert;
import com.voc.service.insights.report.api.enums.RiskLevelEnum;
import com.voc.service.insights.report.api.model.CommonFilterModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;

/**
 * @Title: OverviewParamsModel
 * @Package: com.voc.service.insights.engine.model.report.params
 * @Description:
 * @Author: cuick
 * @Date: 2024/9/12 16:02
 * @Version:1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@SortFieldConvert(fields = {
        @SortField(source = "channelC", targer = "channelC"),
        @SortField(source = "billC", targer = "billC"),
        @SortField(source = "complainC", targer = "complainC"),
        @SortField(source = "complaintC", targer = "complaintC"),
        @SortField(source = "consultC", targer = "consultC"),
        @SortField(source = "nsrC", targer = "nsrC"),
        @SortField(source = "opinion", targer = "opinion"),
        @SortField(source = "tagC", targer = "tag_c"),
        @SortField(source = "tagRp", targer = "tag_rp"),
})
public class OverviewParamsModel extends CommonFilterModel implements Serializable {
    //用户旅程
    private String userJourneyCode;
    //当前页
    @Builder.Default
    private Integer pageNum = 1;
    //每页数量
    @Builder.Default
    private Integer pageSize = 20;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "风险Id")
    private String riskId;

    @Schema(description = "模型结果集ID")
    private String newId;
    //意图
    private String intention;

    private String oneId;

    private String customerName;

    @Schema(description = "分页参数")
    private String order;

    @Schema(description = "大区")
    private String bigArea;
    @Schema(description = "大区编码")
    private String bigAreaIds;
    @Schema(description = "区域 下钻页单值条件使用-省份")
    private String area;
    @Schema(description = "车系 下钻页单值条件使用")
    private String carSeries;
    @Schema
    private String mentionCarSeries;
    private String year;
    @Schema(description = "时间维度：月:12 ,周：49")
    private String dimensionality;

    @Builder.Default
    private String pageSource = "1";

    @Builder.Default
    private Integer reportType = 0;


    private Integer riskType;


    public String getRiskLevel() {
        if (riskLevel == null) {
            return null;
        } else if (RiskLevelEnum.S.getText().equals(riskLevel)) {
            return RiskLevelEnum.S.getCode();
        } else if (RiskLevelEnum.A.getText().equals(riskLevel)) {
            return RiskLevelEnum.A.getCode();
        } else if (RiskLevelEnum.B.getText().equals(riskLevel)) {
            return RiskLevelEnum.B.getCode();
        }
        return riskLevel;
    }
}
