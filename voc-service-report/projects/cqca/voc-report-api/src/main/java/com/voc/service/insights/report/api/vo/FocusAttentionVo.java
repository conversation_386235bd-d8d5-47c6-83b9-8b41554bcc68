package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/30 下午5:25
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FocusAttentionVo {

    /**
     * 聚焦观点标题
     */
    @Schema(description = "聚焦观点标题")
    private String title;
    /**
     * 聚焦观点数值
     */
    @Schema(description = "聚焦观点数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagSumC;
    /**
     * 聚焦关注环图
     */
    @Schema(description = "聚焦关注环图")
    List<FocusDistributionVo> focusDistribution;
    /**
     * 聚焦关注标签分布图
     */
    @Schema(description = "聚焦关注标签分布图")
    List<TagDistributionVo> tagDistribution;
}
