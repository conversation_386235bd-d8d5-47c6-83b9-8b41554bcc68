package com.voc.service.insights.report.api.model;

import com.voc.service.common.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/11 下午3:09
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class InsReportOperationLogModel extends Page implements Serializable {
    @Schema(description = "开始时间")
    private String startDate;
    @Schema(description = "结束时间")
    private String endDate;
    @Schema(description = "所属部门")
    private List<String> deptId;
    @Schema(description = "关键词")
    private String searchKeyword;
    @Schema(description = "系统标识")
    private String appId;

}
