package com.voc.service.insights.report.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/28 上午11:13
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExponentialDataVo implements Serializable {
    /**
     * 指标id
     */
    private String indicatorsId;
    /**
     * 粒度
     */
    private List<ExponentialGranularityVo> granularity;
}
