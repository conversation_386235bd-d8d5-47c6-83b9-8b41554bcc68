package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncReportAccountVo {


    @Schema(description = "数字工作台账号(AD域账号)")
    private String uid;

    @Schema(description = "数字工作台账号唯一ID，不需保存")
    private String accountId;

    @Schema(description = "账户名称")
    private String accountName;

    @Schema(description = "账户别名")
    private String accountNickName;

    @Schema(description = "业务系统账号，可用作数据关联")
    private String accountNo;

    @Schema(description = "EHR员工编号")
    private String empCode;

    @Schema(description = "EDS加密")
    private String pwd;

    @Schema(description = "生效时间，数据格式“2021-07-21”)")
    private String activeDate;

    @Schema(description = "生效时间，数据格式“2022-07-21”")
    private String disableDate;

    @Schema(description = "1：男；0：女")
    private String sex;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "用户手机号，多个用“；”分割")
    private String mobile;

    @Schema(description = "办公电话")
    private String officePhone;

    @Schema(description = "家庭电话")
    private String homePhone;

    @Schema(description = "描述、说明、备注字段")
    private String userRemark;

    @Schema(description = "009:内部，007:临时，004:公共")
    private String accountType;

    @Schema(description = "0新增，1修改，2删除3启用，4停用，5密码修改")
    private String requestFlag;

    @Schema(description = "requestId 每次都不一样，是用来回调requestLog/callback接口的，不需要保存到数据库")
    private String requestId;

    @Schema(description = "状态：-1删除，0停用，1启用，2锁定，3申请中，4审批不同意，5等待中，7关闭")
    private String accountStatus;
}
