package com.voc.service.insights.report.api.model.exponential;

import com.voc.service.insights.report.api.model.indicators.IndicatorsEmojiParamModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/28 上午11:10
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExponentialThresholdModel implements Serializable {
    //指数阈值
    private IndicatorsEmojiParamModel threshold;
    //指标类型
    private List<GranularityDetailModel> exponentialType;

    private String clientId;
    /**
     * 品牌编码
     */
    private String brandCode;
    private String brandName;
}
