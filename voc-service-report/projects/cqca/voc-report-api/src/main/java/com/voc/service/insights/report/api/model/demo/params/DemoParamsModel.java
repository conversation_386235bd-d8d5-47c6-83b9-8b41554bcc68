package com.voc.service.insights.report.api.model.demo.params;

import com.voc.service.insights.report.api.model.CommonFilterModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Title: DemoParamsDemo
 * @Package: com.voc.service.insights.engine.model.report.params
 * @Description:
 * @Author: cuick
 * @Date: 2024/9/12 16:02
 * @Version:1.0
 */
@Data
//@Builder
//@NoArgsConstructor
//@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DemoParamsModel extends CommonFilterModel {

}
