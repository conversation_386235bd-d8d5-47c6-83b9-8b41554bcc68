package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/4 下午12:22
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BriefReportVo {
    @Schema(description = "时间维度")
    private String dateUnit;
    @Schema(description = "开始时间")
    private String startDate;
    @Schema(description = "结束时间")
    private String endDate;
    @Schema(description = "负面观点数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal negativeC;
    @Schema(description = "用户数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal userC;
    @Schema(description = "风险等级")
    private String riskLeve;
    @Schema(description = "涉及车系")
    private String carSeriesName;
    @Schema(description = "观点热词")
    private List<OpinionWordsVo> opinionWord;
    @Schema(description = "负面数环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal negativeRp;
    @Schema(description = "用户数环比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal userRp;
    private String opinionWords;

    private String complainC;

    private String emotionC;

    private String complainRp;

    private String emotionRp;

    private String warningTime;

    private String datePeriod;

    private String focusNames;
}
