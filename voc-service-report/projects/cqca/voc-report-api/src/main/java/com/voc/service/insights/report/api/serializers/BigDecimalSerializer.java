package com.voc.service.insights.report.api.serializers;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * @Title: BigDecimalSerializer
 * @Package: com.voc.service.insights.report.serializer
 * @Description:
 * @Author: cuick
 * @Date: 2024/9/12 17:25
 * @Version:1.0
 */
public class BigDecimalSerializer extends JsonSerializer<BigDecimal> {
    public BigDecimalSerializer() {
    }

    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value != null) {
            BigDecimal number = value.setScale(2, BigDecimal.ROUND_HALF_UP);
            gen.writeNumber(number);
        } else {
            gen.writeNumber(value);
        }

    }
}
