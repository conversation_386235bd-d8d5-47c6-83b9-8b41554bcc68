package com.voc.service.insights.report.api.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RoleReportAuthModel implements Serializable {

    @Schema(description = "角色Id编辑时必传")
    String id;

    String roleId;

    @Schema(description = "客户ID不能为空")
    @NotBlank(message = "客户ID不能为空")
    String clientId;

    @Schema(description = "角色名称")
    @NotBlank(message = "角色名称不能为空")
    String roleName;
    @Schema(description = "菜单IdList")
    @NotEmpty(message = "菜单不能为空")
    List<String> permissionIdList;
    @Schema(description = "关联车系， 传code以后英文逗号(,)分隔")
    @NotEmpty(message = "关联车系不能为空")
    private List<String> seriesIds;

    @Schema(description = "关联渠道ID，以后英文逗号(,)分隔")
    @NotEmpty(message = "关联渠道ID不能为空")
    private List<String> channelIds;

    @Schema(description = "关联业务标签，传code以后英文逗号(,)分隔")
    private List<String> businessTagIds;

    @Schema(description = "关联业务标签，传code以后英文逗号(,)分隔")
    private List<String> serviceTagIds;

    @Schema(description = "关联质量标签，传code以后英文逗号(,)分隔")
    private List<String> qualityTagIds;

    @Schema(description = "关联业务标签，传code以后英文逗号(,)分隔")
    @NotEmpty(message = "关联区域不能为空")
    private List<String> areaIds;

    @Schema(description = "功能权限：是否可以导出 true为是")
    private Boolean isExport;

    @Schema(description = "功能权限：是否可以下载 true为是")
    private Boolean isDownload;

    @Schema(description = "是否拥有所有权限 true:是")
    private Boolean allPermission;

    @Schema(description = "品牌code")
    @NotBlank(message = "品牌名称不能为空")
    private String brandCode;

    @Schema(description = "角色状态")
    Integer enabled;

    private String remark;


}
