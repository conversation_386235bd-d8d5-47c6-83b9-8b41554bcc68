package com.voc.service.insights.report.api.api;


import com.voc.service.insights.report.api.model.InsReportUserRoleModel;
import com.voc.service.insights.report.api.vo.InsReportUserRoleVo;

import java.util.List;

public interface IInsReportUserRoleService {

    Boolean saveOrUpdate(InsReportUserRoleModel insUserRoleModel);

    List<InsReportUserRoleVo> getRoleInfo(InsReportUserRoleModel insUserRoleModel);

    String getRoleIdByUserId(String clientId, String userId);

    void deleteRoleUserId(String clientId, String userId);

    Integer getCountByRole(String roleId);

    List<String> getUserIdByRoleId(String clientId, String roleId);

}
