package com.voc.service.insights.report.api.vo.drilldown;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 地域分析VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "地域分析VO")
public class RegionalAnalysisVo {
    
    @Schema(description = "省份名称")
    private String provinceName;
    
    @Schema(description = "提及量")
    private Long mentions;
    
    @Schema(description = "提及量环比")
    private BigDecimal mentionsMoM;
    
    @Schema(description = "体验值")
    private BigDecimal experienceValue;
    
    @Schema(description = "体验值环比")
    private BigDecimal experienceValueMoM;
} 