package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/1 上午10:49
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserJourneyVo {
    /**
     * 用户旅程编码
     */
    private String code;
    /**
     * 用户旅程名称
     */
    private String name;
    /**
     * 排序
     */
    private String sortOrder;
    /**
     * 指数数值
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nsrC;

    private String nsrG;
    /**
     * 指数环比值
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nsrR;
    /**
     * 指数同比值
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nsrY;
}
