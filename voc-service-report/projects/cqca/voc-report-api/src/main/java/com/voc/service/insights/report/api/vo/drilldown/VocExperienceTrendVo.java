package com.voc.service.insights.report.api.vo.drilldown;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * VOC体验值趋势VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "VOC体验值趋势VO")
public class VocExperienceTrendVo {
    
    @Schema(description = "日期")
    private String date;
    
    @Schema(description = "总提及量")
    private Long totalMentions;
    
    @Schema(description = "正面提及量")
    private Long positiveMentions;
    
    @Schema(description = "中性提及量")
    private Long neutralMentions;
    
    @Schema(description = "负面提及量")
    private Long negativeMentions;
    
    @Schema(description = "体验值")
    private BigDecimal experienceValue;
    
    @Schema(description = "体验值环比")
    private BigDecimal experienceValueMoM;
} 