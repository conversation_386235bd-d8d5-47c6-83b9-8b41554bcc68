package com.voc.service.insights.report.api.api;


import com.github.pagehelper.PageInfo;
import com.voc.service.common.response.Result;
import com.voc.service.insights.report.api.model.InsReportRoleQueryModel;
import com.voc.service.insights.report.api.model.RoleReportAuthModel;
import com.voc.service.insights.report.api.vo.RoleReportAuthVo;
import com.voc.service.insights.report.api.vo.RoleReportUserVo;
import com.voc.service.insights.report.api.vo.TagLibelMappingVo;
import com.voc.service.insights.report.api.vo.UserReportRoleInfoVo;

import java.util.List;
import java.util.Map;

public interface IInsReportRoleService {

    List<RoleReportAuthVo> getListByRoleId(InsReportRoleQueryModel model);

    RoleReportAuthVo getRoleByRoleId(InsReportRoleQueryModel model);

    Map<String, String> getLabelTypeByUserId(String userId, String clientId, String brandCode);

    PageInfo queryRoleList(InsReportRoleQueryModel model);

    List<RoleReportAuthVo> queryMenuPermissionList(InsReportRoleQueryModel model);

    List<RoleReportAuthVo> queryMenuPermissionListNotCache(InsReportRoleQueryModel model);

    Result<?> saveOrUpdateRole(String clientId, List<RoleReportAuthModel> roleModelList);

    UserReportRoleInfoVo queryUserPermission(RoleReportUserVo model);


    Result<?> queryRoleALlList(InsReportRoleQueryModel model);

    List<String> findRegion(InsReportRoleQueryModel model);

    List<String> findChannel(InsReportRoleQueryModel model);

    List<String> findCarSeries(InsReportRoleQueryModel model);

    List<String> findBusinessTag(InsReportRoleQueryModel model);

    List<String> findQualityTag(InsReportRoleQueryModel model);

    Map<String, String> getBrandImage();

    Map<String, Map<String, List<String>>> getProjectTagLabel(String clientId);

    Boolean deleteRole(RoleReportUserVo model);

    Map<String, String> getBrandMap(String clientId);

    Map<String, List<TagLibelMappingVo>> getTagLibeMap(InsReportRoleQueryModel model);

}
