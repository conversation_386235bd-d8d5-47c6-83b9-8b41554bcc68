package com.voc.service.insights.report.api.api.clients;

import com.github.pagehelper.PageInfo;
import com.voc.service.common.response.Result;
import com.voc.service.insights.report.api.model.SyncReportDepartModel;
import com.voc.service.insights.report.api.vo.SyncReportAccountVo;
import com.voc.service.insights.report.api.vo.SyncResultVo;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "service.account", url = "${dndc.syn_data.synUserInfo}")
public interface IDndcAccountServiceClient {

    @Schema(description = "同步东风日产账户")
    @PostMapping("/list")
    SyncResultVo syncReportAccount(@RequestBody SyncReportDepartModel model);
}
