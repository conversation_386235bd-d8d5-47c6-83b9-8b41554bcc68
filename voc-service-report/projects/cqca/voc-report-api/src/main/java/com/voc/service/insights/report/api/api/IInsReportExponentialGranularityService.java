package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.exponential.ExponentialGranularityModel;
import com.voc.service.insights.report.api.vo.ExponentialGranularityVo;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/28 上午11:38
 * @描述:
 **/
public interface IInsReportExponentialGranularityService {

    void saveGranularity(String clientId, List<ExponentialGranularityModel> model, String id, String brandCode);

    List<ExponentialGranularityVo> findGranularity(String clientId, String id, String brandCode);

    void deleteGranularity(List<String> ids);
}
