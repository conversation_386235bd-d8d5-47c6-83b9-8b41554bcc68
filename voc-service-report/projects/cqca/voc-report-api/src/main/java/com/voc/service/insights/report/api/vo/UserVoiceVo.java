package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/4 下午1:24
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserVoiceVo {
    @Schema(description = "原始声音")
    private String originalTextScene;
    @Schema(description = "观点")
    private String opinion;
    @Schema(description = "声音id")
    private String newId;
    @Schema(description = "用户id")
    private String userId;
    @Schema(description = "用户名")
    private String username;
    @Schema(description = "指标数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nsrC;
    @Schema(description = "渠道数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal channelC;
    @Schema(description = "单据数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal billC;
    @Schema(description = "抱怨数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal complainC;
    @Schema(description = "投诉数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal complaintC;
    @Schema(description = "咨询数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal consultC;
    /**
     * 表扬
     */
    @Schema(description = "表扬数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal praiseC;
    /**
     * 建议
     */
    @Schema(description = "建议数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal suggestionC;
    /**
     * 陈述
     */
    @Schema(description = "陈述数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal statementC;
    @Schema(description = "观点数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal opinionC;
    @Schema(description = "渠道分布")
    private List<FocusDistributionVo> channelAttention;
    @Schema(description = "渠道名称")
    private String channelName;
    @Schema(description = "业务产生的时间")
    private String bizCreateTime;
    @Schema(description = "渠道业务")
    private String channelBiz;
    @Schema(description = "车系名称")
    private String carSeriesName;
    @Schema(description = "省份名称")
    private String provinceName;
    @Schema(description = "焦点")
    private String focus;
    private String nsrG;
    private String channelCode;

    private String yearTime;
}
