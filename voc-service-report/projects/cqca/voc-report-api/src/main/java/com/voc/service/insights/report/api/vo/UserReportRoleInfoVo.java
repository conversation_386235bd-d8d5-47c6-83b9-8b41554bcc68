package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UserReportRoleInfoVo implements Serializable {

    @Schema(description = "二级菜单权限树")
    List<ReportRoleAuthListVo> roleAuthListVoList;

    @Schema(description = "所有权限集合")
    List<InsReportRolePermissionVo> insRolePermissionVos;

}
