package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportDataEmotionVo {

    @Schema(description = "标题")
    private String title;

    @Schema(description = "日期")
    private String reportDate;

    @Schema(description = "正面数值")
    private String positiveC;


    @Schema(description = "负面数值")
    private String negativeC;

    @Schema(description = "中性数值")
    private String neutralC;

    @Schema(description = "正面数值合计")
    private String positiveSum;

    @Schema(description = "负面数值合计")
    private String negativeSum;


    @Schema(description = "中性数值合计")
    private String neutralSum;


    @Schema(description = "正面占比")
    private String positiveP;

    @Schema(description = "负面占比")
    private String negativeP;


    @Schema(description = "中性占比")
    private String neutralP;


    @Schema(description = "正面环比")
    private String positiveNsrRp;


    @Schema(description = "正面同比")
    private String positiveNsrYp;

    @Schema(description = "负面环比")
    private String negativeNsrRp;


    @Schema(description = "负面同比")
    private String negativeNsrYp;

    @Schema(description = "中性环比")
    private String neutralNsrRp;

    @Schema(description = "中性同比")
    private String neutralNsrYp;

}
