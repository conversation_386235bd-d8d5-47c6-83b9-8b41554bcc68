package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.demo.params.OverviewParamsModel;
import com.voc.service.insights.report.api.vo.*;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/1 上午11:45
 * @描述:
 **/
public interface IInsReportUserFocusedAttentionService {
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/1 上午11:47
     * @描述   获取用户聚焦关注
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.FocusAttentionVo
     **/
    FocusAttentionVo getClassificationProportion(OverviewParamsModel overviewParamsModel);

    /**
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.FocusDistributionVo
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/1 上午11:50
     * @描述 获取焦点TOP
     **/
    List<FocusDistributionVo> getFocusedTop(OverviewParamsModel overviewParamsModel);
    /**
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.FocusDistributionVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/18 下午2:10
     * @描述 获取焦点TOP折线图
     **/
    List<FocusDistributionVo> getFocusedTopLine(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/1 下午1:43
     * @描述  获取产品/服务情感分布
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.EmotionDistributionVo>
     **/
    List<EmotionDistributionVo> getEmotionDistribution(OverviewParamsModel overviewParamsModel);
    /**
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.EmotionDistributionVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/1 下午1:43
     * @描述 情感分布详情图
     **/
    List<DistributionDetailVo> getEmotionDetail(OverviewParamsModel overviewParamsModel);


    OpinionTrendVo getOpinionTrend(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/13 下午1:30
     * @描述
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.OpinionTrendVo
     **/
    OpinionTrendVo getLinkageOpinionTrend(OverviewParamsModel overviewParamsModel);


}
