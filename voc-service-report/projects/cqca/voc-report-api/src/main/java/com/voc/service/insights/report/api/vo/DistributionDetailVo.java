package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/1 下午1:39
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DistributionDetailVo {
    @Schema(description = "标签名称")
    private String tagName;
    @Schema(description = "标签数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagC;
    @Schema(description = "标签环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagRp;
    @Schema(description = "标签同比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagYp;
    @Schema(description = "标签等级")
    private String level;
}
