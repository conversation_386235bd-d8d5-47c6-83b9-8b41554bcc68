package com.voc.service.insights.report.api.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName msg_event_data
 * @createTime 2024年01月15日 12:00
 * @Copyright cuick
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DepartModel implements Serializable {

    private String id;

    @Schema(description = "应用组织ID，数字工作台orgId")
    private String departId;

    @Schema(description = "组织名称")
    private String name;

    @Schema(description = "0停用，1正常，-1删除")
    private String status;

    @Schema(description = "组织编码，用于数据关联")
    private String code;

    @Schema(description = "父级组织ID")
    private String parentId;

    @Schema(description = "父级组织编码")
    private String parentCode;

    @Schema(description = "备注信息")
    private String remark;

    @Schema(description = "同级排序字段，默认值为0")
    private Integer orgOrder;

    @Schema(description = "组织层级")
    private String orgLevel;

    @Schema(description = "组织管理员账号ID(应用系统账号ID)")
    private String orgAdmin;

    @Schema(description = "组织类型；0：无 1：集团 2：公司 3：部门")
    private String orgType;

    @Schema(description = "所属公司Id")
    private String companyID;

    @Schema(description = "所属公司编码")
    private String companyCode;

    @Schema(description = "组织模板ID，业务系统优先维护好对应模板，在数字工作台中手动添加")
    private String orgTemplateId;

    @Schema(description = "requestId 每次都不一样，是用来回调requestLog/callback接口的，不需要保存到数据库")
    private String requestId;

    @Schema(description = "应用组织ID路径(父组织ID列表)")
    private String orgIdPath;

    @Schema(description = "创建人")
    private String caretedBy;

    @Schema(description = "创建时间")
    private String caretedTime;

    @Schema(description = "修改人")
    private String updateBy;

    @Schema(description = "修改时间")
    private String updateTime;

    @Schema(description = "所属租户编码")
    private String tenantCode;

    @Schema(description = "所属租户ID")
    private String tenantId;

}
