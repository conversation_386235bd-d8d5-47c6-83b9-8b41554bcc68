package com.voc.service.insights.report.api.model.demo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * @Title: DemoModel
 * @Package: com.voc.service.insights.engine.model.report
 * @Description:
 * @Author: cuick
 * @Date: 2024/9/12 15:49
 * @Version:1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemoModel implements Serializable {
    String date;
    //保留小数点后两位
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Builder.Default
    BigDecimal negativeC = BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Builder.Default
    BigDecimal positiveC= BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Builder.Default
    BigDecimal neutralC= BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Builder.Default
    BigDecimal positiveR= BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Builder.Default
    BigDecimal neutralR= BigDecimal.ZERO;
}
