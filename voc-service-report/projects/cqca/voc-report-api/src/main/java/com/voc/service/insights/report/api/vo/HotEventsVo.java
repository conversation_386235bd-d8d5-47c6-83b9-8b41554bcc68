package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2025/8/4 10:20
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotEventsVo {
    @Schema(description = "名称")
    private String name;
    @Schema(description = "提及量")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal mentionCount;
    @Schema(description = "增长量")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal growth;
    @Schema(description = "标注")
    private String label;
}
