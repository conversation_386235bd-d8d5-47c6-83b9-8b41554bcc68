package com.voc.service.insights.report.api.vo.drilldown;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据来源VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据来源VO")
public class DataSourceVo {
    
    @Schema(description = "提及量")
    private Long mentions;
    
    @Schema(description = "数据源名称(渠道名称)")
    private String dataSourceName;
    
    @Schema(description = "数据源编码(渠道编码)")
    private String dataSourceCode;
    
    @Schema(description = "正面提及量")
    private Long positiveMentions;
    
    @Schema(description = "中性提及量")
    private Long neutralMentions;
    
    @Schema(description = "负面提及量")
    private Long negativeMentions;
} 