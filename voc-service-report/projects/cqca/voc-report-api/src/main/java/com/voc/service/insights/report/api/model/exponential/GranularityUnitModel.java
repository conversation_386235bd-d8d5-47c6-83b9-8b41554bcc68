package com.voc.service.insights.report.api.model.exponential;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/28 下午5:13
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GranularityUnitModel {
    private String year;
    private List<GranularityDetailModel> granularityDetail;
}
