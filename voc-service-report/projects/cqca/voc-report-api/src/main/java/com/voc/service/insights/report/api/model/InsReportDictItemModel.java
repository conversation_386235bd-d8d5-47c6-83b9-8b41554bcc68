package com.voc.service.insights.report.api.model;

import com.voc.service.common.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 数据字典项查询模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(description = "数据字典项查询模型")
public class InsReportDictItemModel extends Page  implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "字典项ID")
    private String id;

    @Schema(description = "字典ID")
    private String dictId;

    @Schema(description = "字典项文本")
    private String itemText;

    @Schema(description = "字典项英文文本")
    private String itemTextEn;

    @Schema(description = "字典项键")
    private String itemKey;

    @Schema(description = "字典项值")
    private String itemValue;

    @Schema(description = "字典项描述")
    private String description;

    @Schema(description = "排序顺序")
    private Integer sortOrder;

    @Schema(description = "状态，1-启用，0-不启用")
    private Integer status;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;


}
