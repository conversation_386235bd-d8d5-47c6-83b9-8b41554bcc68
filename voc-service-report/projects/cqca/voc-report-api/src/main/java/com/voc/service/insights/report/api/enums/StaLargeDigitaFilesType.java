package com.voc.service.insights.report.api.enums;

/**
 * @创建者: fanrong
 * @创建时间: 2024/12/26 下午3:23
 * @描述:
 **/
public enum StaLargeDigitaFilesType {

    VOC_STATISTICAL_REPORT("VocStatisticalReport","VOC统计报告"),
    ;

    private final String code;
    private final String text;

    StaLargeDigitaFilesType(String code, String text) {
        this.code = code;
        this.text = text;
    }

    public String getCode() {
        return this.code;
    }

    public String getText() {
        return this.text;
    }

    public static StaLargeDigitaFilesType getByCode(String code) {
        for (StaLargeDigitaFilesType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static StaLargeDigitaFilesType getByText(String text) {
        for (StaLargeDigitaFilesType type : values()) {
            if (type.getText().equals(text)) {
                return type;
            }
        }
        return null;
    }
}
