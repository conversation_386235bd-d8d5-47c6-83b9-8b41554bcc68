package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2025/8/4 10:46
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerEventVo {
    @Schema(description = "提及量")
    private Integer mentionCount;
    @Schema(description = "事件名称")
    private String eventName;
    @Schema(description = "事件处理占比名称")
    private String eventHandlePercentName;
    @Schema(description = "事件处理占比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal eventHandlePercent;
}
