package com.voc.service.insights.report.api.vo;

import com.voc.service.insights.report.api.annotation.ReportCarBrand;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @创建者: fanrong
 * @创建时间: 2024/7/1 上午11:21
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InsUserInfoVo implements Serializable {
    /**
     * 用户id
     */
    private String userId;
    /**
     * 用户名
     */
    private String username;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 系统图标
     */
    private String systemIcon;
    /**
     * 头像
     */
    private String headPortrait;
    /**
     * 系统名称
     */
    private String systemName;
    /**
     * 客户id集
     */

    private ConditionVo clientIds;
    /**
     * 是否管理员
     */
    private Boolean isAdmin;
    /**
     * 默认客户id
     */
    private String defaultClientId;
    /**
     * 菜单
     */
    private List<InsReportRolePermissionVo> menus;
    /**
     * 下钻
     */
    private Set<String> drillDowns;
    /**
     * 按钮
     */
    private Set<String> button;
    @Schema(description = "员工编号")
    private String employeeId;
    @Schema(description = "部门名称")
    private String deptName;

    /**
     * 默认品牌
     */
    @ReportCarBrand
    private String defaultBrand;
    /**
     * 品牌
     */
    private ConditionVo brands;
    /**
     * 阈值范围
     */
    private ConditionVo threshold;
    /**
     * 系统配置
     */
    private InsReportSystemVo systemInfoVo;
    /**
     * 角色id
     */
    private String roleId;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 话术提示
     */
    private Map<String,String> verbal;
    /**
     * 应用标签 PROD SERVICE QY
     */
    private List<ConditionDetailsVo> appTags;

    /**
     * 维护数据
     */
    private Boolean isExport;
    /**
     * 下载图表
     */
    private Boolean isDownload;
}
