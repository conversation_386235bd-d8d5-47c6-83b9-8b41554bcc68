package com.voc.service.insights.report.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportDataSoundDetailsListVo {

    private String serverorder;
    private String publishTime;
    private String channelSubclass;
    private String labelType;
    private String category1;
    private String category2;
    private String category3;
    private String category4;
    private String category5;
    private String brandCode;
    private String carConfigCn;
    private String baseSeries;
    private String vocAge;
    private String bigAreaSale;
    private String samllAreaSale;
    private String provinceName;
    private String cityName;
    private String dlrShortName;
    private String gender;
    private String age;
    private String custType;

}
