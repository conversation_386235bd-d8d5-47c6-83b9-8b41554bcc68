package com.voc.service.insights.report.api.api;


import com.voc.service.insights.report.api.model.InsReportRoleQueryModel;
import com.voc.service.insights.report.api.model.RoleReportAuthModel;

import java.util.List;

/**
 *
 */
public interface IInsReportSysRolePermissionService {

    void saveOrUpdateData(String roleId, RoleReportAuthModel sysRoleModel);

    List<String> getRolePermissionList(InsReportRoleQueryModel model);
}
