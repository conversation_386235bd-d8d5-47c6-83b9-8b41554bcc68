package com.voc.service.insights.report.api.api;


import com.voc.service.insights.report.api.model.DepartModel;
import com.voc.service.insights.report.api.model.StaSysDepartModel;

import java.util.List;
import java.util.Map;

public interface IInsReportStaSysDepartService {

    List<String> getDepartList(List<String> depIdList,String clientId);

    List<StaSysDepartModel> getClientDepartList(String type);

    Boolean saveOrUpdateDepart(DepartModel staSysDepartModel, String clientId);
}
