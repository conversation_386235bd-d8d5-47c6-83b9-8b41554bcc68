package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/2/20 15:34
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportChannelInfoVo implements Serializable {
    /**
     * 主键
     */
    @Schema(description = "id")
    private String id;

    /**
     * 父级id
     */
    @Schema(description = "父级id")
    private String parentId;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    private String name;

    /**
     * 渠道英文名称
     */
    @Schema(description = "渠道英文")
    private String nameEn;

    @Schema(description = "是否选中")
    @Builder.Default
    <PERSON> checked = Boolean.FALSE;


    @Schema(description = "子级渠道")
    private List<ReportChannelInfoVo> child;
}
