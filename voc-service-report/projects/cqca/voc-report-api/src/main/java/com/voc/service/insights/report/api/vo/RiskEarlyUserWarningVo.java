package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/30 下午6:23
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskEarlyUserWarningVo {

    private String levelName;
    private String levelCode;
    /**
     * 风险用户预警标题
     */
    @Schema(description = "风险用户预警标题")
    private String title;
    /**
     * 风险用户预警值
     */
    @Schema(description = "风险用户预警值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nsrC;
    @Schema(description = "风险预警总数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nsrSumC;
    /**
     * 风险用户预警环比百分比
     */
    @Schema(description = "风险用户预警环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nsrRp;
    @Schema(description = "风险预警占比值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nsrP;
    /**
     * 风险用户预警同比百分比
     */
    @Schema(description = "风险用户预警同比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nsrYp;
    /**
     * 风险用户预警详情
     */
    @Schema(description = "风险用户预警详情")
    private List<RiskDetailVo> riskDetail;
}
