package com.voc.service.insights.report.api.enums;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/11 下午5:43
 * @描述:
 **/
public enum RiskLevelEnum {
    S("S","一级"),
    A("A","二级"),
    B("B","三级");



    private final String code;
    private final String text;

    RiskLevelEnum(String code, String text) {
        this.code = code;
        this.text = text;
    }

    public String getCode() {
        return this.code;
    }

    public String getText() {
        return this.text;
    }


    public static RiskLevelEnum getByCode(String code) {
        for (RiskLevelEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static RiskLevelEnum getByText(String text) {
        for (RiskLevelEnum type : values()) {
            if (type.getText().equals(text)) {
                return type;
            }
        }
        return null;
    }
}
