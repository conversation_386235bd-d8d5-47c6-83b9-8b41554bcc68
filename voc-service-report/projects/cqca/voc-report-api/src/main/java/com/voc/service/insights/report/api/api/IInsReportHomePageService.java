package com.voc.service.insights.report.api.api;

import com.github.pagehelper.PageInfo;
import com.voc.service.insights.report.api.model.demo.params.OverviewParamsModel;
import com.voc.service.insights.report.api.vo.*;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/30 下午3:23
 * @描述:
 **/
public interface IInsReportHomePageService {

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/10/30 下午4:50
     * @描述  车系排行
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.ProductExperienceIndexVo>
     **/
    List<ProductExperienceIndexVo> getCarSeriesRanking(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/10/30 下午4:55
     * @描述   热点事件top
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.HotEventsVo>
     **/
    List<HotEventsVo> getHotEventsTop(OverviewParamsModel overviewParamsModel);
    /**
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.FocusDistributionVo
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/10/30 下午5:07
     * @描述 客户吐槽
     **/
    List<CustomerTeasingVo> getCustomerTeasing(OverviewParamsModel overviewParamsModel);
    /**
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.FocusAttentionVo
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/5 下午5:47
     * @描述 观点TOP
     **/
    CustomerEmotionVo getCustomerEmotion(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/10/30 下午5:07
     * @描述  数据来源
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.FocusDistributionVo
     **/
    FocusAttentionVo getDataSource(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/10/30 下午6:03
     * @描述   获取风险事件预警
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.RiskEarlyEventWarningVo
     **/
    RiskEarlyEventWarningVo getRiskEarlyEventWarning(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/10/30 下午6:29
     * @描述  获取产品问题简报
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.BriefReportVo
     **/
    BriefReportVo getBriefReport(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/4 下午1:06
     * @描述  获取趋势变化
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.TrendVariationVo
     **/
    TrendVariationVo getTrendList(OverviewParamsModel overviewParamsModel);
    /**
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.UserVoiceVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/4 下午1:29
     * @描述 获取用户声音
     **/
    PageInfo<UserVoiceVo> getUserVoice(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/8 下午4:08
     * @描述   获取风险用户声音
     * @param overviewParamsModel
     * @return com.github.pagehelper.PageInfo<com.voc.service.insights.report.api.vo.UserVoiceVo>
     **/
    PageInfo<UserVoiceVo> getRiskUserVoice(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/7 下午4:22
     * @描述   获取单据列表
     * @param overviewParamsModel
     * @return com.github.pagehelper.PageInfo<com.voc.service.insights.report.api.vo.UserVoiceVo>
     **/
    PageInfo<UserVoiceVo> getBillList(OverviewParamsModel overviewParamsModel);
    /**
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.UserVoiceVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/4 下午1:46
     * @描述 获取用户列表
     **/
    PageInfo<UserVoiceVo> getUserList(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/4 下午1:57
     * @描述   获取用户声音详情
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.UserVoiceVo
     **/
    UserVoiceVo getUserVoiceDetail(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/4 下午2:01
     * @描述   获取情感意图趋势
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.EmotionIntentionTrendsVo>
     **/
    List<EmotionIntentionTrendsVo> getEmotionIntentionTrends(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/4 下午2:10
     * @描述   获取高频词
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.HighFrequencyWordVo>
     **/
    List<HighFrequencyWordVo> getHighFrequencyWord(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/4 下午2:41
     * @描述  获取趋势变化
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.DataPresentationVo>
     **/
    List<DataPresentationVo> getTrendVariation(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/5 下午1:42
     * @描述  获取用户详情
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.UserVoiceVo
     **/
    UserVoiceVo getUserDetail(OverviewParamsModel overviewParamsModel);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/5 下午2:14
     * @描述   获取数据轨迹
     * @param overviewParamsModel 
     * @return java.util.List<com.voc.service.insights.report.api.vo.UserVoiceVo>
     **/
    List<UserVoiceVo> getDataTrail(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/11 上午10:00
     * @描述   获取数据轨迹渠道
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.UserVoiceVo>
     **/
    List<UserVoiceVo> getDataTrailChannel(OverviewParamsModel overviewParamsModel);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/8 下午3:47
     * @描述  风险用户列表
     * @param overviewParamsModel 
     * @return com.github.pagehelper.PageInfo<com.voc.service.insights.report.api.vo.UserVoiceVo>
     **/
    PageInfo<UserVoiceVo>getRiskUserList(OverviewParamsModel overviewParamsModel);
}
