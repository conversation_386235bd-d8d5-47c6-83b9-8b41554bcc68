package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/4 下午2:19
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpinionTrendVo {
    @Schema(description = "观点趋势柱状图")
    private List<UsersInsightVo> trendZhu;
    @Schema(description = "观点趋势折线图")
    private List<UsersInsightVo> trendLine;
}
