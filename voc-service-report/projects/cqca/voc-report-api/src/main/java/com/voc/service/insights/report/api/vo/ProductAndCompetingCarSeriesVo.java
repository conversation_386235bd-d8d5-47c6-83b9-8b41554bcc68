package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/1 下午2:38
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductAndCompetingCarSeriesVo {
    @Schema(description = "车系名称")
    private String carSeriesName;
    @Schema(description = "车系名称")
    private String carSeriesImage;
    @Schema(description = "车系数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal carSeriesNsrC;
    @Schema(description = "车系环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal carSeriesNsrRp;
    @Schema(description = "车系同比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal carSeriesNsrYp;
    @Schema(description = "车系数值占比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal carSeriesNsrCp;
    @Schema(description = "单据数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal billC;
    @Schema(description = "提及内容(四级标签)")
    private String tagName;
    @Schema(description = "提及内容(四级标签)数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagNsrC;
    @Schema(description = "")
    private String tagType;
    @Schema(description = "提及车系")
    private List<ProductAndCompetingCarSeriesVo> mentionCarSeries;
    @Schema(description = "提及内容")
    private List<ProductAndCompetingCarSeriesVo> mentionContent;
}
