package com.voc.service.insights.report.api.vo.drilldown;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 全旅程客户体验值VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "全旅程客户体验值VO")
public class CustomerJourneyExperienceVo {
    
    @Schema(description = "全旅程客户标签体验值列表")
    private List<CustomerTagExperienceVo> customerTagExperienceList;
    
    @Schema(description = "体验值(所有标签的)")
    private BigDecimal totalExperienceValue;
    
    @Schema(description = "体验值环比(所有标签的)")
    private BigDecimal totalExperienceValueMoM;
    
    @Schema(description = "提及量(所有标签的)")
    private Long totalMentions;
    
    @Schema(description = "提及量环比(所有标签的)")
    private BigDecimal totalMentionsMoM;
    
    /**
     * 客户标签体验值子VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "客户标签体验值VO")
    public static class CustomerTagExperienceVo {
        
        @Schema(description = "全旅程客户标签名称")
        private String customerTagName;
        
        @Schema(description = "全旅程客户标签体验值")
        private BigDecimal experienceValue;
        
        @Schema(description = "正面提及量")
        private Long positiveMentions;
        
        @Schema(description = "中性提及量")
        private Long neutralMentions;
        
        @Schema(description = "负面提及量")
        private Long negativeMentions;
        
        @Schema(description = "总提及量")
        private Long totalMentions;
    }
} 