package com.voc.service.insights.report.api.model;

import com.voc.service.insights.report.api.enums.CustTypeEnum;
import com.voc.service.insights.report.api.enums.PeriodEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Title: CommonFilterModel
 * @Package: com.voc.service.insights.engine.model.report
 * @Description:
 * @Author: cuick
 * @Date: 2024/9/12 15:29
 * @Version:1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonFilterModel implements Serializable {
    @NotBlank(message = "时间范围-开始时间不能为空")
    LocalDate startDate;
    @NotBlank(message = "时间范围-结束时间不能为空")
    LocalDate endDate;
    @NotBlank(message = "用户标识不能为空")
    String userId;
    /**
     * 客户id
     */
    String clientId;
    /**
     * 渠道集合
     */
    Set<String> channelIds = new HashSet<>();
    String riskChannelIds;
    /**
     * 区域集合
     */
    Set<String> areaIds = new HashSet<>();
    /**
     *
     */
    Set<String> subjectList = new HashSet<>();
    /**
     * 情感集合->正面、负面、中性
     */
    Set<String> sentimentList = new HashSet<>();
    /**
     * 严重性等级集合-> 高、中、低、较高、较低、无法评估
     */
    Set<String> faultLevelList = new HashSet<>();
    /**
     * 车系集合
     */
    Set<String> carSeriesList = new HashSet<>();
    String riskCarSeriesList;
    /**
     * 品牌集合
     */
    Set<String> brandCodeList = new HashSet<>();
    /**
     * 一级标签集合
     */
    Set<String> labelTypeLevelFirstList = new HashSet<>();
    String riskLabelTypeLevelFirstList;
    /**
     * 二级标签集合
     */
    Set<String> labelTypeLevelSecondList = new HashSet<>();
    String riskLabelTypeLevelSecondList;
    /**
     * 三级标签集合
     */
    Set<String> labelTypeLevelThreeList = new HashSet<>();
    /**
     * 四级标签集合
     */
    Set<String> labelTypeLevelFourList = new HashSet<>();

    Set<String> labelTypeLevelFourDisableList = new HashSet<>();
    /**
     * 观点集合
     */
    Set<String> topicList = new HashSet<>();
    /**
     * 标签类型集合-> PROD SERVICE QY
     */
    Set<String> labelTypeList = new HashSet<>();
    @Schema(description = "标签分类:PROD SERVICE QY  下钻页单值条件使用")
    private String tagType;

    private List<String> dlrShortNameList;

    private List<String> custTypeList;

    private List<String> genderList;

    List<Map<String, String>> ageRanges;

    List<Map<String, String>> vocAgeRanges;
    /**
     * 风险等级集合
     */
    Set<String> riskLevelList = new HashSet<>();

    List<List<String>> tagLabelList;

    String searchLabelLevel = "1";

    @Schema(description = "车主年龄Code")
    private List<String> ageCode;


    @Schema(description = "车辆年龄Code")
    private List<String> vocAgeCode;

    @Schema(description = "客户类型名称")
    private String custType;

    @Schema(description = "专营店Code")
    private List<String> dlrShortCode;
    @Schema(description = "客户性别名称")
    private String gender;


    /**
     * 是否比较同比日期范围
     * 1: 是   0： 否
     */

    int yoy = 1;

    int scaleMagnitude = 1;

    /**
     * 用户计算环比-偏移量
     *
     * @return
     */
    int scaleMagnitude_;

    /**
     * 时间维度：
     * 1：天  2：周 3：月 4：季度 5：年
     */

    int dateUnit;

    String datePeriod;

    int typeR = 1;

    int typeY = 1;


    public int getScaleMagnitude_() {
        if (dateUnit == -1) {
            if (scaleMagnitude != 1) {
                Long daysBetween = ChronoUnit.DAYS.between(this.getStartDate(), this.getEndDate());
                if (daysBetween > 12) {
                    return Math.toIntExact(daysBetween + 2L);
                } else {
                    return 13;
                }
            }
        } else if (dateUnit == 3) {
            if (scaleMagnitude != 1) {
                return 14;
            }
        }
        return scaleMagnitude + 1;
    }

    public int getTypeR() {
        if (PeriodEnum.D.getCode().equalsIgnoreCase(datePeriod) || "-1".equalsIgnoreCase(datePeriod)) {
            return 0;
        } else {
            return 1;
        }
    }

    public int getDateUnit() {
        //维度为天或者自定义
        if (PeriodEnum.D.getCode().equalsIgnoreCase(datePeriod) || "-1".equalsIgnoreCase(datePeriod)) {
            return 1;
        } else if (PeriodEnum.M.getCode().equalsIgnoreCase(datePeriod)) {
            //维度为月
            return 3;
        } else if (PeriodEnum.W.getCode().equalsIgnoreCase(datePeriod)) {
            //维度为周
            return 2;
        } else if (PeriodEnum.Q.getCode().equalsIgnoreCase(datePeriod)) {
            //维度为季度
            return 4;
        } else if (PeriodEnum.Y.getCode().equalsIgnoreCase(datePeriod)) {
            //维度为年
            return 5;
        }
        return 3;
    }


    public List<Map<String, String>> getAgeRanges() {
        List<Map<String, String>> list = new ArrayList<>();

        if (ObjectUtils.isNotEmpty(ageCode)) {
            ageCode.stream().filter(e -> e.contains("-")).forEach(e -> {
                Map<String, String> map = new HashMap<>();
                String[] split = e.split("-");
                map.put("max", split[1]);
                map.put("min", split[0]);
                list.add(map);
            });

        }
        return list;
    }

    public List<Map<String, String>> getVocAgeRanges() {
        List<Map<String, String>> vocAgeRanges = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(vocAgeCode)) {
            vocAgeCode.stream().filter(e -> e.contains("-")).forEach(e -> {
                Map<String, String> map = new HashMap<>();
                String[] split = e.split("-");
                map.put("max", split[1]);
                map.put("min", split[0]);
                vocAgeRanges.add(map);
            });
        }
        return vocAgeRanges;
    }

    public List<String> getCustTypeList() {
        List<String> list = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(custType) && CustTypeEnum.OTHER.getText().equals(custType)) {
            list = Arrays.stream(CustTypeEnum.values())
                    .filter(e -> !CustTypeEnum.OTHER.getText().equals(e.getText()))
                    .map(CustTypeEnum::getText)
                    .collect(Collectors.toList());
            custType = null;
        }
        return list;
    }
}
