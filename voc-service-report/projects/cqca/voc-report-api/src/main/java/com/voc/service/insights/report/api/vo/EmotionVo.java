package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/20 上午10:11
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmotionVo {
    @Schema(description = "情感名称")
    private String emotionName;
    @Schema(description = "正面数值")
    private BigDecimal positiveC;
    @Schema(description = "负面数值")
    private BigDecimal negativeC;
    @Schema(description = "中性数值")
    private BigDecimal neutralC;
    @Schema(description = "正面占比值")
    private BigDecimal positiveP;
    @Schema(description = "负面占比值")
    private BigDecimal negativeP;
    @Schema(description = "中性占比值")
    private BigDecimal neutralP;
    @Schema(description = "正面同比值")
    private BigDecimal positiveYp;
    @Schema(description = "负面同比值")
    private BigDecimal negativeYp;
    @Schema(description = "中性同比值")
    private BigDecimal neutralYp;
    @Schema(description = "正面环比值")
    private BigDecimal positiveRp;
    @Schema(description = "负面环比值")
    private BigDecimal negativeRp;
    @Schema(description = "中性环比值")
    private BigDecimal neutralRp;
    @Schema(description = "情感总值")
    private BigDecimal emotionSumC;
    @Schema(description = "严重性等级-高")
    private BigDecimal highC;
    @Schema(description = "严重性等级-较高")
    private BigDecimal higherC;
    @Schema(description = "严重性等级-中")
    private BigDecimal middleC;
    @Schema(description = "严重性等级-较低")
    private BigDecimal inferiorC;
    @Schema(description = "严重性等级-低")
    private BigDecimal lowC;
    @Schema(description = "严重性等级-无法评估")
    private BigDecimal unableC;
    @Schema(description = "严重性等级-高-占比百分比")
    private BigDecimal highP;
    @Schema(description = "严重性等级-较高-占比百分比")
    private BigDecimal higherP;
    @Schema(description = "严重性等级-中-占比百分比")
    private BigDecimal middleP;
    @Schema(description = "严重性等级-较低-占比百分比")
    private BigDecimal inferiorP;
    @Schema(description = "严重性等级-低-占比百分比")
    private BigDecimal lowP;
    @Schema(description = "严重性等级-无法评估-占比百分比")
    private BigDecimal unableP;
    @Schema(description = "严重性等级-高-同比百分比")
    private BigDecimal highRp;
    @Schema(description = "严重性等级-较高-同比百分比")
    private BigDecimal higherRp;
    @Schema(description = "严重性等级-中-同比百分比")
    private BigDecimal middleRp;
    @Schema(description = "严重性等级-较低-同比百分比")
    private BigDecimal inferiorRp;
    @Schema(description = "严重性等级-低-同比百分比")
    private BigDecimal lowRp;
    @Schema(description = "严重性等级-无法评估-同比百分比")
    private BigDecimal unableRp;
    @Schema(description = "严重性等级-高-环比百分比")
    private BigDecimal highYp;
    @Schema(description = "严重性等级-较高-环比百分比")
    private BigDecimal higherYp;
    @Schema(description = "严重性等级-中-环比百分比")
    private BigDecimal middleYp;
    @Schema(description = "严重性等级-较低-环比百分比")
    private BigDecimal inferiorYp;
    @Schema(description = "严重性等级-低-环比百分比")
    private BigDecimal lowYp;
    @Schema(description = "严重性等级-无法评估-环比百分比")
    private BigDecimal unableYp;

}
