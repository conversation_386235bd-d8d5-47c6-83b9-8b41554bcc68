package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.demo.params.OverviewParamsModel;
import com.voc.service.insights.report.api.model.indicators.IndicatorsNSRResultModel;
import com.voc.service.insights.report.api.vo.ReportDataBriefingVo;

/**
 * @Title: IInsReportOverviewService
 * @Package: com.voc.service.insights.report.api.api
 * @Description:
 * @Author: cuick
 * @Date: 2024/10/21 13:57
 * @Version:1.0
 */
public interface IInsReportOverviewService {

    ReportDataBriefingVo databriefing(OverviewParamsModel params);
    IndicatorsNSRResultModel test(OverviewParamsModel filterModel);
}
