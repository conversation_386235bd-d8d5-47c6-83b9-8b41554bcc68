package com.voc.service.insights.report.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AllReportExcelDataVo {

    // 数据概览列表
    private List<ReportDataOverviewVo> reportDataOverviewVos;

    // 情感比例列表
    private List<ReportDataEmotionVo> reportDataEmotionVos;

    // 意图比例列表
    private List<ReportDataIntentionVo> reportDataIntentionVos;

    // 渠道分布列表
    private List<ReportDataDistributionVo> reportDataChannelDistributionVos;

    // 区域分布列表
    private List<ReportDataDistributionVo> reportDataRegionalDistributionVos;

    // 车系分布列表
    private List<ReportDataDistributionVo> reportDataCarSeriesDistributionVos;

    // 车龄分布列表
    private List<ReportDataDistributionVo> reportDataCarAgeDistributionVos;

    // 用户类型分布列表
    private List<ReportDataDistributionVo> reportDataUserTypeDistributionVos;

    // 用户性别分布列表
    private List<ReportDataDistributionVo> reportDataUserGenderDistributionVos;

    // 用户年龄分布列表
    private List<ReportDataDistributionVo> reportDataUserAgeDistributionVos;

    // 类别分布列表
    private List<ReportDataDistributionVo> reportDataClassDistributionVos;

    // 所有区域分布列表
    private List<ReportDataDistributionVo> reportDataAllRegionDistributionVos;

    // 小区域销售分布列表
    private List<ReportDataDistributionVo> reportDataSmallAreaSaleDistributionVos;

    // 经销商简称分布列表
    private List<ReportDataDistributionVo> reportDataDlrShortNameDistributionVos;

    // 所有车系分布列表
    private List<ReportDataDistributionVo> reportDataAllCarSeriesDistributionVos;

    // 所有类别分布列表
    private List<ReportDataDistributionVo> reportDataAllClassDistributionVos;

    // 声音详情列表
    private List<ReportDataSoundDetailsListVo> reportDataSoundDetailsListVos;
}
