package com.voc.service.insights.report.api.model.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * 扩展公共查询条件Model
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "扩展公共查询条件Model")
public class ExtendComQueryModel extends BaseComQueryModel implements Serializable {
    @Schema(description = "车系编码，可多选")
    private Set<String> vehicleSeriesCodeSet;

    @Schema(description = "车型编码，可多选")
    private Set<String> vehicleModelCodeSet;

    @Schema(description = "渠道编码，可多选")
    private Set<String> channelCodeSet;

    @Schema(description = "oneId，可多选")
    private Set<String> oneIdSet;

    @Schema(description = "声音ID，可多选")
    private Set<String> idSet;

    @Schema(description = "数据唯一标识(原文id)，可多选")
    private Set<String> dataIdSet;

    @Schema(description = "关键词，可多选")
    private Set<String> keywords;

    @Schema(description = "全旅程客户标签编码1级，可多选")
    private Set<String> cjTagFirstCodeSet;
    @Schema(description = "全旅程客户标签编码2级，可多选")
    private Set<String> cjTagSecondCodeSet;
    @Schema(description = "全旅程客户标签编码3级，可多选")
    private Set<String> cjTagThreeCodeSet;

    @Schema(description = "全领域业务标签编码1级，可多选")
    private Set<String> adbTagFirstCodeSet;
    @Schema(description = "全领域业务标签编码2级，可多选")
    private Set<String> adbTagSecondCodeSet;
    @Schema(description = "全领域业务标签编码3级，可多选")
    private Set<String> adbTagThreeCodeSet;

    @Schema(description = "商品化属性标签编码1级，可多选")
    private Set<String> comTagFirstCodeSet;
    @Schema(description = "商品化属性标签编码2级，可多选")
    private Set<String> comTagSecondCodeSet;
    @Schema(description = "商品化属性标签编码3级，可多选")
    private Set<String> comTagThreeCodeSet;

    @Schema(description = "VRT标签编码1级，可多选")
    private Set<String> vtrTagFirstCodeSet;
    @Schema(description = "VRT标签编码2级，可多选")
    private Set<String> vtrTagSecondCodeSet;
    @Schema(description = "VRT标签编码3级，可多选")
    private Set<String> vtrTagThreeCodeSet;
    
    /**
     * 创建ExtendComQueryModel的builder
     */
    public static ExtendComQueryModelBuilder extendBuilder() {
        return new ExtendComQueryModelBuilder();
    }
    
    /**
     * ExtendComQueryModel Builder类
     */
    public static class ExtendComQueryModelBuilder {
        private ExtendComQueryModel model = new ExtendComQueryModel();
        
        public ExtendComQueryModelBuilder dateUnit(Integer dateUnit) {
            model.setDateUnit(dateUnit);
            return this;
        }
        
        public ExtendComQueryModelBuilder startDate(java.time.LocalDate startDate) {
            model.setStartDate(startDate);
            return this;
        }
        
        public ExtendComQueryModelBuilder endDate(java.time.LocalDate endDate) {
            model.setEndDate(endDate);
            return this;
        }
        
        public ExtendComQueryModelBuilder brandCodeSet(Set<String> brandCodeSet) {
            model.setBrandCodeSet(brandCodeSet);
            return this;
        }
        
        public ExtendComQueryModelBuilder sentimentSet(Set<String> sentimentSet) {
            model.setSentimentSet(sentimentSet);
            return this;
        }
        
        public ExtendComQueryModelBuilder intentionSet(Set<String> intentionSet) {
            model.setIntentionSet(intentionSet);
            return this;
        }
        
        public ExtendComQueryModelBuilder vehicleSeriesCodeSet(Set<String> vehicleSeriesCodeSet) {
            model.setVehicleSeriesCodeSet(vehicleSeriesCodeSet);
            return this;
        }
        
        public ExtendComQueryModelBuilder channelCodeSet(Set<String> channelCodeSet) {
            model.setChannelCodeSet(channelCodeSet);
            return this;
        }
        
        public ExtendComQueryModelBuilder keywords(Set<String> keywords) {
            model.setKeywords(keywords);
            return this;
        }
        
        public ExtendComQueryModelBuilder cjTagFirstCodeSet(Set<String> cjTagFirstCodeSet) {
            model.setCjTagFirstCodeSet(cjTagFirstCodeSet);
            return this;
        }
        
        public ExtendComQueryModel build() {
            return model;
        }
    }
} 