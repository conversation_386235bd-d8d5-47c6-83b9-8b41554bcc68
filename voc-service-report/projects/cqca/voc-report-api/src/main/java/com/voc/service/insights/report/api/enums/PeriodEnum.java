package com.voc.service.insights.report.api.enums;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/12 下午1:23
 * @描述:
 **/
public enum PeriodEnum {
    D("d","日"),
    M("m","月"),
    W("w","周"),
    Q("q","季度"),
    Y("y","年"),
    HY("hy","半年"),
    ;


    private final String code;
    private final String text;

    PeriodEnum(String code, String text) {
        this.code = code;
        this.text = text;
    }

    public String getCode() {
        return this.code;
    }

    public String getText() {
        return this.text;
    }


    public static PeriodEnum getByCode(String code) {
        for (PeriodEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static PeriodEnum getByText(String text) {
        for (PeriodEnum type : values()) {
            if (type.getText().equals(text)) {
                return type;
            }
        }
        return null;
    }
}
