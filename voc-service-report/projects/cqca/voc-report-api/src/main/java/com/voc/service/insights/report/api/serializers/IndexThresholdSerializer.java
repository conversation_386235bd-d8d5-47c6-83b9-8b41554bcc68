package com.voc.service.insights.report.api.serializers;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.voc.service.insights.report.api.annotation.IndexThreshold;
import com.voc.service.insights.report.api.vo.ConditionDetailsVo;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/14 上午9:44
 * @描述:
 **/
public class IndexThresholdSerializer extends JsonSerializer<Object> implements ContextualSerializer {
    private static final Logger log = LoggerFactory.getLogger(IndexThresholdSerializer.class);
    String code;
    String defaultValue;

    Class<?> type;


    public IndexThresholdSerializer() {
        log.info("--->> init {}", this.getClass().getSimpleName());
    }

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        try {
            //TODO 和权限接口一样，暂时写死，最后功能完善后改成灵活读取
            List<ConditionDetailsVo> details = List.of(
                    ConditionDetailsVo.builder().key("poorPerformance").value("表现不佳").startThresholdValue("0").endThresholdValue("30").code("4").build(),
                    ConditionDetailsVo.builder().key("needImprove").value("有待提高").startThresholdValue("30").endThresholdValue("60").code("3").build(),
                    ConditionDetailsVo.builder().key("goodPerformance").value("表现良好").startThresholdValue("60").endThresholdValue("80").code("2").build(),
                    ConditionDetailsVo.builder().key("greatPerformance").value("表现优秀").startThresholdValue("80").endThresholdValue("100").code("1").build()
            );

            List<ConditionDetailsVo> collect = details.stream().filter(k -> {
                BigDecimal v = BigDecimal.valueOf(Double.parseDouble(String.valueOf(value)));
                BigDecimal s = BigDecimal.valueOf(Double.parseDouble(k.getStartThresholdValue()));
                BigDecimal e = BigDecimal.valueOf(Double.parseDouble(k.getEndThresholdValue()));
                if (v.compareTo(s) >= 0 && v.compareTo(e) < 0) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            if (ObjectUtils.isNotEmpty(collect)) {
                ConditionDetailsVo conditionDetailsVo = collect.stream().findFirst().get();
                gen.writeString(String.valueOf(value));
                gen.writeFieldName(gen.getOutputContext().getCurrentName().concat("Text"));
                gen.writeString(conditionDetailsVo.getValue());
            } else if (StrUtil.isNotBlank(defaultValue)) {
                gen.writeObject(defaultValue);
            } else {
                gen.writeObject(value);
            }

        } catch (Exception e) {
//            log.error(e.getMessage(), e);
            log.warn("{} {}", "可能在其他服务调用fegn客户端时,无法有效过去到 bean对象导致，", e.getMessage());

            if (StrUtil.isNotBlank(defaultValue)) {
                gen.writeObject(defaultValue);
            } else {
                gen.writeObject(value);
            }
        }
    }


    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) throws JsonMappingException {
        IndexThreshold annotation = property.getAnnotation(IndexThreshold.class);
        // 只针对String类型属性进行脱敏
        if (Objects.nonNull(annotation) && Objects.equals(BigDecimal.class, property.getType().getRawClass())) {
            defaultValue = annotation.defaultText();
            type = BigDecimal.class;
            return this;
        }else if(Objects.nonNull(annotation) && Objects.equals(List.class, property.getType().getRawClass())){
            defaultValue = annotation.defaultText();
            type = List.class;
            return this;
        }
        else {
            log.error("{}属性类型不支持，当前只支持字符串类型，请注意！", IndexThreshold.class.getSimpleName());
        }
        return prov.findValueSerializer(property.getType(), property);
    }
}
