package com.voc.service.insights.report.api.model.indicators;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Title: IndicatorsParamModel
 * @Package: com.voc.service.insights.report.api.model.indicators
 * @Description:
 * @Author: cuick
 * @Date: 2024/11/19 10:25
 * @Version:1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndicatorsNSRParamModel {
    //负面
    private BigDecimal negative;
    //正面
    private BigDecimal positive;
    //中性
    private BigDecimal neutral;
}
