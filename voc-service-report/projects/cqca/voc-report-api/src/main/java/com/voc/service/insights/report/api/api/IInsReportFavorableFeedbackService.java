package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.demo.params.OverviewParamsModel;
import com.voc.service.insights.report.api.vo.FavorableFeedbackTrendVo;
import com.voc.service.insights.report.api.vo.FocusDistributionVo;
import com.voc.service.insights.report.api.vo.OpinionTrendVo;
import com.voc.service.insights.report.api.vo.UsersInsightVo;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/12/13 下午3:45
 * @描述:
 **/
public interface IInsReportFavorableFeedbackService {

    /**
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.FavorableFeedbackTrendVo
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/12/13 下午4:27
     * @描述 整体好评反馈+产品好评反馈+服务好评反馈
     **/
    FavorableFeedbackTrendVo findFavorableFeedbackTrend(OverviewParamsModel overviewParamsModel);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/12/13 下午4:31
     * @描述   根据tagTypeList获取好评反馈趋势折线图(默认为整体好评反馈折线图,联动时可获取产品+服务好评反馈折线图)
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.FavorableFeedbackTrendVo>
     **/
    List<FavorableFeedbackTrendVo> findFavorableFeedbackTrendLine(OverviewParamsModel overviewParamsModel);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/12/13 下午5:21
     * @描述   获取车系好评反馈占比
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.UsersInsightVo>
     **/
    List<UsersInsightVo> findCarSeriesProportion(OverviewParamsModel overviewParamsModel);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/12/13 下午5:21
     * @描述   获取区域好评反馈占比
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.UsersInsightVo>
     **/
    List<UsersInsightVo> findRegionProportion(OverviewParamsModel overviewParamsModel);

    /**
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.UsersInsightVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/12/13 下午5:34
     * @描述 获取产品/服务好评TOP柱图
     **/
    List<FocusDistributionVo> findFavorableTopZhu(OverviewParamsModel overviewParamsModel);
    /**
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.UsersInsightVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/12/13 下午5:37
     * @描述 获取产品/服务好评TOP列表
     **/
    List<FocusDistributionVo> findFavorableTopList(OverviewParamsModel overviewParamsModel);


    /**
     * 获取区域趋势变化
     *
     * @param overviewParamsModel
     * @return
     */
    List<UsersInsightVo> getRegionTrendChange(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/12/15 下午5:58
     * @描述   获取整体好评反馈趋势折线图
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.FavorableFeedbackTrendVo>
     **/
    List<FavorableFeedbackTrendVo> findFavorableFeedbackTrendDefaultLine(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/12/15 下午9:04
     * @描述   获取车系观点趋势
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.OpinionTrendVo
     **/
    OpinionTrendVo getCarSeriesOpinionTrend(OverviewParamsModel overviewParamsModel);
}
