package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/1 下午1:58
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserFocusVo {
    @Schema(description = "聚焦TOP列表")
    private List<FocusDistributionVo> list;
    @Schema(description = "聚焦TOP折线图")
    private List<FocusDistributionVo> line;
}
