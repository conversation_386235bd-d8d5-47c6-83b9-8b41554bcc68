package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/1 上午9:48
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndexedDistributionVo {
    @Schema(description = "用户旅途")
    private List<UserJourneyVo> userJourney;
    @Schema(description = "标签分布(一级+二级)")
    private List<IndexedPerformanceLevel> indexedPerformance;
    @Schema(description = "体验指数(四级)")
    private List<IndexedPerformanceVo> experienceIndex;
}
