package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/1 下午1:39
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DistributionDetailV2Vo {
    @Schema(description = "标签名称")
    private String tagName;
    @Schema(description = "标签数值")
    private BigDecimal tagC;
    private BigDecimal tagRp;
    private BigDecimal tagYp;


    @Schema(description = "标签名称")
    private String sentiment;
    @Schema(description = "标签数值")
    private BigDecimal sentimentC;
    private BigDecimal sentimentRp;
    private BigDecimal sentimentYp;
    private BigDecimal sentimentP;

    @Schema(description = "标签名称")
    private String faultLevel;
    @Schema(description = "标签数值")
    private BigDecimal faultLevelC;
    private BigDecimal faultLevelRp;
    private BigDecimal faultLevelYp;
    private BigDecimal faultLevelP;

    int level;

}
