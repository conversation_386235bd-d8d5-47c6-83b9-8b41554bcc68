package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2025/8/4 10:38
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerTeasingVo {
    @Schema(description = "标题")
    private String title;
    @Schema(description = "提及量")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal mentionCount;
    @Schema(description = "提及内容")
    private String mentionContent;
    @Schema(description = "提及时间")
    private String mentionTime;
    @Schema(description = "客户名称")
    private String customerName;
}
