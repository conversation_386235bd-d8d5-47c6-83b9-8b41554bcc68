package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @创建者: fanrong
 * @创建时间: 2025/8/4 10:53
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerTaskVo {
    @Schema(description = "任务名称")
    private String taskName;
    @Schema(description = "任务类型")
    private String taskType;
    @Schema(description = "优先级")
    private String priority;
    @Schema(description = "任务状态")
    private String taskStatus;
    @Schema(description = "责任部门")
    private String dutyDept;
}
