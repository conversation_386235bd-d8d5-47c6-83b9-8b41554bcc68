package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/30 上午9:15
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagDistributionVo {
    /**
     * 标签名称
     */
    @Schema(description = "标签名称")
    private String tagName;
    /**
     * 标签数量值
     */
    @Schema(description = "标签数量值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagC;
    /**
     * 标签环比百分比
     */
    @Schema(description = "标签环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagRp;
    /**
     * 标签同比百分比
     */
    @Schema(description = "标签同比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagYp;

    private String firstCode;

    private String secondCode;

}
