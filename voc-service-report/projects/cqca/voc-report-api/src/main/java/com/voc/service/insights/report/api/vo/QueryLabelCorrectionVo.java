package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryLabelCorrectionVo {
    @Schema(description = "原文")
    private String originalText;
    @Schema(description = "标签")
    private String tagText;
    @Schema(description = "观点")
    private String topic;
    @Schema(description = "意图")
    private String intention;
    @Schema(description = "情感")
    private String sentiment;

}
