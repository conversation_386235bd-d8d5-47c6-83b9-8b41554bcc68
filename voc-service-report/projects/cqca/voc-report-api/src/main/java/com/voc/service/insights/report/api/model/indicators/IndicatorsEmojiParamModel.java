package com.voc.service.insights.report.api.model.indicators;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Title: IndicatorsParamModel
 * @Package: com.voc.service.insights.report.api.model.indicators
 * @Description:
 * @Author: cuick
 * @Date: 2024/11/19 10:25
 * @Version:1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndicatorsEmojiParamModel {

    //优秀
    private BigDecimal excellentS; //低值
    private BigDecimal excellentE; //高值

    //良好
    private BigDecimal goodS;
    private BigDecimal goodE;
    //有待提高
    private BigDecimal improvementS;
    private BigDecimal improvementE;
    //不佳
    private BigDecimal poorS;
    private BigDecimal poorE;
}
