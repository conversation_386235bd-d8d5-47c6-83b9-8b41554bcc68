package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/1 下午2:29
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpectationDistributionVo {
    @Schema(description = "用户期待列表")
    private List<UsersInsightVo> usersInsightList;
    @Schema(description = "用户期待柱状图")
    private List<UsersInsightVo> usersInsightBing;
}
