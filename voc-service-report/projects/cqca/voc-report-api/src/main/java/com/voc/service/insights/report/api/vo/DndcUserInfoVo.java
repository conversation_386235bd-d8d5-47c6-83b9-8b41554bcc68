package com.voc.service.insights.report.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/22 下午3:06
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DndcUserInfoVo implements Serializable {
    private String id;
    private DndcAttributesInfo attributes;
}
