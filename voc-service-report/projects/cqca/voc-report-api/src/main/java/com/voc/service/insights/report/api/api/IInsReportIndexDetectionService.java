package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.demo.params.OverviewParamsModel;
import com.voc.service.insights.report.api.model.exponential.ExponentialThresholdModel;
import com.voc.service.insights.report.api.vo.*;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/1 上午10:12
 * @描述:
 **/
public interface IInsReportIndexDetectionService {

    /**
     * 获取指数表现
     *
     * @param overviewParamsModel
     * @return
     */
    DataPresentationVo getDataPresentation(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/1 上午10:15
     * @描述   默认体验指数检测
     * @param overviewParamsModel 
     * @return com.voc.service.insights.report.api.vo.IndexedPerformanceVo
     **/
    List<IndexedPerformanceVo> getDefaultIndexedPerformance(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者: fanrong
     * @创建时间: 2024/11/1 上午10:15
     * @描述   产品+服务体验指数检测
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.IndexedPerformanceVo
     **/
    List<IndexedPerformanceVo> getProductAndServiceIndexedPerformance(OverviewParamsModel overviewParamsModel);
    /**
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.IndexedPerformanceVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/1 上午11:04
     * @描述 获取体验指数分布
     **/
    IndexedDistributionVo getIndexedDistribution(OverviewParamsModel overviewParamsModel);

    /**
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.IndexedPerformanceVo
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/1 上午11:04
     * @描述 获取联动体验指数分布
     **/
    IndexedDistributionVo getLinkedIndexedDistribution(OverviewParamsModel overviewParamsModel);

    /**
     * 获取体验指数
     *
     * @param overviewParamsModel
     * @return
     */
    IndexedDistributionVo getIndexedPerformance(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/4 下午2:25
     * @描述  获取观点趋势
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.OpinionTrendVo
     **/
    OpinionTrendVo getOpinionTrend(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/13 下午1:30
     * @描述
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.OpinionTrendVo
     **/
    OpinionTrendVo getLinkageOpinionTrend(OverviewParamsModel overviewParamsModel);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/27 下午1:51
     * @描述  新增指数阈值
     * @param exponentialThresholdModel
     * @return void
     **/
    void saveExponentialConfig(ExponentialThresholdModel exponentialThresholdModel);

    ExponentialThresholdVo findExponentialConfig(ExponentialThresholdModel exponentialThresholdModel);
    /**
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.GranularityDetailVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/12/7 上午11:08
     * @描述 根据时间维度、年份以及维度类型(w或m)获取指标详情
     **/
    List<GranularityDetailVo> findGranularityDetail(OverviewParamsModel overviewParamsModel);

}
