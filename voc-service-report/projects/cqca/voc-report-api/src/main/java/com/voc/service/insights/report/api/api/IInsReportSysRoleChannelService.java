package com.voc.service.insights.report.api.api;


import com.voc.service.insights.report.api.model.InsReportRoleQueryModel;
import com.voc.service.insights.report.api.model.RoleReportAuthModel;

import java.util.List;
import java.util.Map;

/**
 *
 */
public interface IInsReportSysRoleChannelService {

    void saveOrUpdateData(String roleId, RoleReportAuthModel sysRoleModel);

    Map<String, List<String>> getRoleChanneList(InsReportRoleQueryModel model);
}
