package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.InsReportRoleQueryModel;
import com.voc.service.insights.report.api.vo.*;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/18 下午1:57
 * @描述:
 **/
public interface IInsReportSearchCriteriaService {

    /**
     * 获取区域
     * @param model
     * @return
     */
    List<InsReportRegionConfigVo> findRegion(InsReportRoleQueryModel model);
    /**
     * 获取渠道
     * @param model
     * @return
     */
    List<InsReportChannelInfoVo> findChannel(@RequestBody InsReportRoleQueryModel model);

    /**
     * 获取车系
     * @param model
     * @return
     */
    List<InsReportCarInfoVo> findCarSeries(@RequestBody InsReportRoleQueryModel model);

    /**
     * 获取业务标签
     * @param model
     * @return
     */
    List<InsReportTagLibCategoryVo> findBusinessTag(@RequestBody InsReportRoleQueryModel model);

    /**
     * 获取质量标签
     * @param model
     * @return
     */
    List<InsReportTagLibCategoryVo> findQualityTag(@RequestBody InsReportRoleQueryModel model);
    /**
     * 获取标签类型
     * @param model
     * @return
     */
    List<InsReportTagLibCategoryVo> findTagLabelType(InsReportRoleQueryModel model);

    /**
     * 获取阈值配置
     *
     * @param model
     * @return
     */
    List<ConditionDetailsVo> findThreshold(InsReportRoleQueryModel model);

    List<DateVo> findDateDimensionality();

}
