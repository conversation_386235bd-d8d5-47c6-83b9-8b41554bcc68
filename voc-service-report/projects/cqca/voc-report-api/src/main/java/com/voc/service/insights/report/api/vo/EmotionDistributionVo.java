package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/1 下午1:26
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmotionDistributionVo {
    @Schema(description = "情感名称")
    private String emotionName;
    @Schema(description = "情感值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal emotionC;
    @Schema(description = "情感总值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal emotionSumC;
    @Schema(description = "情感总值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal emotionP;
    @Schema(description = "情感总值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal emotionRp;
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Schema(description = "情感总值")
    private BigDecimal emotionYp;
    @Schema(description = "情感分类柱图")
    private List<DistributionDetailVo> distributionZhu;
    @Schema(description = "情感分布详情")
    private List<DistributionDetailVo> distributionDetail;
    @JsonIgnore
    int order ;
}
