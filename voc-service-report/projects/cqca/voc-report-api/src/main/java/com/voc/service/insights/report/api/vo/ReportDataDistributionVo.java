package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportDataDistributionVo {

    @Schema(description = "标题")
    private String title;

    @Schema(description = "名称")
    private String firstLabel;

    @Schema(description = "名称")
    private String secondLabel;

    @Schema(description = "名称")
    private String threeLabel;

    @Schema(description = "名称")
    private String fourLabel;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "正面数值")
    private BigDecimal positiveC;


    @Schema(description = "负面数值")
    private BigDecimal negativeC;

    @Schema(description = "中性数值")
    private BigDecimal neutralC;


    @Schema(description = "合计数值")
    private BigDecimal sumC;

    @Schema(description = "体验指数")
    private String nsrC;


    @Schema(description = "合计环比")
    private String sumNsrRp;


    @Schema(description = "合计同比")
    private String sumNsrYp;

    @Schema(description = "体验指数环比")
    private String nsrRp;


    @Schema(description = "体验指数同比")
    private String nsrYp;

}
