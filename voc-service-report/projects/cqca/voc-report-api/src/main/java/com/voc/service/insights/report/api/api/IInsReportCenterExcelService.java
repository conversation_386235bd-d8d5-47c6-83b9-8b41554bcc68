package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.InsReportCenterInfoModel;
import com.voc.service.insights.report.api.model.StaLargeDigitaFilesModel;
import com.voc.service.insights.report.api.vo.InsReportCenterInfoVo;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

public interface IInsReportCenterExcelService {

    //下载模版组装数据
    InsReportCenterInfoVo downloadTemplate(InsReportCenterInfoModel reportCenterInfoModel, HttpServletResponse response);

    void insert(StaLargeDigitaFilesModel staLargeDigitaFilesModel);

    Boolean update(StaLargeDigitaFilesModel staLargeDigitaFilesModel);

    List<StaLargeDigitaFilesModel> findReportList(String userId);

}
