package com.voc.service.insights.report.api.model.demo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * @Title: DemoModel
 * @Package: com.voc.service.insights.engine.model.report
 * @Description:
 * @Author: cuick
 * @Date: 2024/9/12 15:49
 * @Version:1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Demo3Model implements Serializable {
    String businessLabelTypeLevelFirst;

    long businessLabelTypeLevelFirstC;             //提及量
    //保留小数点后两位
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Builder.Default
    BigDecimal businessLabelTypeLevelFirstR = BigDecimal.ZERO;   //环比

    //保留小数点后两位
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Builder.Default
    BigDecimal businessLabelTypeLevelFirstP = BigDecimal.ZERO;   //占比

}
