package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/9/11 上午9:19
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportRegionConfigVo {
    /**
     * 主键
     */
    @Schema(description = "id")
    private String id;

    /**
     * 父级id
     */
    @Schema(description = "父级id")
    private String parentId;

    /**
     * 区域名称/分类名称
     */
    @Schema(description = "区域名称/分类名称")
    private String name;

    /**
     * 区域英文名称
     */
    @Schema(description = "区域英文名称")
    private String nameEn;
    /**
     * 区域状态
     */
    @Schema(description = "区域状态")
    private String status;
    /**
     * 区域(省份+城市)
     */
    @Schema(description = "区域(省份+城市)")
    private List<ReportProvinceVo> region;

    private List<ReportRegionConfigVo> child;
}
