package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.InsReportCenterInfoModel;
import com.voc.service.insights.report.api.vo.*;

import java.util.List;

public interface IInsReportCenterDataService {


    /**
     * 获取全部模版数据
     *
     * @param reportCenterInfoModel
     */
    AllReportExcelDataVo getAllReportExcelData(InsReportCenterInfoModel reportCenterInfoModel);

    /**
     * VoC-数据总览
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataOverviewVo> vocDataOverview(InsReportCenterInfoModel reportCenterInfoModel);

    /**
     * 情感占比
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataEmotionVo> emotionProportion(InsReportCenterInfoModel reportCenterInfoModel);

    /**
     * 意图占比
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataIntentionVo> intentionProportion(InsReportCenterInfoModel reportCenterInfoModel);


    /**
     * 渠道分布TOP5
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataDistributionVo> channelDistribution(InsReportCenterInfoModel reportCenterInfoModel);


    /**
     * 区域分布TOP5
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataDistributionVo> regionalDistribution(InsReportCenterInfoModel reportCenterInfoModel);


    /**
     * 车系分布TOP5
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataDistributionVo> carSeriesDistribution(InsReportCenterInfoModel reportCenterInfoModel);


    /**
     * 车辆车龄分布
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataDistributionVo> carAgeDistribution(InsReportCenterInfoModel reportCenterInfoModel);


    /**
     * 用户类型分布
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataDistributionVo> userTypeDistribution(InsReportCenterInfoModel reportCenterInfoModel);


    /**
     * 用户性别分布
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataDistributionVo> userGenderDistribution(InsReportCenterInfoModel reportCenterInfoModel);


    /**
     * 用户年龄分布
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataDistributionVo> userAgeDistribution(InsReportCenterInfoModel reportCenterInfoModel);


    /**
     * 分类分布TOP10
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataDistributionVo> classDistribution(InsReportCenterInfoModel reportCenterInfoModel);


    /**
     * 全部大区分布
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataDistributionVo> allRegionDistribution(InsReportCenterInfoModel reportCenterInfoModel);


    /**
     * 全部小区分布
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataDistributionVo> smallAreaSaleDistribution(InsReportCenterInfoModel reportCenterInfoModel);


    /**
     * 全部专营店分布
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataDistributionVo> dlrShortNameDistribution(InsReportCenterInfoModel reportCenterInfoModel);


    /**
     * 车系分布列表
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataDistributionVo> allCarSeriesDistribution(InsReportCenterInfoModel reportCenterInfoModel);


    /**
     * 分类分布列表
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataDistributionVo> allClassDistribution(InsReportCenterInfoModel reportCenterInfoModel);


    /**
     * 声音明细列表
     *
     * @param reportCenterInfoModel
     * @return
     */
    List<ReportDataSoundDetailsListVo> soundDetailsList(InsReportCenterInfoModel reportCenterInfoModel);
}
