package com.voc.service.insights.report.api.enums;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/11 上午11:01
 * @描述:
 **/
public enum LabelTypeEnum {

    PROD("PROD","prod","产品体验指数","产品好评反馈"),
    SERVICE("SERVICE","srv","服务体验指数","服务好评反馈"),
    QY("QY","qy","品质体验指数","品质好评反馈");

    private final String code;
    private final String dbCode;
    private final String text;
    private final String favorableFeedback;

    LabelTypeEnum(String code, String dbCode,String text,String favorableFeedback) {
        this.code = code;
        this.dbCode = dbCode;
        this.text = text;
        this.favorableFeedback = favorableFeedback;
    }

    public String getDbCode() {
        return this.dbCode;
    }
    public String getCode() {
        return this.code;
    }

    public String getText() {
        return this.text;
    }

    public String getFavorableFeedback() {
        return this.favorableFeedback;
    }

    public static LabelTypeEnum getByCode(String code) {
        for (LabelTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }


    public static LabelTypeEnum getByText(String text) {
        for (LabelTypeEnum type : values()) {
            if (type.getText().equals(text)) {
                return type;
            }
        }
        return null;
    }
}
