package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/4 下午1:54
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmotionIntentionTrendsVo {
    @Schema(description = "时间")
    private String date;
    @Schema(description = "抱怨数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal complainC;
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Schema(description = "抱怨环比")
    private BigDecimal complainR;
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Schema(description = "抱怨同比")
    private BigDecimal complainY;
    @Schema(description = "负面数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal negativeC;
    @Schema(description = "负面环比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal negativeR;
    @Schema(description = "负面同比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal negativeY;
    @Schema(description = "投诉数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal complaintC;
    @Schema(description = "投诉环比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal complaintR;
    @Schema(description = "投诉同比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal complaintY;

}
