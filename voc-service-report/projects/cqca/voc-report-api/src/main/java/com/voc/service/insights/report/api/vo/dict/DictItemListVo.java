package com.voc.service.insights.report.api.vo.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 数据字典项列表VO
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Data
@Schema(description = "数据字典项列表VO")
public class DictItemListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "字典项ID")
    private String id;

    @Schema(description = "字典ID")
    private String dictId;

    @Schema(description = "字典项文本")
    private String itemText;

    @Schema(description = "字典项英文文本")
    private String itemTextEn;

    @Schema(description = "字典项键")
    private String itemKey;

    @Schema(description = "字典项值")
    private String itemValue;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "状态（1启用 0不启用）")
    private Integer status;

    @Schema(description = "创建人")
    private String operator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
