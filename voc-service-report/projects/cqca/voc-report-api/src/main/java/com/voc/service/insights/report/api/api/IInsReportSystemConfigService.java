package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.InsReportSystemInfoModel;
import com.voc.service.insights.report.api.vo.InsReportSystemInfoVo;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/10 下午3:36
 * @描述:
 **/
public interface IInsReportSystemConfigService {

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/10/10 下午4:55
     * @描述   更新系统配置信息
     * @param reportSystemInfoModel
     * @return void
     **/
    void updateSystemConfig(InsReportSystemInfoModel reportSystemInfoModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/10/10 下午6:08
     * @描述   获取系统配置信息
     * @param reportSystemInfoModel
     * @return com.voc.service.insights.report.api.vo.InsReportSystemInfoVo
     **/
    InsReportSystemInfoVo findSystemConfig(InsReportSystemInfoModel reportSystemInfoModel);
}
