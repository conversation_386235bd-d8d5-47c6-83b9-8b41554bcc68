package com.voc.service.insights.report.api.api;

import com.github.pagehelper.PageInfo;
import com.voc.service.insights.report.api.model.InsReportDictItemModel;
import com.voc.service.insights.report.api.vo.dict.DictItemDetailVo;
import com.voc.service.insights.report.api.vo.dict.DictItemListVo;

import java.util.List;

/**
 * <p>
 * 字典项服务类
 * </p>
 *
 * @since 2018-12-28
 */
public interface IInsReportDictItemService {

    /**
     * 新增字典项
     *
     * @param insDictItemModel 字典项模型
     * @return 新增结果
     */
    Integer save(InsReportDictItemModel insDictItemModel);

    /**
     * 根据ID查询字典项
     *
     * @param id 字典项ID
     * @return 字典项模型
     */
    InsReportDictItemModel getById(String id);

    /**
     * 分页查询字典项列表
     *
     * @param model 查询条件
     * @return 分页结果
     */
    PageInfo<DictItemListVo> queryDictItemList(InsReportDictItemModel model);

    /**
     * 根据字典ID查询所有字典项
     *
     * @param dictId 字典ID
     * @return 字典项列表
     */
    List<DictItemListVo> getDictItemsByDictId(String dictId);

    /**
     * 根据ID查询字典项详情
     *
     * @param id 字典项ID
     * @return 字典项详情
     */
    DictItemDetailVo getDictItemDetailById(String id);

    /**
     * 更新字典项
     *
     * @param model 字典项模型
     * @return 更新结果
     */
    Integer updateDictItem(InsReportDictItemModel model);

    /**
     * 删除字典项
     *
     * @param id 字典项ID
     * @return 删除结果
     */
    Integer deleteDictItem(String id);
}
