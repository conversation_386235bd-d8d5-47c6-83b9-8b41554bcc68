package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Title: DataBriefingVo
 * @Package: com.voc.service.insights.report.api.vo
 * @Description:
 * @Author: cuick
 * @Date: 2024/10/21 13:51
 * @Version:1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportDataBriefingVo implements Serializable {
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Schema(description = "净情感值")
    BigDecimal nssC;
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Schema(description = "净情感值-环比值")
    BigDecimal nssR;
    @Schema(description = "月提及量")
    BigDecimal voiceC;
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Schema(description = "月提及量-环比比例")
    BigDecimal voiceRP;
    @Schema(description = "发声用户数")
    BigDecimal oneidC;
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Schema(description = "发声用户数-环比比例")
    BigDecimal oneidRP;
    @Schema(description = "发声数")
    BigDecimal mentionC;
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Schema(description = "发声数-环比比例")
    BigDecimal mentionRP;
}
