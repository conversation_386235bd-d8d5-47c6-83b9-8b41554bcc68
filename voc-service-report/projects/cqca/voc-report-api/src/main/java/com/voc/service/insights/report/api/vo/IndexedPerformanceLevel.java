package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/13 下午5:53
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndexedPerformanceLevel {
    @Schema(description = "一级标签名称")
    private String tagFirstName;
    /**
     * 一级标签数值
     */
    @Schema(description = "一级标签数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagFirstNsrC;
    /**
     * 一级标签图标
     */
    @Schema(description = "一级标签图标")
    private String tagFirstNsrG;

    @Schema(description = "一级标签环比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagFirstNsrR;
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Schema(description = "一级标签同比")
    private BigDecimal tagFirstNsrY;

    int sort;
    /**
     * 下级标签
     */
    private List<IndexedPerformanceVo> subordinateTag;
}
