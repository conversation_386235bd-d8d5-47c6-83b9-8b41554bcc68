package com.voc.service.insights.report.api.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.*;

/**
 * @创建者: fanrong
 * @创建时间: 2024/12/4 下午7:02
 * @描述:
 **/
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpsLogVo {
    /**
     * 操作时间
     */
    @ExcelProperty(value = "操作时间", order = 0)
    @ColumnWidth(20)
    private String operatorTime;
    /**
     * 操作人用户名称
     */
    @ExcelProperty(value = "操作人用户名称", order = 1)
    @ColumnWidth(20)
    private String username;

    /**
     * 操作详细日志
     */
    @ExcelProperty(value = "操作详细日志", order = 2)
    @ColumnWidth(20)
    private String logContent;

    /**
     * 操作详细日志
     */
    @ExcelProperty(value = "操作详细日志", order = 3)
    @ColumnWidth(20)
    private String logDesc;

    /**
     * IP
     */
    @ExcelProperty(value = "IP", order = 4)
    @ColumnWidth(20)
    private String ip;
}
