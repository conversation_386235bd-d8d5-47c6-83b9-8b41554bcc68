package com.voc.service.insights.report.api.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class InsReportCenterInfoModel extends CommonFilterModel implements Serializable {

    private String id;
    private String reportName;

    private String clientId;

    @Schema(description = "报告类型")
    private String reportType;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "品牌code")
    private String brandCode;

    @Schema(description = "车系名称")
    private List<String> carSeriesName;

    @Schema(description = "车系Code")
    private List<String> carSeriesCode;

    @Schema(description = "渠道名称")
    private List<String> channelName;

    @Schema(description = "渠道Code")
    private List<String> channelCode;


    @Schema(description = "专营店名称")
    private List<String> dlrShortName;



    @Schema(description = "标签类型 产品 服务 质量")
    private String labelTypeName;
    @Schema(description = "标签类型code 产品 服务 质量")
    private String labelTypeCode;

    @Schema(description = "标签名称集合")
    private List<String> labelNameList;


    @Schema(description = "客户类型Code")
    private String custCode;


    @Schema(description = "客户性别Code")
    private String genderCode;


    @Schema(description = "车主年龄")
    private List<String> age;



    @Schema(description = "车辆年龄")
    private List<String> vocAge;


    @Schema(description = "分页类型")
    private Integer pageType;

    private String taskId;

    private List<List<String>> tempDlrShortCodeList;

    //用户旅程
    private String userJourneyCode;
    //当前页
    @Builder.Default
    private Integer pageNum = 1;
    //每页数量
    @Builder.Default
    private Integer pageSize = 20;

    @Schema(description = "模型结果集ID")
    private String newId;
    //意图
    private String intention;

    private String oneId;

    private String customerName;

    @Schema(description = "分页参数")
    private String order;

    @Schema(description = "大区")
    private String bigArea;
    @Schema(description = "大区编码")
    private String bigAreaIds;
    @Schema(description = "区域 下钻页单值条件使用-省份")
    private String area;
    @Schema(description = "车系 下钻页单值条件使用")
    private String carSeries;
    @Schema
    private String mentionCarSeries;

}
