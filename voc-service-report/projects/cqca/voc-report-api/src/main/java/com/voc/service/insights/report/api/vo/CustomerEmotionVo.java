package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/8/4 10:55
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerEmotionVo {
    @Schema(description = "客户事件")
    List<CustomerEventVo> customerEvent;
    @Schema(description = "客户任务")
    List<CustomerTaskVo> customerTask;
}
