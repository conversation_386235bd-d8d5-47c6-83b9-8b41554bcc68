package com.voc.service.insights.report.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/10 下午6:06
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InsReportSystemVo implements Serializable {
    /**
     * id
     */
    private String id;
    /**
     * 是否开启系统水印
     */
    private Boolean systemWatermarking;
    /**
     * 是否开启文档水印
     */
    private Boolean documentWatermarking;
    /**
     * 默认时间周期
     */
    private String defaultPeriod;
}
