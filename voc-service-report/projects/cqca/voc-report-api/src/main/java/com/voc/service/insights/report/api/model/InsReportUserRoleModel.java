package com.voc.service.insights.report.api.model;

import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class InsReportUserRoleModel implements Serializable {

    private String id;

    private String userId;

    private List<String> userIdList;

    private String roleId;

    private String clientId;

    private LocalDateTime createTime;

}
