package com.voc.service.insights.report.api.vo.drilldown;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 提及量趋势VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "提及量趋势VO")
public class MentionTrendVo {
    
    @Schema(description = "提及量")
    private Long mentions;
    
    @Schema(description = "体验值")
    private BigDecimal experienceValue;
    
    @Schema(description = "日期")
    private String date;
    
    @Schema(description = "正面提及量")
    private Long positiveMentions;
    
    @Schema(description = "中性提及量")
    private Long neutralMentions;
    
    @Schema(description = "负面提及量")
    private Long negativeMentions;
} 