package com.voc.service.insights.report.api.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/10 下午3:29
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InsReportSystemInfoModel implements Serializable {
    @Schema(description = "系统配置id")
    private String id;
    @Schema(description = "客户id")
    private String clientId;
    @Schema(description = "是否显示系统水印")
    @Builder.Default
    private Boolean systemWatermarking = false;
    @Schema(description = "是否显示文档水印")
    @Builder.Default
    private Boolean documentWatermarking = false;
    @Builder.Default
    @Schema(description = "默认时间周期 日:d 月:m 周：w 季:q 年:y")
    private String defaultPeriod = "d";
}
