package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/29 下午4:24
 * @描述: 数据简报VO
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportDataOverviewVo {
    /**
     * 体验指数标题
     */
    @Schema(description = "标题")
    private String title;
    /**
     * 体验指数值
     */
    @Schema(description = "日期")
    private String reportDate;

    @Schema(description = "观点数值")
    private String positiveNsrC;


    @Schema(description = "单据数值")
    private String orderNsrC;

    @Schema(description = "用户数值")
    private String userNsrC;

    @Schema(description = "体验指数值")
    private String experienceIndex;

    @Schema(description = "观点数合计")
    private String positiveAmountTo;

    @Schema(description = "单据数合计")
    private String orderAmountTo;

    @Schema(description = "用户数合计")
    private String userAmountTo;

    @Schema(description = "观点数合计")
    private String experienceIndexAmountTo;
    /**
     * 观点数环比百分比
     */
    @Schema(description = "观点数环比百分比")
    private String positiveNsrRp;
    /**
     * 观点数同比百分比
     */
    @Schema(description = "观点数同比百分比")
    private String positiveNsrYp;

    /**
     * 单据数环比百分比
     */
    @Schema(description = "单据数环比百分比")
    private String orderNsrRp;
    /**
     * 单据数同比百分比
     */
    @Schema(description = "单据数同比百分比")
    private String orderNsrYp;


    /**
     * 用户数环比百分比
     */
    @Schema(description = "用户数环比百分比")
    private String userNsrRp;
    /**
     * 用户数同比百分比
     */
    @Schema(description = "用户数同比百分比")
    private String userNsrYp;

    /**
     * 体验指数环比值
     */
    @Schema(description = "体验指数环比值")
    private String eiNsrRp;
    /**
     * 体验指数同比值
     */
    @Schema(description = "体验指数同比值")
    private String eiNsrYp ;


}
