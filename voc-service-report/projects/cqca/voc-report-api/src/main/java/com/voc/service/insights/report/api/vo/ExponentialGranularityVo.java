package com.voc.service.insights.report.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/28 上午11:15
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExponentialGranularityVo implements Serializable {
    /**
     * 粒度类型 w:周 m:月
     */
    private String granularityUnit;
    /**
     * 粒度明细
     */
    private List<GranularityUnitVo> granularity;

}
