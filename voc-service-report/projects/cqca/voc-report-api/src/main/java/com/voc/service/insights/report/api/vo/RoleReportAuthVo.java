package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class RoleReportAuthVo implements Serializable {

    Object dataChannel;
    Object relationBuTag;
    Object qualityTag;
    Object serviceTag;
    List<RelationCarVo> relationCar;
    List<RoleReportAuthTree> appKanban;
    Object area;
    @Schema(description = "关联车系， 传code以后英文逗号(,)分隔")
    private List<String> seriesIds;

    @Schema(description = "服务标签， 传code以后英文逗号(,)分隔")
    private List<String> serviceTagIds;

    @Schema(description = "关联渠道ID，以后英文逗号(,)分隔")
    private List<String> channelIds;

    @Schema(description = "关联产品标签，传code以后英文逗号(,)分隔")
    private List<String> businessTagIds;

    @Schema(description = "关联质量标签，传code以后英文逗号(,)分隔")
    private List<String> qualityTagIds;

    @Schema(description = "关联业务标签，传code以后英文逗号(,)分隔")
    private List<String> areaIds;
    private String roleId;
    private String roleName;
    private Integer roleType;
    private String brandCode;
    private String brandName;
    private List<String> brandCodeList;
    private Boolean isExport;
    private Boolean isDownload;
    private Boolean allPermission;
    private Integer status;
    private String remark;
    private Boolean checked;
    private List<String> appTags;



}
