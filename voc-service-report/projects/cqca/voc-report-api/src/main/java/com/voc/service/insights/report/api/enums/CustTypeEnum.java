package com.voc.service.insights.report.api.enums;

/**
 * @创建者: fanrong
 * @创建时间: 2024/12/26 下午3:23
 * @描述:
 **/
public enum CustTypeEnum {

    GUARANTOR("guarantor","保客"),
    CORPORATION("corporation","公司"),
    PERSONAGE("personage","个人"),
    PROWLER("prowler","潜客"),
    OTHER("other","其他"),
    ;

    private final String code;
    private final String text;

    CustTypeEnum(String code, String text) {
        this.code = code;
        this.text = text;
    }

    public String getCode() {
        return this.code;
    }

    public String getText() {
        return this.text;
    }

    public static CustTypeEnum getByCode(String code) {
        for (CustTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static CustTypeEnum getByText(String text) {
        for (CustTypeEnum type : values()) {
            if (type.getText().equals(text)) {
                return type;
            }
        }
        return null;
    }
}
