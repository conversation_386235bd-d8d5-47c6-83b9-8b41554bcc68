package com.voc.service.insights.report.api.api.clients;


import com.github.pagehelper.PageInfo;
import com.voc.service.common.response.Result;
import com.voc.service.insights.report.api.model.SyncReportDepartModel;
import com.voc.service.insights.report.api.vo.SyncDepartResultVo;
import com.voc.service.insights.report.api.vo.SyncReportDepartVo;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "service.depart", url = "${dndc.syn_data.syncDepart}")
public interface IDndcDepartServiceClient {

    @Schema(description = "同步东风日产部门")
    @PostMapping("/list")
    SyncDepartResultVo syncReportDepart(@RequestBody SyncReportDepartModel model);

}
