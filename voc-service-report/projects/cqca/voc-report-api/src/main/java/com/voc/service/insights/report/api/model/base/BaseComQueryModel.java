package com.voc.service.insights.report.api.model.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Set;

/**
 * 基础公共查询条件Model
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "基础公共查询条件Model")
public class BaseComQueryModel implements Serializable {
    @Schema(description = "时间维度 -1:日, 0:周, 1:月, 2:季, 3:年")
    private Integer dateUnit;

    @Schema(description = "开始时间")
    private LocalDate startDate;

    @Schema(description = "结束时间")
    private LocalDate endDate;

    @Schema(description = "品牌编码，可多选")
    private Set<String> brandCodeSet;

    @Schema(description = "情感，可多选")
    private Set<String> sentimentSet;

    @Schema(description = "意图，可多选")
    private Set<String> intentionSet;

    @Schema(description = "排序字段")
    private String orderBy;

    @Schema(description = "排序方式，asc/desc")
    private String orderType;

    @Schema(description = "页码")
    private Integer pageNum;

    @Schema(description = "每页大小")
    private Integer pageSize;
} 