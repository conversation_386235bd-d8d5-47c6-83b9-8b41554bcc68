package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.indicators.IndicatorsEmojiParamModel;
import com.voc.service.insights.report.api.vo.IndicatorsEmojiParamVo;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/28 上午11:38
 * @描述:
 **/
public interface IInsReportThresholdService {

    void saveThreshold(String clientId, IndicatorsEmojiParamModel model, String brandCode);

    IndicatorsEmojiParamVo findThreshold(String clientId,String brandCode);
}
