package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.voc.service.insights.report.api.annotation.Dict;
import com.voc.service.insights.report.api.constant.InsReportsightsConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/15 上午10:20
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InsReportCenterInfoVo implements Serializable {
    @Schema(description = "id")
    private String id;
    @Schema(description = "报告名称")
    private String reportName;
    @Schema(description = "时间范围")
    private String timeScope;
    @Schema(description = "品牌车系")
    private String brandCarSeries;
    @Schema(description = "渠道")
    private String channel;
    @Schema(description = "专营店")
    private String dlrShort;
    @Schema(description = "标签")
    private String tag;
    @Schema(description = "客户类型")
    private String custType;
    @Schema(description = "客户性别")
    private String gender;
    @Schema(description = "客户年龄")
    private String age;
    @Schema(description = "车辆年龄")
    private String vocAge;
    @Schema(description = "报告生成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @Schema(description = "报告状态 0:生成中，1：生成失败")
    @Dict(code = InsReportsightsConstants.REPORT_STATUS)
    private String status;
    private String reportUrl;
    private String fileName;

}
