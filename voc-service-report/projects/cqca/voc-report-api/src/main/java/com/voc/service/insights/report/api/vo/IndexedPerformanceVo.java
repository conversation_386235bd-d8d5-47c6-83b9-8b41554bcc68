package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/1 上午9:45
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndexedPerformanceVo {
    /**
     * 日期
     */
    @Schema(description = "日期")
    private String date;
    /**
     * 横坐标(X轴)值
     */
    @Schema(description = "")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nsrC;
    /**
     * 顶部标签数值
     */
    @Schema(description = "顶部标签数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagTopNsrC;
    /**
     * 顶部标签图标
     */
    @Schema(description = "顶部标签图标")
    private String tagTopNsrG;
    /**
     * 一级标签名称
     */
    @Schema(description = "一级标签名称")
    private String tagFirstName;
    /**
     * 一级标签数值
     */
    @Schema(description = "一级标签数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagFirstNsrC;
    /**
     * 一级标签图标
     */
    @Schema(description = "一级标签图标")
    private String tagFirstNsrG;

    @Schema(description = "一级标签环比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagFirstNsrR;
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Schema(description = "一级标签同比")
    private BigDecimal tagFirstNsrY;

    /**
     * 标签名称
     */
    @Schema(description = "标签名称")
    private String tagName;
    /**
     * 标签数值
     */
    @Schema(description = "标签数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagNsrC;
    /**
     * 标签图标
     */
    @Schema(description = "标签图标")
    private String tagNsrG;
    @Schema(description = "标签环比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagNsrR;
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Schema(description = "标签同比")
    private BigDecimal tagNsrY;

    @Schema(description = "观点数")
    private String opinionC;
    @Schema(description = "环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal opinionRP;
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Schema(description = "同比百分比")
    private BigDecimal opinionYP;

    int sort ;

    private String labelType;
}
