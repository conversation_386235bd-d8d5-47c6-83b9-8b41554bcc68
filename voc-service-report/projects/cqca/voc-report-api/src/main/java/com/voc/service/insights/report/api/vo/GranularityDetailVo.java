package com.voc.service.insights.report.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/28 上午11:21
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GranularityDetailVo implements Serializable {
    private String name;
    private BigDecimal value;
    private int sort;
    //指数数据
    private List<ExponentialGranularityVo> exponentialGranularity;
}
