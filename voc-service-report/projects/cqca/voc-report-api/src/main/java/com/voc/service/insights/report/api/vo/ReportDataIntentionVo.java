package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/29 下午4:24
 * @描述: 数据简报VO
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportDataIntentionVo {

    @Schema(description = "日期")
    private String reportDate;

    @Schema(description = "抱怨数值")
    private String complainC;


    @Schema(description = "投诉数值")
    private String complaintC;

    @Schema(description = "建议数值")
    private String suggestC;



    @Schema(description = "咨询数值")
    private String consultC;


    @Schema(description = "表扬数值")
    private String praiseC;

    @Schema(description = "陈述数值")
    private String otherC;



    @Schema(description = "抱怨数值合计")
    private String complainSum;


    @Schema(description = "投诉数值合计")
    private String complaintSum;

    @Schema(description = "建议数值合计")
    private String suggestSum;



    @Schema(description = "咨询数值合计")
    private String consultSum;

    @Schema(description = "表扬数值合计")
    private String praiseSum;

    @Schema(description = "陈述数值合计")
    private String otherSum;




    @Schema(description = "抱怨占比")
    private String complainP;

    @Schema(description = "投诉占比")
    private String complaintP;

    @Schema(description = "建议占比")
    private String suggestP;


    @Schema(description = "咨询占比")
    private String consultP;

    @Schema(description = "表扬占比")
    private String praiseP;

    @Schema(description = "陈述占比")
    private String otherP;


    @Schema(description = "抱怨环比")
    private String complainNsrRp;


    @Schema(description = "抱怨同比")
    private String complainNsrYp;

    @Schema(description = "投诉环比")
    private String complaintNsrRp;


    @Schema(description = "投诉同比")
    private String complaintNsrYp;

    @Schema(description = "建议环比")
    private String suggestNsrRp;

    @Schema(description = "建议同比")
    private String suggestNsrYp;


    @Schema(description = "咨询环比")
    private String consultNsrRp;


    @Schema(description = "咨询同比")
    private String consultNsrYp;

    @Schema(description = "表扬环比")
    private String praiseNsrRp;


    @Schema(description = "表扬同比")
    private String praiseNsrYp;

    @Schema(description = "陈述环比")
    private String otherNsrRp;

    @Schema(description = "陈述同比")
    private String otherNsrYp;

}
