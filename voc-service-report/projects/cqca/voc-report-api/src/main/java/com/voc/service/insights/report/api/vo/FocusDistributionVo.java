package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/30 上午9:08
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FocusDistributionVo {

    private String title;
    private BigDecimal tagSumC;
    /**
     * 日期
     */
    @Schema(description = "日期")
    private String date;
    /**
     * 聚焦观点标签名称
     */
    @Schema(description = "聚焦观点标签名称")
    private String tagName;
    /**
     * 聚焦观点标签数值
     */
    @Schema(description = "聚焦观点标签数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagC;
    /**
     * 聚焦观点标签百分比
     */
    @Schema(description = "聚焦观点标签百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagP;
    /**
     * 聚焦观点标签环比百分比
     */
    @Schema(description = "聚焦观点标签环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagRp;
    /**
     * 聚焦观点标签同比百分比
     */
    @Schema(description = "聚焦观点标签同")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tagYp;

    private String labelType;
    @Schema(description = "标签等级")
    private Integer level;
    @Schema(description = "一级标签名称")
    private String tagFirstLevelName;
    @Schema(description = "二级标签code")
    private String secondCode;
}
