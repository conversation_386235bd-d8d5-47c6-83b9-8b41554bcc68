package com.voc.service.insights.report.api.api;

import com.github.pagehelper.PageInfo;
import com.voc.service.insights.report.api.model.demo.params.OverviewParamsModel;
import com.voc.service.insights.report.api.vo.OpinionTrendVo;
import com.voc.service.insights.report.api.vo.ProductAndCompetingCarSeriesVo;
import com.voc.service.insights.report.api.vo.UserVoiceVo;
import com.voc.service.insights.report.api.vo.UsersInsightVo;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/1 下午2:35
 * @描述:
 **/
public interface IInsReportUsersExpectInsightService {
    /**
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.ExpectationDistributionVo
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/1 下午2:36
     * @描述 获取产品或服务期待分布
     **/
    List<UsersInsightVo> getExpectationDistributionBing(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/19 下午4:00
     * @描述   获取产品或服务期待分布详情
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.UsersInsightVo>
     **/
    List<UsersInsightVo> getExpectationDistributionDetails(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者: fanrong
     * @创建时间: 2024/11/1 下午2:37
     * @描述: 获取本竞品车系同时提及分析
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.ExpectationDistributionVo
     **/

   List<ProductAndCompetingCarSeriesVo> getCarSeriesMentionedAnalysis(OverviewParamsModel overviewParamsModel);
   /**
    * @创建者/修改者 fanrong
    * @创建/更新日期 2024/11/20 上午11:42
    * @描述   获取本竞品车系同时提及分析联动
    * @param overviewParamsModel
    * @return java.util.List<com.voc.service.insights.report.api.vo.ProductAndCompetingCarSeriesVo>
    **/
   List<ProductAndCompetingCarSeriesVo> getLinkedCarSeriesMentionedAnalysis(OverviewParamsModel overviewParamsModel);


    PageInfo<UserVoiceVo> getUserList(OverviewParamsModel overviewParamsModel);

    PageInfo<UserVoiceVo> getUserVoice(OverviewParamsModel overviewParamsModel);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/4 下午2:25
     * @描述  获取观点趋势
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.OpinionTrendVo
     **/
    OpinionTrendVo getOpinionTrend(OverviewParamsModel overviewParamsModel);

    OpinionTrendVo getLinkageOpinionTrend(OverviewParamsModel overviewParamsModel);

}
