package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.base.ExtendComQueryModel;
import com.voc.service.insights.report.api.vo.drilldown.*;

import java.util.List;

/**
 * 下钻服务接口
 */
public interface IDrillDownService {

    /**
     * 获取VOC体验值趋势
     *
     * @param queryModel 查询条件
     * @return VOC体验值趋势列表
     */
    List<VocExperienceTrendVo> getVocExperienceTrend(ExtendComQueryModel queryModel);

    /**
     * 获取全旅程客户体验值
     *
     * @param queryModel 查询条件
     * @return 全旅程客户体验值
     */
    CustomerJourneyExperienceVo getCustomerJourneyExperience(ExtendComQueryModel queryModel);

    /**
     * 获取全旅程TOP问题
     *
     * @param queryModel 查询条件
     * @return 全旅程TOP问题列表(TOP10)
     */
    List<TopIssuesVo> getTopIssues(ExtendComQueryModel queryModel);

    /**
     * 获取人群特征
     *
     * @param queryModel 查询条件
     * @return 人群特征
     */
    DemographicsVo getDemographics(ExtendComQueryModel queryModel);

    /**
     * 获取数据来源
     *
     * @param queryModel 查询条件
     * @return 数据来源列表(按提及量排序)
     */
    List<DataSourceVo> getDataSources(ExtendComQueryModel queryModel);

    /**
     * 获取提及量趋势
     *
     * @param queryModel 查询条件
     * @return 提及量趋势列表
     */
    List<MentionTrendVo> getMentionTrend(ExtendComQueryModel queryModel);

    /**
     * 获取词云图
     *
     * @param queryModel 查询条件
     * @return 词云图列表(按提及量排序)
     */
    List<WordCloudVo> getWordCloud(ExtendComQueryModel queryModel);

    /**
     * 获取地域分析
     *
     * @param queryModel 查询条件
     * @return 地域分析列表(按提及量排序)
     */
    List<RegionalAnalysisVo> getRegionalAnalysis(ExtendComQueryModel queryModel);
} 