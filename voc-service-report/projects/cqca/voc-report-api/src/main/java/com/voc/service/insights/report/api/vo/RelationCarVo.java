package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RelationCarVo implements Serializable {

    private String id;

    private String carSeriesCode;

    private String carSeriesName;

    @Schema(description = "是否选中")
    @Builder.Default
    <PERSON> checked = Boolean.FALSE;


}
