package com.voc.service.insights.report.api.vo.sounds;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "VOC声音数据VO")
public class VocSoundsVo {
    @Schema(description = "声音ID")
    private String id;
    
    @Schema(description = "数据唯一标识")
    private String dataId;
    
    @Schema(description = "渠道编码")
    private String channelCode;
    
    @Schema(description = "渠道名称")
    private String channel;
    
    @Schema(description = "品牌编码")
    private String brandCode;
    
    @Schema(description = "品牌名称")
    private String brand;
    
    @Schema(description = "车系编码")
    private String vehicleSeriesCode;
    
    @Schema(description = "车系名称")
    private String vehicleSeries;
    
    @Schema(description = "车型编码")
    private String vehicleModelCode;
    
    @Schema(description = "车型名称")
    private String vehicleModel;
    
    @Schema(description = "数据类型")
    private String labelType;
    
    @Schema(description = "情感")
    private String sentiment;
    
    @Schema(description = "意图")
    private String intention;
    
    @Schema(description = "热词")
    private String hotWord;
    
    @Schema(description = "用户旅程")
    private String userJourney;
    
    @Schema(description = "关键词")
    private String keywords;
    
    @Schema(description = "数据产生时间")
    private LocalDateTime dataCreateTime;
    
    @Schema(description = "数据抓取时间")
    private LocalDateTime createTime;
    
    @Schema(description = "经销商ID")
    private Long dealerId;
    
    @Schema(description = "经销商编码")
    private String dealerCode;
    
    @Schema(description = "经销商全称")
    private String dealerName;
    
    @Schema(description = "经销商所在省编码")
    private String dealerProvinceCode;
    
    @Schema(description = "经销商所在省")
    private String dealerProvince;
    
    @Schema(description = "经销商所在大区编码")
    private String dealerRegionalCode;
    
    @Schema(description = "经销商所在大区")
    private String dealerRegional;
    
    @Schema(description = "经销商所在市编码")
    private String dealerCityCode;
    
    @Schema(description = "经销商所在市")
    private String dealerCity;
    
    @Schema(description = "车辆购买日期")
    private LocalDate vehiclePurchaseDate;
    
    @Schema(description = "车辆生产日期")
    private LocalDate vehicleProductionDate;
    
    @Schema(description = "车辆出厂日期")
    private LocalDate vehicleFactoryReleaseDate;
    
    @Schema(description = "车辆车架号")
    private String vehicleVin;
    
    @Schema(description = "oneId")
    private String oneId;
    
    @Schema(description = "客户姓名")
    private String custName;
    
    @Schema(description = "客户手机号")
    private String custMobile;
    
    @Schema(description = "客户年龄")
    private Integer custAge;
    
    @Schema(description = "客户性别")
    private String custGender;
    
    @Schema(description = "客户常驻省份编码")
    private String custProvinceCode;
    
    @Schema(description = "客户常驻省份")
    private String custProvince;
    
    @Schema(description = "客户常驻市编码")
    private String custCityCode;
    
    @Schema(description = "客户常驻市")
    private String custCity;
    
    @Schema(description = "客户常驻区编码")
    private String custDistrictCode;
    
    @Schema(description = "客户常驻区")
    private String custDistrict;
    
    @Schema(description = "客户最高学历")
    private String custHighestEdu;
    
    @Schema(description = "客户月收入")
    private String custMonthlyIncome;
    
    @Schema(description = "客户最近一次购车时间")
    private LocalDate custLastPurchaseDate;
    
    @Schema(description = "客户类型")
    private String custType;
    
    @Schema(description = "是否车主")
    private Boolean isVehicleOwner;
    
    @Schema(description = "原文内容类型")
    private String contentType;
    
    @Schema(description = "工单ID")
    private Long workOrderId;
    
    @Schema(description = "是否主贴")
    private Boolean isMainPost;
    
    @Schema(description = "帖子标题")
    private String postTitle;
    
    @Schema(description = "帖子原文链接")
    private String postOriginalLink;
    
    @Schema(description = "帖子原文详情")
    private String postOriginalContent;
    
    @Schema(description = "问卷类型（问卷类型拥有）")
    private String questType;
    
    @Schema(description = "问卷题目/内容（问卷类型拥有）")
    private String questQuestionContent;
    
    @Schema(description = "问卷答案分数（问卷类型拥有）")
    private Integer questAnswerScore;
    
    @Schema(description = "问卷业务类型（问卷类型拥有）")
    private String questBusinessType;
    
    @Schema(description = "问卷业务场景（问卷类型拥有）")
    private String questBusinessScenario;
    
    @Schema(description = "观点")
    private String topic;
    
    // 标签字段组（VRT/商品化属性/全领域业务等）
    @Schema(description = "VRT标签编码1级")
    private String vtrTagFirstCode;
    
    @Schema(description = "VRT标签编码2级")
    private String vtrTagSecondCode;
    
    @Schema(description = "VRT标签编码3级")
    private String vtrTagThreeCode;
    
    @Schema(description = "VRT标签1级")
    private String vtrTagFirst;
    
    @Schema(description = "VRT标签2级")
    private String vtrTagSecond;
    
    @Schema(description = "VRT标签3级")
    private String vtrTagThree;
    
    // 商品化属性标签字段组
    @Schema(description = "商品化属性标签编码1级")
    private String comTagFirstCode;
    @Schema(description = "商品化属性标签编码2级")
    private String comTagSecondCode;
    @Schema(description = "商品化属性标签编码3级")
    private String comTagThreeCode;
    @Schema(description = "商品化属性标签1级")
    private String comTagFirst;
    @Schema(description = "商品化属性标签2级")
    private String comTagSecond;
    @Schema(description = "商品化属性标签3级")
    private String comTagThree;

    // 全领域业务标签字段组
    @Schema(description = "全领域业务标签编码1级")
    private String adbTagFirstCode;
    @Schema(description = "全领域业务标签编码2级")
    private String adbTagSecondCode;
    @Schema(description = "全领域业务标签编码3级")
    private String adbTagThreeCode;
    @Schema(description = "全领域业务标签1级")
    private String adbTagFirst;
    @Schema(description = "全领域业务标签2级")
    private String adbTagSecond;
    @Schema(description = "全领域业务标签3级")
    private String adbTagThree;

    // 口碑评价指标字段组
    @Schema(description = "口碑评价指标编码1级")
    private String womTagFirstCode;
    @Schema(description = "口碑评价指标编码2级")
    private String womTagSecondCode;
    @Schema(description = "口碑评价指标编码3级")
    private String womTagThreeCode;
    @Schema(description = "口碑评价指标1级")
    private String womTagFirst;
    @Schema(description = "口碑评价指标2级")
    private String womTagSecond;
    @Schema(description = "口碑评价指标3级")
    private String womTagThree;

    // 客户体验指标字段组
    @Schema(description = "客户体验指标编码1级")
    private String cxTagFirstCode;
    @Schema(description = "客户体验指标编码2级")
    private String cxTagSecondCode;
    @Schema(description = "客户体验指标编码3级")
    private String cxTagThreeCode;
    @Schema(description = "客户体验指标1级")
    private String cxTagFirst;
    @Schema(description = "客户体验指标2级")
    private String cxTagSecond;
    @Schema(description = "客户体验指标3级")
    private String cxTagThree;

    // 全旅程客户签字段组
    @Schema(description = "全旅程客户签编码1级")
    private String cjTagFirstCode;
    @Schema(description = "全旅程客户签编码2级")
    private String cjTagSecondCode;
    @Schema(description = "全旅程客户签编码3级")
    private String cjTagThreeCode;
    @Schema(description = "全旅程客户签1级")
    private String cjTagFirst;
    @Schema(description = "全旅程客户签2级")
    private String cjTagSecond;
    @Schema(description = "全旅程客户签3级")
    private String cjTagThree;

    // 销售线索字段组
    @Schema(description = "销售线索编码1级")
    private String slTagFirstCode;
    @Schema(description = "销售线索编码2级")
    private String slTagSecondCode;
    @Schema(description = "销售线索编码3级")
    private String slTagThreeCode;
    @Schema(description = "销售线索1级")
    private String slTagFirst;
    @Schema(description = "销售线索2级")
    private String slTagSecond;
    @Schema(description = "销售线索3级")
    private String slTagThree;

    // 全媒体指标字段组
    @Schema(description = "全媒体指标编码1级")
    private String omTagFirstCode;
    @Schema(description = "全媒体指标编码2级")
    private String omTagSecondCode;
    @Schema(description = "全媒体指标编码3级")
    private String omTagThreeCode;
    @Schema(description = "全媒体指标1级")
    private String omTagFirst;
    @Schema(description = "全媒体指标2级")
    private String omTagSecond;
    @Schema(description = "全媒体指标3级")
    private String omTagThree;
    
    // 时间周期字段
    @Schema(description = "数据产生周期-周")
    private String dataCreateWeek;
    
    @Schema(description = "数据产生周期-月")
    private String dataCreateMonth;
    
    @Schema(description = "数据产生周期-季")
    private String dataCreateQuarter;
    
    @Schema(description = "数据产生周期-年")
    private String dataCreateYear;
}
