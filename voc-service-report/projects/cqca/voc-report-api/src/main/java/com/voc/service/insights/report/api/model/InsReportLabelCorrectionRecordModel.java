package com.voc.service.insights.report.api.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "ins_label_correction_record")
public class InsReportLabelCorrectionRecordModel implements Serializable {

    private static final long serialVersionUID = 1L;

    private String newId;

    private String id;

    private String workId;
    private String clientId;
    private String channelId;
    private String contentType;

    private String inputDataId;

    private String originalId;

    private String sampleDataType;

    private String originalTextScene;

    private String brandCodeName;

    private String carSeriesName;

    private String labelType;

    private String labelTypeLevelFirst;

    private String labelTypeLevelSecond;

    private String labelTypeLevelThree;

    private String labelTypeLevelFour;

    private String labelTypeLevelFive;

    private String sentiment;

    private String intentionType;

    private String scenario;

    private String topic;

    private String opinion;

    private String subject;

    private String faultLevel;

    private String description;

    private String sentimentScore;

    private String keywords;

    private LocalDateTime publishTime;

    private Integer modelType;

    private Object extFields;

    Object bizExtAttrs;

    String oneId;

    @Builder.Default
    private LocalDateTime operateTime = LocalDateTime.now();

    private String operateUser;

    private String auditStatus;

    private String errorType;

    private String correctionInfo;

    private String correctionLabel;

    private String correctionTopic;

    private String correctionSentiment;

    private String correctionIntention;


    @Builder.Default
    private LocalDateTime updateTime = LocalDateTime.now();


}
