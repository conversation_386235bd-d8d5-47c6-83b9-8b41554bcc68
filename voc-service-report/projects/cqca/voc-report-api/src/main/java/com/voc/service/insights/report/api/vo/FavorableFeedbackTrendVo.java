package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2024/12/13 下午4:09
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FavorableFeedbackTrendVo implements Serializable {
    @Schema(description = "整体好评反馈标题")
    private String globalFeedbackTitle;
    @Schema(description = "整体好评反馈值")
    private BigDecimal globalFeedbackC;
    @Schema(description = "整体好评反馈环比百分比")
    private BigDecimal globalFeedbackRp;
    @Schema(description = "整体好评反馈同比百分比")
    private BigDecimal globalFeedbackYp;
    @Schema(description = "整体好评反馈表情")
    private String globalFeedbackG;

    @Schema(description = "产品好评反馈标题")
    private String prodFeedbackTitle;
    @Schema(description = "产品好评反馈值")
    private BigDecimal prodFeedbackC;
    @Schema(description = "产品好评反馈环比百分比")
    private BigDecimal prodFeedbackRp;
    @Schema(description = "产品好评反馈同比百分比")
    private BigDecimal prodFeedbackYp;

    @Schema(description = "服务好评反馈标题")
    private String serviceFeedbackTitle;
    @Schema(description = "服务好评反馈值")
    private BigDecimal serviceFeedbackC;
    @Schema(description = "服务好评反馈环比百分比")
    private BigDecimal serviceFeedbackRp;
    @Schema(description = "服务好评反馈同比百分比")
    private BigDecimal serviceFeedbackYp;

    @Schema(description = "观点名称")
    private String topicName;
    @Schema(description = "观点数值")
    private BigDecimal topicC;
    @Schema(description = "观点值环比百分比")
    private BigDecimal topicRp;
    @Schema(description = "观点值同比百分比")
    private BigDecimal topicYp;
    @Schema(description = "观点值G值")
    private String topicG;
    @Schema(description = "日期")
    private String date;

    private String labelType;

}
