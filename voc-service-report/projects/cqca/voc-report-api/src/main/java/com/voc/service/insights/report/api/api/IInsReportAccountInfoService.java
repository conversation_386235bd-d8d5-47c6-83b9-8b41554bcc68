package com.voc.service.insights.report.api.api;

import com.github.pagehelper.PageInfo;
import com.voc.service.common.model.UserModel;
import com.voc.service.common.response.Result;
import com.voc.service.insights.report.api.model.InsReportAccountInfoModel;
import com.voc.service.insights.report.api.model.InsReportRoleQueryModel;
import com.voc.service.insights.report.api.model.StaSysDepartModel;
import com.voc.service.insights.report.api.vo.InsReportAccountInfoVo;

import java.util.List;
import java.util.Map;

/**
 * @创建者: fanrong
 * @创建时间: 2024/3/4 13:51
 * @描述:
 **/
public interface IInsReportAccountInfoService {
    /**
     * @param accountInfoModel
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/3/4 13:52
     * @描述 新增账号信息
     **/
    void saveAccountInfo(InsReportAccountInfoModel accountInfoModel);

    /**
     * @param accountInfoModel
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/3/5 09:00
     * @描述 更新账号信息
     **/
    void updateAccountInfo(InsReportAccountInfoModel accountInfoModel);

    /**
     * @param accountInfoModel
     * @return com.voc.service.insights.engine.vo.InsAccountInfoVo
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/3/5 10:20
     * @描述 根据id获取账号信息
     **/
    InsReportAccountInfoVo findAccountInfo(InsReportAccountInfoModel accountInfoModel);

    /**
     * @param accountInfoModel
     * @return java.util.List<com.voc.service.insights.engine.vo.InsAccountInfoVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/3/5 14:28
     * @描述 按条件分页查询账号信息
     **/
    PageInfo findAccountInfoList(InsReportAccountInfoModel accountInfoModel);

    /**
     * @param accountInfoModel
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/3/5 17:02
     * @描述 根据id删除账号信息
     **/
    void deleteAccountInfo(InsReportAccountInfoModel accountInfoModel);


    List<StaSysDepartModel> findDepartList();

    /**
     * @param userModel
     * @return java.util.List<com.voc.service.common.model.UserModel>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/3/6 16:29
     * @描述 查询所有账号信息
     **/
    List<UserModel> findAllAccountInfoList(UserModel userModel);


    Result<?> queryRoleALlList(InsReportRoleQueryModel model);
}
