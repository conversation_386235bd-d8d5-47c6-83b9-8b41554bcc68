package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.vo.sounds.VocSoundsVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * VOC声音数据服务接口
 */
@Schema(description = "VOC声音数据服务接口")
public interface IVocSoundsService {

    /**
     * 批量插入测试数据
     * 
     * @param count 插入数据条数
     * @return 插入成功的条数
     */
    @Operation(summary = "批量插入测试数据", description = "向VOC声音表中批量插入规律性测试数据")
    int insertTestData(int count);

    /**
     * 根据ID查询声音数据
     * 
     * @param id 声音ID
     * @return 声音数据
     */
    @Operation(summary = "根据ID查询声音数据", description = "根据声音ID查询单条声音数据")
    VocSoundsVo getById(String id);

    /**
     * 批量插入声音数据
     * 
     * @param vocSoundsList 声音数据列表
     * @return 插入成功的条数
     */
    @Operation(summary = "批量插入声音数据", description = "批量插入声音数据到数据库")
    int batchInsert(List<VocSoundsVo> vocSoundsList);

    /**
     * 清空测试数据
     * 
     * @return 删除的条数
     */
    @Operation(summary = "清空测试数据", description = "清空所有测试数据")
    int clearTestData();

    /**
     * 统计声音数据总数
     * 
     * @return 总数
     */
    @Operation(summary = "统计声音数据总数", description = "统计VOC声音数据总数")
    long countAll();
} 