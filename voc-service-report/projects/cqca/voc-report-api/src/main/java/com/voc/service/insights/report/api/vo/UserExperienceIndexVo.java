package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/29 下午6:04
 * @描述: 用户体验指数
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserExperienceIndexVo {
    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;
    /**
     * 标签名称
     */
    @Schema(description = "标签名称")
    private String tagName;
    /**
     * 体验指数值
     */
    @Schema(description = "体验指数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nsrC;
    /**
     * 体验指数环比百分比
     */
    @Schema(description = "体验指数环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nsrRp;
    /**
     * 体验指数同比百分比
     */
    @Schema(description = "体验指数同比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal nsrYp;
    /**
     * 图标
     */
    @Schema(description = "图标")
    private String nsrG;

    private String firstCode;


}
