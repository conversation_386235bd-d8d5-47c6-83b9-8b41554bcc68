package com.voc.service.insights.report.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/20 下午5:54
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagLibelMappingVo {
    /**
     * 一级标签名称
     */
    private String tagName1;
    /**
     * 二级标签名称
     */
    private String tagName2;
    /**
     * 标签类型
     */
    private String tagType;
    /**
     * 二级标签编码
     */
    private String tagCode;
}
