package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/29 下午6:51
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductExperienceIndexVo {
    @Schema(description = "名称")
    private String name;

    @Schema(description = "图片地址")
    private String imgUrl;

    @Schema(description = "负面率")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal negativeRate;

    @Schema(description = "增长量")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal growth;

    @Schema(description = "提及量")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal mentionCount;

    @Schema(description = "增长趋势")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private List<BigDecimal> growthTrend;

}
