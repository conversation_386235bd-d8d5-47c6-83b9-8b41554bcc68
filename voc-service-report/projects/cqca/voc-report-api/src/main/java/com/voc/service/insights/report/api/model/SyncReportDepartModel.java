package com.voc.service.insights.report.api.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncReportDepartModel {

    @Schema(description = "每页多少数据数(默认10)")
    private String size;

    @Schema(description = "第几页(默认从第1页)")
    private String page;

    @Schema(description = "应用注册编码，由数字工作台提供")
    private String appCode;

    @Schema(description = "精确到毫秒的时间戳（13位）")
    private Long timestamp;

    @Schema(description = "MD5(client_id+client_secret+timestamp)")
    private String sign;

}
