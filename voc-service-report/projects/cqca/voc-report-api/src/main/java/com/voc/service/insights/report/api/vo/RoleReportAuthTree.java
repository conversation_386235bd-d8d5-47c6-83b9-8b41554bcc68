package com.voc.service.insights.report.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoleReportAuthTree implements Serializable {
    @Schema(description = "菜单ID")
    String id;
    @Schema(description = "Code")
    String code;
    @Schema(description = "父级ID")
    String pid;
    @Schema(description = "icon")
    String icon;
    @Schema(description = "菜单名称")
    String name;
    @Schema(description = "路径")
    String path;
    String permissionKey;
    @Schema(description = "是否是按钮")
    Boolean checkButton;
    Integer sort;
    @Schema(description = "是否选中")
    @Builder.Default
    Boolean checked = Boolean.FALSE;
    List<RoleReportAuthTree> children;
    List<RoleReportAuthTree> drillDownPermissionList;
    @Builder.Default
    Boolean drillDownPermission = Boolean.FALSE;
    String apiPath;
    String alwaysShow;

}
