package com.voc.service.insights.report.api.api;


import com.voc.service.insights.report.api.vo.ConditionVo;

public interface IConditionFilters {
    String STATUS = "enable_type";
    String STOP_OR_ENABLE = "stop_or_enable";
    String PERIOD = "period";
    String EMOTION = "emotion";
    String INTENTION = "intention";
    String USER_JOURNEY = "user_journey";
    String CUSTOMER_TYPE = "customer_type";
    String CUSTOMER_GENDER = "customer_gender";
    String CAR_OWNER_AGE = "car_owner_age";
    String CAR_AGE = "car_age";
    ConditionVo get(String key);
}
