package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.InsReportCenterInfoModel;
import com.voc.service.insights.report.api.vo.InsReportCenterInfoVo;
import com.voc.service.insights.report.api.vo.InsReportRegionConfigVo;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;


public interface IInsReportCenterService {

    List<InsReportCenterInfoVo> findReportList();

    void generateReport(InsReportCenterInfoModel reportCenterInfoModel, HttpServletResponse response);

    List<InsReportRegionConfigVo> getRegionTree(InsReportCenterInfoModel reportCenterInfoModel);
}
