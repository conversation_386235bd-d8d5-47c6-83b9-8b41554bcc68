package com.voc.service.insights.report.api.model.indicators;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Title: IndicatorsResultModel
 * @Package: com.voc.service.insights.report.api.model.indicators
 * @Description:
 * @Author: cuick
 * @Date: 2024/11/19 10:25
 * @Version:1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndicatorsNSRResultModel {
    @Builder.Default
    private String name = "NSR";

    private BigDecimal value;
}
