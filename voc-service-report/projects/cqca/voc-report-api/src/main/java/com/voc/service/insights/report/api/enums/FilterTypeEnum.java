package com.voc.service.insights.report.api.enums;

/**
 * @创建者: fanrong
 * @创建时间: 2024/12/25 下午9:09
 * @描述:
 **/
public enum FilterTypeEnum {
    NOT_CAR_TYPE_INFO("无品牌无车型信息",1),
    NOT_CAR_SERIES_INFO("无车型信息",2);



    private final String name;
    private final int sort;

    FilterTypeEnum(String name, int sort) {
        this.name = name;
        this.sort = sort;
    }

    public String getName() {
        return this.name;
    }

    public int getSort() {
        return this.sort;
    }


    public static FilterTypeEnum getByName(String name) {
        for (FilterTypeEnum type : values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }
}
