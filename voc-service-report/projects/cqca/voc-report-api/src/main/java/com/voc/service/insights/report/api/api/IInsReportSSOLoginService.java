package com.voc.service.insights.report.api.api;

import com.voc.service.common.model.UserModel;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/16 下午3:38
 * @描述:
 **/
public interface IInsReportSSOLoginService {

    String getAccessToken(final String url);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/10/16 下午3:50
     * @描述  根据access_token 换取用户信息
     * @param accessToken
     * @return java.lang.String
     **/
    String getUserInfo(String accessToken);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/10/16 下午4:00
     * @描述  根据用户id获取token
     * @param userId
     * @return java.lang.String
     **/
    String getTokenByUserId(String token, String userId);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/10/22 下午2:25
     * @描述       token校验
     * @param userModel
     * @return java.lang.Boolean
     **/
    Boolean checkToken(String token, UserModel userModel);
}
