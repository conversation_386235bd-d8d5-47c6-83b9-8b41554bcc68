#application:
#  security:
#    jwt:
#      secret-key: 405E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
#      expiration: 7200 # a day

#是否开启验证码校验功能
captcha.enabled: false

security:
  ignoring:
    urls:
    - /auth/login
    - /auth/logout
    - /login
    - /free/login
    - /logout
    - /sso
    - /logs/**
    - /randomImage/**
    - /user/register
    - /user/userinfo
    - /user/valiedatePassword
    - /v2/api-docs
    - /v3/api-docs
    - /v3/api-docs/**
    - /actuator/**
    - /swagger-resources
    - /swagger-resources/**
    - /configuration/ui
    - /configuration/security
    - /randomImage/**
    - /instances
    - /actuator
    - /actuator/**
    - /swagger-ui/**
    - /webjars/**
    - /swagger-ui.html
    - /error
    - /favicon.ico
    - /doc.html
    - /druid/**
    - /tools/config/**
    tokens:
      - eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoiMSIsImlkZW50aXR5X3R5cGUiOiJiYXNlIiwiYXBwX2lkIjoiYW5hbHlzaXMiLCJ1c2VybmFtZSI6IkV2NjFsa1hvdktyWjVXZmVhd3ExODdaMUV6c3o1N20wblR6Smc1QWxpbzF2WTBkMWRDSXltbnErby9aYlQzaU0iLCJzdWIiOiIxIiwiaWF0IjoxNzEwNDExMzg0LCJleHAiOjQwNzU2MTEzODR9.cH4p0nn6NfFYLZipGO8y7aakLrBicotPDjcGkQuOi_g


#密码校验策略提示信息
pass.policy:
    messages:
      HISTORY_VIOLATION: Password matches one of %1$s previous passwords.
      ILLEGAL_WORD: Password contains the dictionary word '%1$s'.
      ILLEGAL_WORD_REVERSED: Password contains the reversed dictionary word '%1$s'.
      ILLEGAL_DIGEST_WORD: Password contains a dictionary word.
      ILLEGAL_DIGEST_WORD_REVERSED: Password contains a reversed dictionary word.
      ILLEGAL_MATCH: Password matches the illegal pattern '%1$s'.
      ALLOWED_MATCH: Password must match pattern '%1$s'.
      ILLEGAL_CHAR: Password %2$s the illegal character '%1$s'.
      ALLOWED_CHAR: Password %2$s the illegal character '%1$s'.
      ILLEGAL_QWERTY_SEQUENCE: Password contains the illegal QWERTY sequence '%1$s'.
      ILLEGAL_ALPHABETICAL_SEQUENCE: Password contains the illegal alphabetical sequence '%1$s'.
      ILLEGAL_NUMERICAL_SEQUENCE: Password contains the illegal numerical sequence '%1$s'.
      ILLEGAL_USERNAME: Password %2$s the user id '%1$s'.
      ILLEGAL_USERNAME_REVERSED: Password %2$s the user id '%1$s' in reverse.
      ILLEGAL_WHITESPACE: Password %2$s a whitespace character.
      ILLEGAL_NUMBER_RANGE: Password %2$s the number '%1$s'.
      ILLEGAL_REPEATED_CHARS: Password contains %3$s sequences of %1$s or more repeated characters, but only %2$s allowed- %4$s.
      INSUFFICIENT_UPPERCASE: Password must contain %1$s or more uppercase characters.
      INSUFFICIENT_LOWERCASE: Password must contain %1$s or more lowercase characters.
      INSUFFICIENT_ALPHABETICAL: Password must contain %1$s or more alphabetical characters.
      INSUFFICIENT_DIGIT: Password must contain %1$s or more digit characters.
      INSUFFICIENT_SPECIAL: Password must contain %1$s or more special characters.
      INSUFFICIENT_CHARACTERISTICS: Password matches %1$s of %3$s character rules, but %2$s are required.
      INSUFFICIENT_COMPLEXITY: Password meets %2$s complexity rules, but %3$s are required.
      INSUFFICIENT_COMPLEXITY_RULES: No rules have been configured for a password of length %1$s.
      SOURCE_VIOLATION: Password cannot be the same as your %1$s password.
      TOO_LONG: Password must be no more than %2$s characters in length.
      TOO_SHORT: Password must be %1$s or more characters in length.
      TOO_MANY_OCCURRENCES: Password contains %2$s occurrences of the character '%1$s', but at most %3$s are allowed.
      EXPOSED_HAVEIBEENPWNED: Password is exposed from previous leaks, seen %1$s times before. Consider this password unusable.
      IO_ERROR_HAVEIBEENPWNED: Error communicating with %1$s


threadpool:
  async:
    executor:
      enabled: true
      corePoolSize: 100
      maxPoolSize: 2000
      queueCapacity: 3000
      threadNamePrefix: auth-


logging.level:
  com.voc: debug