<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">
    <!--应用名称-->
    <springProperty scope="context" name="springAppName" source="spring.application.name" />
    <!--日志文件路径配置-->
    <property name="log.path" value="/applogs/${springAppName}" />
    <!--yml配置的日志级别-->
    <springProperty scope="context" name="logLevel" source="log_level" />

    <!--自定义日志输出格式 -->
    <!-- <property name="log_pattern_console"
        value="%white([${skywalking.agent.service_name}]) %green(%d{yyyy-MM-dd HH:mm:ss.SSS}) [%-58X{tid}] %green(.%-15.15thread.) [%-5level] [%-40.40logger{40}-%-4L]: %msg%n" />

    <property name="log_pattern"
        value="[${skywalking.agent.service_name}] %d{yyyy-MM-dd HH:mm:ss.SSS} [%X{tid}] .%-15thread. [%-5level] [%-36logger{36}-%-4L]: %msg%n" />

    <property name="log_pattern_console"
        value="%white([${skywalking.agent.service_name}]) %d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai} %yellow([%-58X{tid}]) [.%-15.15thread.] %green(%-5level) %cyan(%logger{36}) -%msg%n" />

    <property name="log_pattern_logs"
        value="[${skywalking.agent.service_name}] %d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai} [%X{tid}] [%thread] %-5level %logger{36} -%msg%n" />
     -->
    <property name="log_pattern_console"
              value="%white([${skywalking.agent.service_name}]) %d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai} %yellow([%X{tid}]) .%-15.15thread. %green([%-5level]) %cyan([%-40.40logger{40}-%-4L]): %msg%n" />

    <property name="log_pattern_logs"
              value="[${skywalking.agent.service_name}] %d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai} [%X{tid}] .%-15.15thread. [%-5level] [%-40.40logger{40}-%-4line]: %msg%n" />

    <!--控制台日志输出-->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout
                    class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <Pattern>${log_pattern_console}</Pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
        <queueSize>1024</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="STDOUT" />
    </appender>
    <!--gRPC
    报告程序到sw-ocp
    https://skywalking.apache.org/docs/skywalking-java/v9.1.0/en/setup/service-agent/java-agent/application-toolkit-logback-1.x/-->
    <appender name="grpc-log"
              class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout
                    class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <Pattern>${log_pattern_logs}</Pattern>
            </layout>
        </encoder>
    </appender>
    <!--2.
    输出到文档-->
    <!-- 2.1 level为 INFO 日志，时间滚动输出 -->
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文档的路径及文档名 -->
        <file>${log.path}/info.log</file>
        <!--日志文档输出格式 -->
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout
                    class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${log_pattern_logs}</pattern>
            </layout>
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 每天日志归档路径以及格式 -->
            <fileNamePattern>${log.path}/info-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>200MB</maxFileSize>
            <!--日志文档保留天数 -->
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <!-- 此日志文档只记录info级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>  <!-- 如果命中就禁止这条日志 -->
            <onMismatch>ACCEPT</onMismatch>  <!-- 如果没有命中就使用这条规则 -->
        </filter>
        <encoder>
            <pattern>${log_pattern_logs}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--如果只是想要Error
        级别的日志，那么需要过滤一下，默认是 info 级别的，ThresholdFilter-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>error</level>
        </filter>
        <!--日志名称，如果没有File
        属性，那么只会使用FileNamePattern的文件路径规则
        如果同时有<File>和<FileNamePattern>，那么当天日志是<File>，明天会自动把今天
        的日志改名为今天的日期。即，<File> 的日志都是当天的。
        -->
        <File>${log.path}/error.log</File>
        <!--滚动策略，按照时间滚动TimeBasedRollingPolicy-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--文件路径,定义了日志的切分方式——把每一天的日志归档到一个文件中,以防止日志填满整个磁盘空间-->
            <FileNamePattern>${log.path}/error.%d{yyyy-MM-dd}.log.gz</FileNamePattern>
            <!--只保留最近90天的日志-->
            <maxHistory>90</maxHistory>
            <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
        </rollingPolicy>
        <!--日志输出编码格式化-->

        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout
                    class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${log_pattern_logs}</pattern>
            </layout>
        </encoder>
    </appender>
    <!--1、root节点是必选节点，用来指定最基础的日志输出级别，只有一个level属性。
    level:用来设置打印级别，大小写无关：TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，不能设置为INHERITED或者同义词NULL。
    默认是DEBUG。
    可以包含零个或多个元素，标识这个appender将会添加到这个loger。
    -->
    <appender name="info_async" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="INFO_FILE" />
        <includeCallerData>true</includeCallerData>
    </appender>
    <appender name="error_async" class="ch.qos.logback.classic.AsyncAppender">
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="ERROR_FILE" />
    </appender>

    <root level="INFO">
        <appender-ref ref="ASYNC" />
        <appender-ref ref="grpc-log" />
        <appender-ref ref="info_async" />
        <appender-ref ref="error_async" />
    </root>
</configuration>