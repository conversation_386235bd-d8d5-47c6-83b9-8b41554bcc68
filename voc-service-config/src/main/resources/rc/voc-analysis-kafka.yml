
spring:
  kafka:
    #bootstrap-servers: 172.16.80.16:30127
    bootstrap-servers: 172.16.80.16:30130,172.16.80.16:30131,172.16.80.16:30132
    producer:
      enable.idempotence: true
      security.protocol: PLAINTEXT
      # 发生错误后，消息重发的次数 ，0为不启用重试机制，默认int最大值
      retries: 3
      # 当有多个消息需要被发送到统一分区时，生产者会把他们放在同一批次里。该参数指定了一个批次可以使用的内存大小，按照字节数计算
      batch-size: 16384
      # 生产者可以使用的总内存字节来缓冲等待发送到服务器的记录
      buffer-memory: 33554432
      # 键的序列化方式
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 值的序列化方式
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      # acks 应答机制
      # acks=0 ： 生产者发送过来的数据，不需要等数据落盘应答。
      # acks=1 ： 生产者发送过来的数据，Leader 收到数据后应答。
      # acks=all ：只有当所有参与复制的节点全部收到消息时，生产者才会收到一个来自服务器的成功响应。
      acks: -1
      properties:
        sasl:
          mechanism: PLAIN
          #jaas.config: com.voc.service.components.kafka.security.plain.CustomPlainLoginModule required username="producer" password="producer@futong2024";
          jaas.config: org.apache.kafka.common.security.plain.PlainLoginModule required username="producer" password="producer@futong2024";
        # 批量发送，延迟为1毫秒，启用该功能能有效减少生产者发送消息次数，从而提高并发量
        linger.ms: 1
        # 对发送的数据进行压缩 支持压缩类型：none、gzip、snappy、lz4 和 zstd。
        #partitioner.class: com.yh.kafka.config.CustomizePartitioner
        partitioner.class: org.apache.kafka.clients.producer.internals.DefaultPartitioner
      compression-type: "snappy"
      # 开启事务
      #transaction-id-prefix: analysis-transaction
    consumer:
      group-id: default
      # 是否自动提交偏移量，默认值是true,为了避免出现重复数据和数据丢失，可以把它设置为false,然后手动提交偏移量
      enable-auto-commit: true
      # 自动提交的时间间隔 在Spring Boot 2.x 版本中这里采用的值的类型Duration 需要符合特定的格式，如1S，1M，2H，5D
      auto-commit-interval: 1s
      # 该属性指定了消费者在读取一个没有偏移量的分区或者偏移量无效的情况下该作何处理：
      # latest（默认值）在偏移量无效的情况下，消费者将从最新的记录开始读取数据（在消费者启动之后生成的记录）
      # earliest ：在偏移量无效的情况下，消费者将从起始位置读取分区的记录
      # none（如果无offset就抛出异常）
      auto-offset-reset: earliest
      # 键的反序列化方式
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 值的反序列化方式
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 这个参数允许消费者指定从broker读取消息时最小的Payload的字节数。当消费者从broker读取消息时，如果数据字节数小于这个阈值，broker会等待直到有足够的数据，然后才返回给消费者。对于写入量不高的主题来说，这个参数可以减少broker和消费者的压力，因为减少了往返的时间。而对于有大量消费者的主题来说，则可以明显减轻broker压力。
      fetch-min-size: 1 #默认值： 1
      #上面的fetch.min.bytes参数指定了消费者读取的最小数据量，而这个参数则指定了消费者读取时最长等待时间，从而避免长时间阻塞。这个参数默认为500ms。
      fetch-max-wait: 500
      # 这个参数控制一个poll()调用返回的记录数，即consumer每次批量拉多少条数据。
      max-poll-records: 500

    listener:
      # 在监听器容器中运行的线程数,创建多少个consumer，值必须小于等于Kafk Topic的分区数。
      concurrency: 1 # 推荐设置为topic的分区数
      # 当每一条记录被消费者监听器（ListenerConsumer）处理之后提交
      # RECORD
      # 当每一批poll()的数据被消费者监听器（ListenerConsumer）处理之后提交
      # BATCH
      # 当每一批poll()的数据被消费者监听器（ListenerConsumer）处理之后，距离上次提交时间大于TIME时提交
      # TIME
      # 当每一批poll()的数据被消费者监听器（ListenerConsumer）处理之后，被处理record数量大于等于COUNT时提交
      # COUNT
      # TIME |　COUNT　有一个条件满足时提交
      # COUNT_TIME
      # 当每一批poll()的数据被消费者监听器（ListenerConsumer）处理之后, 手动调用Acknowledgment.acknowledge()后提交
      # MANUAL
      # 手动调用Acknowledgment.acknowledge()后立即提交，一般使用这种
      # MANUAL_IMMEDIATE
      #listner负责ack，每调用一次，就立即commit
      #ack-mode: RECORD
      # 消费监听接口监听的主题不存在时，默认会报错
      missing-topics-fatal: false
      # 使用批量消费需要将listener的type设置为batch，该值默认为single
      #type: batch