spring:
  data:
    redis:
      host: ${k8s_host}
      port: 30102
      password: PBE(vFJ572LLStzuGk3HBRSRIutVGW5EHqlJjeheZdq8RVXYtDLePl+9b9XLyvEXmuVv)
      database: 5
      #cluster:
      #  nodes:
      #  - ************:31961
      #  - ************:32498
      #sentinel:
      #  master: mymaster
      #  nodes:
      #  - ************:30345
      #  - ************:30147
      lettuce:
        pool:
          # 最小空闲连接
          min-idle: 1
          # 最大空闲连接
          max-idle: 5
          # 最大连接数
          max-active: 100
          # 连接分配应该阻塞的最大时间
          max-wait: 2000


jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  #hidePackages: com.alibaba
  local:
    default:
      type: caffeine
      limit: 10000
      keyConvertor: bean:myFastjson2KeyConvertor #其他可选：fastjson/jackson
      expireAfterWriteInMillis: 100000
  remote:
    default:
      type: redis
      keyConvertor: bean:myFastjson2KeyConvertor
      #keyConvertor: com.alicp.jetcache.support.FastjsonKeyConvertor
      broadcastChannel: ${spring.application.name}
      valueEncoder: kryo5 #其他可选：java/kryo/kryo5
      valueDecoder: kryo5 #其他可选：java/kryo/kryo5
      keyPrefix: '${system.appid}:'
      poolConfig:
        minIdle: ${spring.data.redis.lettuce.pool.min-idle}
        maxIdle: ${spring.data.redis.lettuce.pool.max-idle}
        maxTotal: ${spring.data.redis.lettuce.pool.max-wait}
      host: ${spring.data.redis.host}
      port: ${spring.data.redis.port}
      password: ${spring.data.redis.password}
      database: ${spring.data.redis.database}
      #sentinels: 127.0.0.1:26379 , 127.0.0.1:26380, 127.0.0.1:26381
      #masterName: mymaster