<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voc.service.insights.engine.model.mapper.InsModelDescMapper">
    <resultMap type="com.voc.service.insights.engine.model.entity.InsModelDescEntity" id="InsModelDescMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="modelId" column="model_id" jdbcType="VARCHAR"/>
        <result property="modelLabel" column="model_label" jdbcType="VARCHAR"/>
        <result property="modelPath" column="model_path" jdbcType="VARCHAR"/>
        <result property="modelDesc" column="model_desc" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="testAcc" column="test_acc" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
        <result property="versionDesc" column="version_desc" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into ins_model_desc(model_id, model_label, model_path, model_desc, status, test_acc, version,
        version_desc, update_time, create_time, update_by, create_by)
        values
        <foreach collection="model" item="entity" separator=",">
            (#{entity.modelId}, #{entity.modelLabel}, #{entity.modelPath}, #{entity.modelDesc}, #{entity.status},
            #{entity.testAcc}, #{entity.version}, #{entity.versionDesc}, #{entity.updateTime}, #{entity.createTime},
            #{entity.updateBy}, #{entity.createBy})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into ins_model_desc(model_id, model_label, model_path, model_desc, status, test_acc, version,
        version_desc, update_time, create_time, update_by, create_by)
        values
        <foreach collection="model" item="entity" separator=",">
            (#{entity.modelId}, #{entity.modelLabel}, #{entity.modelPath}, #{entity.modelDesc}, #{entity.status},
            #{entity.testAcc}, #{entity.version}, #{entity.versionDesc}, #{entity.updateTime}, #{entity.createTime},
            #{entity.updateBy}, #{entity.createBy})
        </foreach>
        on duplicate key update
        model_id = values(model_id) ,
        model_label = values(model_label) ,
        model_path = values(model_path) ,
        model_desc = values(model_desc) ,
        status = values(status) ,
        test_acc = values(test_acc) ,
        version = values(version) ,
        version_desc = values(version_desc) ,
        update_time = values(update_time) ,
        create_time = values(create_time) ,
        update_by = values(update_by) ,
        create_by = values(create_by)
    </insert>
</mapper>

