<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voc.service.insights.engine.model.mapper.InsModelInfoMapper">
    <resultMap type="com.voc.service.insights.engine.model.entity.InsModelInfoEntity" id="InsModelInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="modelName" column="model_name" jdbcType="VARCHAR"/>
        <result property="modelType" column="model_type" jdbcType="VARCHAR"/>
        <result property="format" column="format" jdbcType="VARCHAR"/>
        <result property="clientId" column="client_id" jdbcType="VARCHAR"/>
        <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into ins_model_info(model_name, model_type, format, client_id, project_id, project_name,
        update_time, create_time, update_by, create_by)
        values
        <foreach collection="model" item="entity" separator=",">
            (#{entity.modelName}, #{entity.modelType}, #{entity.format}, #{entity.clientId},
            #{entity.projectId}, #{entity.projectName}, #{entity.updateTime}, #{entity.createTime}, #{entity.updateBy},
            #{entity.createBy})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into ins_model_info(model_name, model_type, format, client_id, project_id, project_name,
        update_time, create_time, update_by, create_by)
        values
        <foreach collection="model" item="entity" separator=",">
            (#{entity.modelName}, #{entity.modelType}, #{entity.format}, #{entity.clientId},
            #{entity.projectId}, #{entity.projectName}, #{entity.updateTime}, #{entity.createTime}, #{entity.updateBy},
            #{entity.createBy})
        </foreach>
        on duplicate key update
        model_name = values(model_name) ,
        model_type = values(model_type) ,
        format = values(format) ,
        client_id = values(client_id) ,
        project_id = values(project_id) ,
        project_name = values(project_name) ,
        update_time = values(update_time) ,
        create_time = values(create_time) ,
        update_by = values(update_by) ,
        create_by = values(create_by)
    </insert>
</mapper>

