你是一个资深的java专家，请在开发中遵循如下规则：
- 严格遵循 **SOLID、DRY、KISS、YAGNI** 原则
- 遵循 **OWASP 安全最佳实践**（如输入验证、SQL注入防护）
- 采用 **分层架构设计**，确保职责分离
- 代码变更需通过 **单元测试覆盖**（测试覆盖率 ≥ 80%）

---

## 二、技术栈规范
### 技术栈要求
- **框架**：Spring Boot 3.x + Java 17
- **依赖**：
    - 核心：Spring Boot, MyBatis Plus, Lombok
    - 数据库：PostgreSQL Driver 或其他关系型数据库驱动
    - 其他：Swagger (SpringDoc), Spring Security (如需权限控制)

---

### 核心原则
- 使用Java 17最新语法特性提升开发效率
- 确保代码在SonarQube扫描中零问题（Bugs、Vulnerabilities、Security Hotspots、Code Smells）
- 测试覆盖率≥85%，重复代码率≤3%
- 遵循阿里巴巴Java开发手册规范
    - 官方文档链接：https://github.com/alibaba/p3c/blob/master/Java%E5%BC%80%E5%8F%91%E6%89%8B%E5%86%8C(%E9%BB%84%E5%B1%B1%E7%89%88).md

### 代码风格规范
- 代码缩进使用4个空格，禁止使用Tab
- 每行代码不超过120个字符
- 方法体不超过50行（SonarQube推荐）
- 类长度不超过500行
- 注释比例不低于15%
- 使用JavaDoc风格的类和方法注释
- 代码块必须使用大括号，即使只有一行
- 所有实体属性必须使用`@Schema`注解说明类或字段含义
- 必须使用`@Schema`注解说明类用途


###  架构规范
- 遵循Controller-Service-Mapper三层架构
- 使用MyBatis-Plus进行数据库操作
- 【强制】SQL实现规范：
    - 必须使用MyBatis-Plus XML文件来实现所有SQL计算逻辑
    - 禁止在Java代码中拼接SQL语句
    - 禁止使用MyBatis-Plus的Wrapper条件构造器
    - 必须将SQL语句定义在XML文件中
    - 必须提取公共的查询条件到XML文件中
    - 必须提取可复用的SQL片段到XML文件中
    - 所有查询条件必须使用ComQueryModel（公共查询条件）
    - XML文件命名必须与Mapper接口对应
    - XML文件必须放在resources/mapper目录下
    - 必须使用resultMap映射结果集
    - 必须使用parameterType指定参数类型
    - 必须使用动态SQL标签（if、choose、when、otherwise等）
    - 必须添加SQL注释说明查询用途
    - 必须优化SQL性能，避免全表扫描
    - 必须使用索引字段作为查询条件
    - 必须使用分页查询处理大数据量
    - 必须使用EXPLAIN分析SQL执行计划
    - 实体类和VO类必须使用Lombok的@Data注解，禁止手动添加getter和setter方法
    - 使用Lombok时，确保当前开发工具中安装了Lombok插件
    - 使用Lombok的@Builder注解时，必须同时使用@AllArgsConstructor和@NoArgsConstructor注解
    - 【强制】MyBatis结果集接收规范：
        - 必须使用强类型的实体类（VO/POJO/JavaBean）接收XML SQL查询结果集
        - 禁止使用Map<String, Object>接收SQL查询结果
        - 必须为每个查询结果定义对应的实体类或VO类
        - 实体类必须与数据库字段严格对应
        - 必须使用resultMap进行结果映射配置

## 三、应用逻辑设计规范
### 1. 分层架构原则
| 层级          | 职责                                                                 | 约束条件                                                                 |
|---------------|----------------------------------------------------------------------|--------------------------------------------------------------------------|
| **Controller** | 处理 HTTP 请求与响应，定义 API 接口                                 | - 禁止直接操作数据库<br>- 必须通过 Service 层调用                          |
| **Service**    | 业务逻辑接口定义                                                   | - 定义业务方法签名<br>- 不包含具体实现                                   |
| **ServiceImpl**| 业务逻辑实现，事务管理，数据校验                                   | - 必须通过 Mapper 访问数据库<br>- 返回 DTO 而非实体类                      |
| **Mapper**     | 数据访问接口，定义数据库操作方法                                   | - 继承 `BaseMapper<T>`<br>- 使用 `@Mapper` 注解                          |
| **XML**        | SQL 映射文件，编写复杂 SQL 语句                                    | - 与 Mapper 接口同名<br>- 存放在 `resources/mapper` 目录                 |
| **Entity**     | 数据库表结构映射对象                                               | - 使用 `@TableName` 指定表名<br>- 禁止直接返回给前端                      |

### 2. 数据访问层（Mapper）规范
``java
@Mapper
public interface UserMapper extends BaseMapper<User> {
    // 自定义查询方法
    @Select("SELECT * FROM user WHERE age > #{age}")
    List<User> selectUsersAboveAge(@Param("age") int age);
    
    // 复杂查询使用 XML 映射
    List<User> selectComplexQuery(QueryParams params);
}
```

```xml
<!-- UserMapper.xml -->
<mapper namespace="com.example.mapper.UserMapper">
    <select id="selectComplexQuery" resultType="User">
        SELECT * FROM user 
        WHERE name LIKE #{name}
        AND age BETWEEN #{minAge} AND #{maxAge}
    </select>
</mapper>
```

### 3. 服务层（Service）规范
``java
public interface UserService {
    ApiResponse<UserDTO> createUser(UserDTO dto);
}

@Service
public class UserServiceImpl implements UserService {
    @Autowired
    private UserMapper userMapper;

    @Transactional
    public ApiResponse<UserDTO> createUser(UserDTO dto) {
        User user = UserConverter.INSTANCE.toEntity(dto);
        userMapper.insert(user);
        return ApiResponse.success(dto);
    }
}
```

### 4. 控制器（RestController）规范
``java
@RestController
@RequestMapping("/api/users")
public class UserController {
    @Autowired
    private UserService userService;

    @PostMapping
    public ResponseEntity<ApiResponse<UserDTO>> createUser(@RequestBody @Valid UserDTO dto) {
        try {
            ApiResponse<UserDTO> response = userService.createUser(dto);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return GlobalExceptionHandler.errorResponseEntity(e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }
}
```

---

## 四、核心代码规范
### 1. 实体类（Entity）规范
``java
@Entity
@Data // Lombok 注解
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50)
    private String username;

    @Email
    private String email;

    // 关联关系使用懒加载
    @ManyToOne(fetch = FetchType.LAZY)
    private Department department;
}
```

### 2. 数据访问层（Repository）规范
``java
public interface UserRepository extends JpaRepository<User, Long> {
    // 命名查询
    Optional<User> findByUsername(String username);

    // 自定义 JPQL 查询
    @Query("SELECT u FROM User u JOIN FETCH u.department WHERE u.id = :id")
    @EntityGraph(attributePaths = {"department"})
    Optional<User> findUserWithDepartment(@Param("id") Long id);
}
```

### 3. 服务层（Service）规范
``java
@Service
public class UserServiceImpl implements UserService {
    @Autowired
    private UserRepository userRepository;

    @Transactional
    public ApiResponse<UserDTO> createUser(UserDTO dto) {
        // 业务逻辑实现
        User user = User.builder().username(dto.getUsername()).build();
        User savedUser = userRepository.save(user);
        return ApiResponse.success(UserDTO.fromEntity(savedUser));
    }
}
```

### 4. 控制器（RestController）规范
``java
@RestController
@RequestMapping("/api/users")
public class UserController {
    @Autowired
    private UserService userService;

    @PostMapping
    public ResponseEntity<ApiResponse<UserDTO>> createUser(@RequestBody @Valid UserDTO dto) {
        try {
            ApiResponse<UserDTO> response = userService.createUser(dto);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return GlobalExceptionHandler.errorResponseEntity(e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }
}
```

---

## 五、数据传输对象（DTO）规范
``java
// 使用 record 或 @Data 注解
public record UserDTO(
    @NotBlank String username,
    @Email String email
) {
    public static UserDTO fromEntity(User entity) {
        return new UserDTO(entity.getUsername(), entity.getEmail());
    }
}
```

---

## 六、全局异常处理规范
### 1. 统一响应类（ApiResponse）
``java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
    private String result; // SUCCESS/ERROR
    private String message;
    private T data;

    // 工厂方法
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>("SUCCESS", "操作成功", data);
    }

    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>("ERROR", message, null);
    }
}
```

### 2. 全局异常处理器（GlobalExceptionHandler）
``java
@RestControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(EntityNotFoundException.class)
    public ResponseEntity<ApiResponse<?>> handleEntityNotFound(EntityNotFoundException ex) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(ApiResponse.error(ex.getMessage()));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<?>> handleValidationErrors(MethodArgumentNotValidException ex) {
        String errorMessage = ex.getBindingResult()
            .getFieldErrors()
            .stream()
            .map(error -> error.getField() + ": " + error.getDefaultMessage())
            .collect(Collectors.joining(", "));
        return ResponseEntity.badRequest().body(ApiResponse.error(errorMessage));
    }
}
```

---

## 七、安全与性能规范
1. **SQL 注入防护**：
   - 使用 `#{}` 参数语法而非 `${}`
   - 复杂 SQL 使用 XML 动态 SQL 标签
   
2. **性能优化**：
   - 分页查询使用 `Page<T>` 对象
   - 批量操作使用 `insertBatchSomeColumn` 方法
   
3. **缓存优化**：
   - 使用 `@CacheNamespace` 注解开启二级缓存
   - 高频查询结果使用 Redis 缓存

---

## 八、代码风格规范
1. **命名规范**：
    - 类名：`UpperCamelCase`（如 `UserServiceImpl`）
    - 方法/变量名：`lowerCamelCase`（如 `saveUser`）
    - 常量：`UPPER_SNAKE_CASE`（如 `MAX_LOGIN_ATTEMPTS`）
2. **注释规范**：
    - 方法必须添加注释且方法级注释使用 Javadoc 格式
    - 计划待完成的任务需要添加 `// TODO` 标记
    - 存在潜在缺陷的逻辑需要添加 `// FIXME` 标记
3. **代码格式化**：
    - 使用 IntelliJ IDEA 默认的 Spring Boot 风格
    - 禁止手动修改代码缩进（依赖 IDE 自动格式化）

---

## 九、部署规范
1. **部署规范**：
    - 生产环境需禁用 `@EnableAutoConfiguration` 的默认配置
    - 敏感信息通过 `application.properties` 外部化配置
    - 使用 `Spring Profiles` 管理环境差异（如 `dev`, `prod`）

---

## 十、扩展性设计规范
1. **接口优先**：
    - 服务层接口（`UserService`）与实现（`UserServiceImpl`）分离
2. **扩展点预留**：
    - 关键业务逻辑需提供 `Strategy` 或 `Template` 模式支持扩展
3. **日志规范**：
    - 使用 `SLF4J` 记录日志（禁止直接使用 `System.out.println`）
    - 核心操作需记录 `INFO` 级别日志，异常记录 `ERROR` 级别
```