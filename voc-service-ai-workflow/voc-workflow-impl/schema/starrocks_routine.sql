-- test 环境
-- "kafka_broker_list" = "kafka-products-svc.middleware-test.svc.cluster.local:9092",
-- "kafka_topic" = "metaData_e11ab369ea4d56a7a64ab0a3c491a2cc",
-- "property.security.protocol" = "PLAINTEXT", -- 指定安全协议为 SASL_PLAINTEXT
-- "property.sasl.mechanism" = "PLAIN", -- 指定 SASL 认证机制为 PLAIN
-- "property.sasl.username" = "consumer", -- SASL 的用户名
-- "property.sasl.password" = "consumer@futong2024", -- SASL 的密码
-- "property.group.id" = "voc-starrocks-hst",
-- "property.kafka_default_offsets" = "OFFSET_BEGINNING"


-- SHOW ROUTINE LOAD FOR example_tbl2_ordertest9
-- SHOW ROUTINE LOAD TASK WHERE JobName = "example_tbl2_ordertest9"
-- PAUSE  ROUTINE LOAD FOR  ays_meta_data_analysis_kafka_test1
--   RESUME  ROUTINE LOAD FOR  ays_meta_data_analysis_kafka_20240712_c_1
-- STOP ROUTINE LOAD FOR ays_meta_data_analysis_kafka_20240712_c_1

CREATE ROUTINE LOAD mod_ftm_brand_car_series_kafka_c_0 ON mod_ftm_brand_car_series
COLUMNS( new_id, voice_id, original_id, work_id,brand,brand_code, car_series,car_series_code,create_time,channel_id,content_type,model_type,ext_fields, tid)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.voiceId\",\"$.originalId\",\"$.workId\",\"$.brand\"
		,\"$.brandCode\",\"$.carSeries\",\"$.carSeriesCode\",\"$.createTime\",\"$.channelId\",\"$.contentType\",\"$.modelType\",\"$.extFields\",\"$.tid\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "************:30130,************:30131,************:30132",
    "kafka_topic" = "modFtmBrandCarSeries_0",
    "property.group.id" = "voc-starrocks-ai-workflow",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);



CREATE ROUTINE LOAD mod_ftm_car_not_tag_result_c_0 ON mod_ftm_car_not_tag_result
COLUMNS( new_id, voice_id, original_id, work_id,opinion,opinion_sentiment, subject,description,
car_body_label,view_label,process_status,create_time,channel_id,content_type,model_type,ext_fields, tid)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.voiceId\",\"$.originalId\",\"$.workId\",\"$.opinion\"
		,\"$.opinionSentiment\",\"$.subject\",\"$.description\",\"$.carBodyLabel\",\"$.viewLabel\",
\"$.processStatus\",\"$.createTime\",\"$.channelId\",\"$.contentType\",\"$.modelType\",\"$.extFields\",\"$.tid\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "************:30130,************:30131,************:30132",
    "kafka_topic" = "modFtmCarNotTagResult_0",
    "property.group.id" = "voc-starrocks-ai-workflow",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);


CREATE ROUTINE LOAD mod_ftm_car_scene_result_c_0 ON mod_ftm_car_scene_result
COLUMNS( new_id, voice_id, original_id, work_id,sim_scenario,score,scenario_schema,create_time,done,channel_id,content_type,model_type,ext_fields, tid)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.voiceId\",\"$.originalId\",\"$.workId\",\"$.simScenario\"
		,\"$.score\",\"$.scenarioSchema\",\"$.createTime\",\"$.done\",\"$.channelId\",\"$.contentType\",\"$.modelType\",\"$.extFields\",\"$.tid\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "************:30130,************:30131,************:30132",
    "kafka_topic" = "modFtmCarSceneResult_0",
    "property.group.id" = "voc-starrocks-ai-workflow",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);



CREATE ROUTINE LOAD mod_ftm_car_tag_result_c_0 ON mod_ftm_car_tag_result
COLUMNS( new_id, voice_id, original_id, work_id,original_text_scene,b_tag,q_tag,business_label_type_level_first, business_label_type_level_second,
business_label_type_level_three, business_label_type_level_four, quality_label_type_level_first, quality_label_type_level_second,
quality_label_type_level_three, quality_label_type_level_four,
sentiment, intention_type, topic, opinion, fault_level, keywords,create_time,channel_id,content_type,model_type,ext_fields, tid)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.voiceId\",\"$.originalId\",\"$.workId\",\"$.originalTextScene\"
		,\"$.bTag\",\"$.qTag\",\"$.businessLabelTypeLevelFirst\",\"$.businessLabelTypeLevelSecond\"
      ,\"$.businessLabelTypeLevelThree\",\"$.businessLabelTypeLevelFour\",\"$.qualityLabelTypeLevelFirst\",
\"$.qualityLabelTypeLevelSecond\",\"$.qualityLabelTypeLevelThree\",\"$.qualityLabelTypeLevelFour\",\"$.sentiment\",\"$.intentionType\",\"$.topic\",
\"$.opinion\",\"$.faultLevel\",\"$.keywords\",\"$.createTime\",\"$.channelId\",\"$.contentType\",\"$.modelType\",\"$.extFields\",\"$.tid\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "************:30130,************:30131,************:30132",
    "kafka_topic" = "modFtmCarTagResult_0",
    "property.group.id" = "voc-starrocks-ai-workflow",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);




CREATE ROUTINE LOAD mod_ftm_not_car_scene_result_c_0 ON mod_ftm_not_car_scene_result
COLUMNS(new_id, voice_id, original_id,tags_level4, work_id,scenario,create_time,channel_id,content_type,model_type,ext_fields,  tid)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.voiceId\",\"$.originalId\",\"$.tagsLevel4\",
 \"$.workId\",\"$.scenario\",\"$.createTime\",\"$.channelId\",\"$.contentType\",\"$.modelType\",\"$.extFields\",\"$.tid\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "************:30130,************:30131,************:30132",
    "kafka_topic" = "modFtmNotCarSceneResult_0",
    "property.group.id" = "voc-starrocks-ai-workflow",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);




CREATE ROUTINE LOAD mod_llm_return_result_info_record_c_0 ON mod_llm_return_result_info_record
COLUMNS(new_id, original_id, work_id,client_id,vehicle_brand,vehicle_model,scenario,subject,aspect,description,
sentiment,intent,confidence,create_time,done,channel_id,content_type,model_type,ext_fields, tid)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.originalId\",\"$.workId\",\"$.clientId\",\"$.vehicleBrand\",
    \"$.vehicleModel\",\"$.scenario\",\"$.subject\",\"$.aspect\",\"$.description\",\"$.sentiment\",\"$.intent\",
\"$.confidence\",\"$.createTime\",\"$.done\",\"$.channelId\",\"$.contentType\",\"$.modelType\",\"$.extFields\",\"$.tid\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "************:30130,************:30131,************:30132",
    "kafka_topic" = "modLlmReturnResultInfoRecord_0",
    "property.group.id" = "voc-starrocks-ai-workflow",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);



CREATE ROUTINE LOAD mod_merge_status_record_c_0 ON mod_merge_status_record
COLUMNS(new_id, voice_id, original_id, work_id,client_id,label_num,scenario_num,
brand_car_num,create_time,update_time,status,preset_status,done,channel_id,content_type,model_type,ext_fields, tid)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.voiceId\",\"$.originalId\",\"$.workId\",\"$.clientId\",
  \"$.labelNum\",\"$.scenarioNum\",\"$.brandCarNum\",\"$.createTime\",\"$.updateTime\",
\"$.status\",\"$.presetStatus\",\"$.done\",\"$.channelId\",\"$.contentType\",\"$.modelType\",\"$.extFields\",\"$.tid\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "************:30130,************:30131,************:30132",
    "kafka_topic" = "mergingResultDataEvent_save",
    "property.group.id" = "voc-starrocks-ai-workflow",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);


CREATE ROUTINE LOAD mod_meta_data_analysis_kafka_c_0 ON mod_meta_data_analysis
COLUMNS(new_id, content_type,work_id,client_id, content,title,model_type,ext_fields)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.id\",\"$.source\",\"$.workId\",\"$.clientId\",\"$.content\",\"$.title\",\"$.modelType\",\"$.extFields\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "************:30130,************:30131,************:30132",
    "kafka_topic" = "toModel",
	"property.group.id" = "voc-starrocks-ai-workflow",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);


CREATE ROUTINE LOAD mod_llm_return_result_exception_records_kafka_c_0 ON mod_llm_return_result_exception_record
COLUMNS( new_id, batch_id, `data`, create_time, done, tid)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.batchId\",\"$.data\",\"$.createTime\",\"$.tid\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "************:30130,************:30131,************:30132",
    "kafka_topic" = "modLlmReturnResultExceptionRecord_0",
    "property.group.id" = "voc-starrocks-ai-workflow",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);