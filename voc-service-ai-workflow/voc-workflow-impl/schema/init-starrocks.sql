CREATE TABLE `gwm`.`mod_car_and_dim_buz_tag_mapping`
(
    `car`               varchar(2000) NULL COMMENT "整车体系",
    `dim`               varchar(2000) NULL COMMENT "评价维度",
    `business_tag_name` varchar(2000) NOT NULL COMMENT "业务标签名称",
    `business_tag_code` varchar(2000) NOT NULL COMMENT "业务标签编码"
) ENGINE = OLAP DUPLICATE KEY(`car`)
COMMENT "模型-整体体系和评价维度与业务标签映射表"
DISTRIBUTED BY RANDOM
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


-- gwm.mod_car_and_dim_quality_tag_mapping definition

CREATE TABLE `gwm`.`mod_car_and_dim_quality_tag_mapping`
(
    `car`              varchar(2000) NULL COMMENT "整车体系",
    `dim`              varchar(2000) NULL COMMENT "评价维度",
    `quality_tag_name` varchar(2000) NOT NULL COMMENT "质量标签名称",
    `quality_tag_code` varchar(2000) NOT NULL COMMENT "质量标签编码",
    `severity_level`   varchar(100)  NOT NULL COMMENT "严重性等级"
) ENGINE = OLAP DUPLICATE KEY(`car`)
COMMENT "模型-整体体系和评价维度与质量标签映射表"
DISTRIBUTED BY RANDOM
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `voc`.`mod_car_and_dim_opinion_mapping`
(
    `car`     varchar(2000) NULL COMMENT "整车体系",
    `topic`   varchar(2000) NULL COMMENT "业务标签名称",
    `dim`     varchar(2000) NULL COMMENT "评价维度",
    `opinion` varchar(2000) NULL COMMENT "业务标签编码"
) ENGINE = OLAP
    COMMENT "模型-整体体系和评价维度与topic映射表"
    PROPERTIES
( "replication_num" = "1",
    "in_memory" = "false",
    "enable_persistent_index" = "false",
    "replicated_storage" = "true",
    "compression" = "LZ4");



CREATE TABLE `voc`.`mod_scenarios_tags_level`
(
    `level_1` varchar(2000) NULL COMMENT "层级_1",
    `level_2` varchar(2000) NULL COMMENT "层级_2",
    `level_3` varchar(2000) NOT NULL COMMENT "层级_3",
    `level_4` varchar(2000) NOT NULL COMMENT "层级_4"
) ENGINE = OLAP
    COMMENT "模型-车型使用场景标签层级数据表"
    PROPERTIES
( "replication_num" = "1",
    "in_memory" = "false",
    "enable_persistent_index" = "false",
    "replicated_storage" = "true",
    "compression" = "LZ4");



CREATE TABLE `mod_ftm_brand_car_series`
(
    `new_id`          varchar(40)  NOT NULL COMMENT "主键",
    `voice_id`        varchar(40)  NOT NULL COMMENT "声音ID",
    `original_id`     varchar(40)  NOT NULL COMMENT "原文ID",
    `work_id`         varchar(40)  NOT NULL COMMENT "接收处理标识",
    `channel_id`      varchar(40)  NULL COMMENT "渠道标识",
    `content_type`    varchar(10)  NULL COMMENT "内容类型：文本：text、 工单：order",
    `model_type`      int(11)      NULL COMMENT "1 智谱AI离线 2智谱AI实时 3聚类大模型",
    `brand`           varchar(50)  NULL COMMENT "品牌",
    `brand_code`      varchar(50)  NULL COMMENT "品牌编码",
    `car_series`      varchar(100) NULL COMMENT "车系",
    `car_series_code` varchar(100) NULL COMMENT "车系编码",
    `ext_fields`      json         NULL COMMENT "扩展字段",
    `create_time`     datetime     NULL COMMENT "接收时间"
) ENGINE = OLAP PRIMARY KEY(`new_id`)
COMMENT "品牌车系模型结果表"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`work_id`, `create_time`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `mod_ftm_car_not_tag_result`
(
    `new_id`            varchar(40)   NOT NULL COMMENT "主键",
    `voice_id`          varchar(40)   NOT NULL COMMENT "声音ID",
    `original_id`       varchar(40)   NOT NULL COMMENT "原文ID",
    `work_id`           varchar(40)   NOT NULL COMMENT "接收处理标识",
    `channel_id`        varchar(40)   NULL COMMENT "渠道标识",
    `content_type`      varchar(10)   NULL COMMENT "内容类型：文本：text、 工单：order",
    `model_type`        int(11)       NULL COMMENT "1 智谱AI离线 2智谱AI实时 3聚类大模型",
    `opinion`           varchar(500)  NULL COMMENT "观点",
    `opinion_sentiment` varchar(1000) NULL COMMENT "观点情感",
    `subject`           varchar(1000) NULL COMMENT "主体",
    `description`       varchar(1000) NULL COMMENT "描述",
    `car_body_label`    varchar(1000) NULL COMMENT "整车体系",
    `view_label`        varchar(1000) NULL COMMENT "评价维度",
    `process_status`    int(11)       NULL COMMENT "过程 1,2,3",
    `create_time`       datetime      NULL COMMENT "接收时间",
    `ext_fields`        json          NULL COMMENT "扩展字段"
) ENGINE = OLAP PRIMARY KEY(`new_id`)
COMMENT "汽车未打标签模型结果表"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`work_id`, `create_time`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `mod_ftm_car_scene_result`
(
    `new_id`          varchar(40)  NOT NULL COMMENT "主键",
    `voice_id`        varchar(40)  NOT NULL COMMENT "声音ID",
    `original_id`     varchar(40)  NOT NULL COMMENT "原文ID",
    `work_id`         varchar(40)  NOT NULL COMMENT "接收处理标识",
    `channel_id`      varchar(40)  NULL COMMENT "渠道标识",
    `content_type`    varchar(10)  NULL COMMENT "内容类型：文本：text、 工单：order",
    `model_type`      int(11)      NULL COMMENT "1 智谱AI离线 2智谱AI实时 3聚类大模型",
    `sim_scenario`    varchar(200) NULL COMMENT "跟声音ID对应场景 最相似的scenario",
    `score`           varchar(50)  NULL COMMENT "声音ID对应场景 和 最相似的scenario 之间的相似度",
    `scenario_schema` varchar(200) NULL COMMENT "一级标签->二级标签->三级...",
    `create_time`     datetime     NULL COMMENT "接收时间",
    `ext_fields`      json         NULL COMMENT "扩展字段",
    `done`            int(11)      NULL COMMENT "是否完成 是：1，否：0"
) ENGINE = OLAP PRIMARY KEY(`new_id`)
COMMENT "汽车场景模型结果表"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`work_id`, `create_time`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `mod_ftm_car_tag_result`
(
    `new_id`                           varchar(40)    NOT NULL COMMENT "主键",
    `voice_id`                         varchar(40)    NOT NULL COMMENT "声音ID",
    `original_id`                      varchar(40)    NOT NULL COMMENT "原文ID",
    `work_id`                          varchar(40)    NOT NULL COMMENT "接收处理标识",
    `original_text_scene`              varchar(10000) NULL COMMENT "原文片段",
    `b_tag`                            varchar(2000)  NULL COMMENT "业务标签",
    `q_tag`                            varchar(2000)  NULL COMMENT "质量标签",
    `business_label_type_level_first`  varchar(200)   NULL COMMENT "一级标签",
    `business_label_type_level_second` varchar(200)   NULL COMMENT "二级标签",
    `business_label_type_level_three`  varchar(200)   NULL COMMENT "三级标签",
    `business_label_type_level_four`   varchar(200)   NULL COMMENT "四级标签/话题",
    `quality_label_type_level_first`   varchar(200)   NULL COMMENT "一级标签",
    `quality_label_type_level_second`  varchar(200)   NULL COMMENT "二级标签",
    `quality_label_type_level_three`   varchar(200)   NULL COMMENT "三级标签",
    `quality_label_type_level_four`    varchar(200)   NULL COMMENT "四级标签/话题",
    `sentiment`                        varchar(1000)  NULL COMMENT "情感",
    `intention_type`                   varchar(1000)  NULL COMMENT "意图",
    `topic`                            varchar(300)   NULL COMMENT "聚合后的观点=>标签叶子结点",
    `opinion`                          varchar(300)   NULL COMMENT "原始观点",
    `fault_level`                      varchar(50)    NULL COMMENT "故障问题严重性等级",
    `keywords`                         varchar(1000)  NULL COMMENT "热词",
    `create_time`                      datetime       NULL COMMENT "接收时间"
) ENGINE = OLAP PRIMARY KEY(`new_id`)
COMMENT "汽车标签模型结果表"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`work_id`, `create_time`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `mod_ftm_not_car_scene_result`
(
    `new_id`      varchar(40) NOT NULL COMMENT "主键",
    `voice_id`    varchar(40) NOT NULL COMMENT "声音ID",
    `original_id` varchar(40) NOT NULL COMMENT "原文ID",
    `work_id`     varchar(40) NOT NULL COMMENT "接收处理标识",
    `scenario`    varchar(50) NULL COMMENT "用车场景",
    `create_time` datetime    NULL COMMENT "接收时间"
) ENGINE = OLAP PRIMARY KEY(`new_id`)
COMMENT "汽车场景模型结果表"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`work_id`, `create_time`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `mod_llm_api_token_record`
(
    `new_id`          varchar(40)  NOT NULL COMMENT "主键",
    `client_id`       varchar(40)  NULL COMMENT "客户标识",
    `upload_file_num` int(11)      NULL COMMENT "已经上传文件数量",
    `api_key_status`  int(11)      NULL COMMENT "api状态 0可用，1不可用",
    `api_key`         varchar(255) NULL COMMENT "apikey",
    `api_key_user`    varchar(40)  NULL COMMENT "apikey所属人",
    `create_time`     datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
    `work_id`         varchar(255) NULL COMMENT "接收处理标识"
) ENGINE = OLAP PRIMARY KEY(`new_id`)
COMMENT "智谱apikeywg记录表"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`create_time`, `new_id`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `mod_llm_batch_info_record`
(
    `new_id`         varchar(40)  NOT NULL COMMENT "主键",
    `client_id`      varchar(40)  NULL COMMENT "客户标识",
    `api_key`        varchar(440) NULL COMMENT "apikey",
    `work_id`        varchar(40)  NULL COMMENT "接收处理标识",
    `input_file_id`  varchar(500) NULL COMMENT "上传文件的 ID",
    `batch_id`       varchar(500) NULL COMMENT "任务ID",
    `output_file_id` varchar(500) NULL COMMENT "成功执行请求的输出的文件ID",
    `error_file_id`  varchar(500) NULL COMMENT "出现错误的请求的输出的文件ID",
    `batch_status`   varchar(500) NULL COMMENT "任务状态 validating:文件正在验证中，Batch 任务未开始
failed:文件未通过验证
in_progress:文件已成功验证，Batch 任务正在进行中
finalizing:Batch 任务已完成，结果正在准备中
completed:Batch 任务已完成，结果已准备好
expired:Batch 任务未能在24小时内完成
cancelling:Batch 任务正在取消中
cancelled:Batch 任务已取消",
    `create_time`    datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
    `update_time`    datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT "更新时间",
    `done`           int(11)      NULL COMMENT "是否完成计算 是：1，否：0",
    `batch_size`     int(11)      NULL COMMENT "批次大小文章量"
) ENGINE = OLAP PRIMARY KEY(`new_id`)
COMMENT "大模型批次信息记录表"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`batch_id`, `create_time`, `new_id`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `mod_llm_prompt_template`
(
    `new_id`        varchar(40)  NOT NULL COMMENT "主键",
    `client_id`     varchar(40)  NULL COMMENT "客户标识",
    `method`        varchar(500) NULL COMMENT "请求类型：POST",
    `url`           varchar(500) NULL COMMENT "请求地址",
    `model`         varchar(500) NULL COMMENT "请求模型名称",
    `messages`      json         NULL COMMENT "prompt",
    `prompt_status` int(11)      NULL COMMENT "prompt状态 0可用，1不可用",
    `create_time`   datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
    `work_id`       varchar(255) NULL COMMENT "接收处理标识",
    `source`        varchar(255) NULL COMMENT "内容类型"
) ENGINE = OLAP PRIMARY KEY(`new_id`)
COMMENT "大模型prompt提示词模板表"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`create_time`, `new_id`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `mod_llm_return_result_info_record`
(
    `new_id`        varchar(40)   NOT NULL COMMENT "主键",
    `work_id`       varchar(40)   NOT NULL COMMENT "接收处理标识",
    `original_id`   varchar(40)   NOT NULL COMMENT "原文ID",
    `client_id`     varchar(40)   NOT NULL COMMENT "客户标识",
    `vehicle_brand` varchar(50)   NULL COMMENT "品牌",
    `vehicle_model` varchar(50)   NULL COMMENT "车系",
    `scenario`      varchar(100)  NULL COMMENT "场景",
    `subject`       varchar(100)  NULL COMMENT "主体",
    `aspect`        varchar(100)  NULL COMMENT "观点对象的属性或者方面",
    `description`   varchar(1000) NULL COMMENT "描述",
    `sentiment`     varchar(100)  NULL COMMENT "情感",
    `intent`        varchar(100)  NULL COMMENT "意图",
    `confidence`    varchar(100)  NULL COMMENT "观点的情感的置信度，用来表示情感的程度，-1到1 ",
    `create_time`   datetime      NULL COMMENT "接收时间",
    `done`          int(11)       NULL COMMENT "是否完成计算 是：1，否：0"
) ENGINE = OLAP PRIMARY KEY(`new_id`)
COMMENT "AIzhipu-结果数据记录表"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`work_id`, `create_time`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `mod_merge_sentence`
(
    `new_id`                           varchar(40)   NOT NULL COMMENT "主键",
    `voice_id`                         varchar(40)   NOT NULL COMMENT "声音ID",
    `original_id`                      varchar(40)   NOT NULL COMMENT "原文ID",
    `work_id`                          varchar(40)   NOT NULL COMMENT "接收处理标识",
    `client_id`                        varchar(40)   NOT NULL COMMENT "客户标识",
    `brand_code_name`                  varchar(1000) NULL COMMENT "品牌名称",
    `car_series_name`                  varchar(1000) NULL COMMENT "车系名称",
    `b_tag`                            varchar(2000) NULL COMMENT "业务标签",
    `q_tag`                            varchar(2000) NULL COMMENT "质量标签",
    `business_label_type_level_first`  varchar(200)  NULL COMMENT "一级标签",
    `business_label_type_level_second` varchar(200)  NULL COMMENT "二级标签",
    `business_label_type_level_three`  varchar(200)  NULL COMMENT "三级标签",
    `business_label_type_level_four`   varchar(200)  NULL COMMENT "四级标签/话题",
    `quality_label_type_level_first`   varchar(200)  NULL COMMENT "一级标签",
    `quality_label_type_level_second`  varchar(200)  NULL COMMENT "二级标签",
    `quality_label_type_level_three`   varchar(200)  NULL COMMENT "三级标签",
    `quality_label_type_level_four`    varchar(200)  NULL COMMENT "四级标签/话题",
    `scenario`                         varchar(500)  NULL COMMENT "用车场景",
    `sentiment`                        varchar(1000) NULL COMMENT "情感",
    `intention_type`                   varchar(1000) NULL COMMENT "意图",
    `topic`                            varchar(300)  NULL COMMENT "聚合后的观点=>标签叶子结点",
    `opinion`                          varchar(300)  NULL COMMENT "原始观点",
    `subject`                          varchar(300)  NULL COMMENT "主体【雨刮器】",
    `fault_level`                      varchar(50)   NULL COMMENT "故障问题严重性等级",
    `description`                      varchar(200)  NULL COMMENT "描述/评价【时灵时不灵】",
    `sentiment_score`                  varchar(10)   NULL COMMENT "情感严重程度",
    `keywords`                         varchar(1000) NULL COMMENT "热词",
    `unmarkedOpinions`                 json          NULL COMMENT "观点（未打标数据记录入库）（高频观点，未映射到业务/质量标签的观点）",
    `create_time`                      datetime      NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
    `update_time`                      datetime      NULL DEFAULT CURRENT_TIMESTAMP COMMENT "更新时间",
    `merge_status`                     int(11)       NULL COMMENT "是否合并完成2, 4, 8",
    `done`                             int(11)       NULL COMMENT "是否推送完成 是：1，否：0"
) ENGINE = OLAP PRIMARY KEY(`new_id`)
COMMENT "结果数据合并记录表"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`work_id`, `create_time`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);



CREATE TABLE `mod_merge_status_record`
(
    `new_id`        varchar(40) NOT NULL COMMENT "主键",
    `work_id`       varchar(40) NOT NULL COMMENT "接收处理标识",
    `voice_id`      varchar(40) NOT NULL COMMENT "声音ID",
    `original_id`   varchar(40) NOT NULL COMMENT "原文ID",
    `client_id`     varchar(40) NOT NULL COMMENT "客户标识",
    `label_num`     int(11)     NOT NULL DEFAULT "-1" COMMENT "标签数量",
    `scenario_num`  int(11)     NOT NULL DEFAULT "-1" COMMENT "场景数量",
    `brand_car_num` int(11)     NOT NULL DEFAULT "-1" COMMENT "品牌车系数量",
    `create_time`   datetime    NULL COMMENT "接收时间",
    `update_time`   datetime    NULL COMMENT "更新时间",
    `status`        int(11)     NOT NULL COMMENT "车系：2等",
    `preset_status` int(11)     NOT NULL COMMENT "车系：2等",
    `done`          int(11)     NULL COMMENT "是否推送完成 是：1，否：0"
) ENGINE = OLAP PRIMARY KEY(`new_id`)
COMMENT "合并状态纪录表"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`work_id`, `create_time`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `mod_meta_data_analysis`
(
    `new_id`       varchar(40)    NOT NULL COMMENT "主键",
    `work_id`      varchar(40)    NULL COMMENT "接收处理标识",
    `client_id`    varchar(40)    NULL COMMENT "客户标识",
    `channel_id`   varchar(40)    NULL COMMENT "渠道标识",
    `content_type` varchar(10)    NULL COMMENT "内容类型：文本：text、 工单：order",
    `title`        varchar(500)   NULL COMMENT "标题",
    `content`      varchar(50000) NULL COMMENT "内容",
    `done`         int(11)        NULL COMMENT "是否完成计算 是：1，否：0",
    `data_status`  int(11)        NULL COMMENT "数据状态 0全部 1去噪数据 2已打标数据 3未打标数据",
    `publish_time` datetime       NULL COMMENT "发布时间",
    `create_time`  datetime       NULL DEFAULT CURRENT_TIMESTAMP COMMENT "接收时间"
) ENGINE = OLAP PRIMARY KEY(`new_id`)
COMMENT "大模型原文数据记录表"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`create_time`, `new_id`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);



-- INSERT INTO `voc`.`mod_car_and_dim_opinion_mapping`
-- SELECT * FROM FILES
--               (
--         "aws.s3.endpoint" = "http://172.16.80.16:30125",
--         "path" = "s3://test/topic_car_dim.parquet",
--         "aws.s3.enable_ssl" = "false",
--         "aws.s3.access_key" = "uYldWnIWcQ01sm2yrnje",
--         "aws.s3.secret_key" = "mPBJJita2gMNcrLINkoIti6klsvLthxRsozMc5Tq",
--         "format" = "parquet",
--         "aws.s3.use_aws_sdk_default_behavior" = "false",
--         "aws.s3.use_instance_profile" = "false",
--         "aws.s3.enable_path_style_access" = "true"
--               )


CREATE EXTERNAL TABLE `mod_opinion_and_tags_mapping` (
  `id` varchar(2000) NULL COMMENT "id",
  `subject` varchar(2000) NULL COMMENT "评价主体",
  `aspect` varchar(2000) NULL COMMENT "评价属性",
  `description` varchar(2000) NULL COMMENT "描述",
  `opinion` varchar(2000) NULL COMMENT "观点",
  `topic` varchar(2000) NULL COMMENT "归一观点",
  `business_tag` varchar(2000) NULL COMMENT "业务标签",
  `quality_tag` varchar(2000) NULL COMMENT "质量标签",
  `scenario_tag` varchar(2000) NULL COMMENT "场景标签",
  `sentiment` varchar(2000) NULL COMMENT "情感",
  `intention` varchar(2000) NULL COMMENT "意图"
) ENGINE=JDBC
PROPERTIES (
"resource" = "voc_test_jdbc",
"table" = "ins_knowledge_base_details"
);


-- CREATE EXTERNAL RESOURCE voc_test_jdbc
-- PROPERTIES (
-- "type" = "jdbc",
-- "user" = "voc",
-- "password" = "voc2024.",
-- "jdbc_uri" = "******************************************************************************",
-- "driver_url" = "https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.28/mysql-connector-java-8.0.28.jar",
-- "driver_class" = "com.mysql.cj.jdbc.Driver"
-- );