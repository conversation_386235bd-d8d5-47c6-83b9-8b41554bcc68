package com.voc.service.model.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Title: ModCarSceneResultEntity
 * @Package: com.voc.service.model.entity
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/6 10:20
 * @Version:1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModCarNotSceneResultModel implements Serializable {
    String newId;
    String workId;
    String voiceId;
    String originalId;
    String scenario;
    String tagsLevel4;
    @Builder.Default
    LocalDateTime createTime = LocalDateTime.now();
    int done;
    private String channelId;
    private String contentType;
    private Integer modelType;
    private Object extFields;

}
