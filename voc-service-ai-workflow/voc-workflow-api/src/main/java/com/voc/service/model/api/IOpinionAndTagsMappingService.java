package com.voc.service.model.api;

import com.voc.service.model.model.OpinionAndTagsMappingModel;

import java.util.List;
import java.util.Map;

/**
 * @Title: ICarAndDimChannelMappingService
 * @Package: com.voc.service.model.api
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/1 16:40
 * @Version:1.0
 */
public interface IOpinionAndTagsMappingService {
    Map<String, List<OpinionAndTagsMappingModel>> findAlMd5Maps(String clientId);

    String getMd5(String opinion);

    List<OpinionAndTagsMappingModel> getModelList(String clientId, String opinion);

    void cleanOpinionCache(String clientId);

}
