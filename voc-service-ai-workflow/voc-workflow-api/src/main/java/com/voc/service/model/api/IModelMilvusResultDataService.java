package com.voc.service.model.api;

import com.voc.service.model.model.VoiceClipRequestDataModel;
import com.voc.service.model.model.VoiceClipResultData;

import java.util.List;
import java.util.Map;

/**
 * @创建者: fanrong
 * @创建时间: 2024/8/2 下午3:40
 * @描述:
 **/
public interface IModelMilvusResultDataService {

    /**
     * @param modelList
     * @return java.util.List<com.voc.service.model.model.MilvusResultDataModel>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/8/2 下午3:48
     * @描述 获取OnnxRuntime模型embedding结果
     **/
    List<Float> findOnnxRuntimeEmbeddingData(VoiceClipRequestDataModel modelList);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/9/13 下午2:50
     * @描述  批量获取观点对应的embedding数据
     * @param opinionList
     * @return java.util.Map<java.lang.String,java.util.List<java.lang.Float>>
     **/
    Map<String, List<Float>> getOnnxRuntimeEmbeddingData(List<String> opinionList);

    /**
     * @param modelList
     * @param embeddingList
     * @return java.util.List<com.voc.service.model.model.MilvusDataModel>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/8/5 上午9:33
     * @描述 获取milvus向量库搜索结果
     **/
    List<VoiceClipResultData> findMilvusResultData(VoiceClipRequestDataModel modelList, List<Float> embeddingList);

    /**
     * @param model         模型输入数据
     * @param embeddingList 近似值集合
     * @param clientId      客户id
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/8/7 上午9:52
     * @描述 保存未命中数据到milvus
     **/
    void saveMilvusUnmatchedData(VoiceClipRequestDataModel model, List<Float> embeddingList, String clientId);
}
