package com.voc.service.model.api;

import com.voc.service.model.model.ModCarSceneResultModel;

import java.util.List;
import java.util.Set;

/**
 * @Title: IModCarSceneResultService
 * @Package: com.voc.service.model.api
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/6 10:23
 * @Version:1.0
 */
public interface IModCarSceneResultService {
    boolean add(String clientId, List<ModCarSceneResultModel> list);

    List<ModCarSceneResultModel> findModCarSceneResult(Set<String> ids);
}
