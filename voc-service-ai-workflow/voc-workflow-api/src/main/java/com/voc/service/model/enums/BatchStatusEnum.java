package com.voc.service.model.enums;

import java.util.Objects;

public enum BatchStatusEnum {

    FILE_NOT_UPLOADED("file_not_uploaded", "文件未上传"),
    BATCH_NOT_CREATED("batch_not_created", "文件已上传，未创建Batch"),

    VALIDATING("validating", "文件正在验证中，Batch 任务未开始"),
    IN_PROGRESS("in_progress", "文件已成功验证，Batch 任务正在进行中"),
    FINALIZING("finalizing", "Batch 任务已完成，结果正在准备中"),
    COMPLETED("completed", "Batch 任务已完成，结果已准备好"),
    EXPIRED("expired", "Batch 任务未能在24小时内完成"),
    CANCELLING("cancelling", "Batch 任务正在取消中"),
    CANCELLED("cancelled", "Batch 任务已取消");


    private final String code;
    private final String text;

    BatchStatusEnum(String code, String text) {
        this.code = code;
        this.text = text;
    }

    public String getCode() {
        return this.code;
    }

    public String getText() {
        return this.text;
    }

    public static BatchStatusEnum getByCode(String code) {
        for (BatchStatusEnum type : values()) {
            if (Objects.equals(type.getCode(), code)) {
                return type;
            }
        }
        return VALIDATING;
    }
}
