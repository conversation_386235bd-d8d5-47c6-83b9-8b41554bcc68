package com.voc.service.model.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/8/2 下午5:26
 * @描述:
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VoiceClipResultData implements Serializable {
    private String id;
    /**
     * 跟 声音ID对应观点 最相似的opinion
     */
    private String sim_opinion;
    /**
     * im_opinion对应的topic
     */
    private String topic;
    /**
     * 声音ID对应观点 和 最相似的opinion 之间的相似度
     */
    private Float score;
    /**
     * 整车体系: 一级标签->二级标签->三级...
     */
    private String car_schema;
    /**
     * 评价维度: 一级标签->二级标签->三级...
     */
    private String evaluate_schema;
    /**
     * 标签第4层级
     */
    private String tagsLevel4;
    private String contentId;

    String car;
    String dim;
    String businessTagName;
    String businessTagCode;
    String qualityTagName;
    String qualityTagCode;
    String md5;

    private String scenario_schema;
    /**
     * scenario 结果数据集
     */
    private List<ScenariosTagsLevelMappingModel> scenariosTagsLevelMappingList;
}
