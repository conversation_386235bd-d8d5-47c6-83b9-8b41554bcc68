package com.voc.service.model.api;


import com.voc.service.model.model.MergingResultDataModel;

import java.util.List;
import java.util.Set;

/**
 * @Title: IMergingResltDataService
 * @Package: com.voc.service.model.api
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 16:41
 * @Version:1.0
 */
public interface IMergingResltDataService {

    int modifyToDone(Set<String> ids) throws Exception;

    int modifyToDoneDB(Set<String> ids);

    int saveMergingResultData(List<MergingResultDataModel> mergingResultDataModels);

    List<MergingResultDataModel> findUnpushedData();
}
