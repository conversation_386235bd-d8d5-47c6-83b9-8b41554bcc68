package com.voc.service.model.api;

import com.voc.service.model.model.CarAndDimChannelMappingModel;

import java.util.List;
import java.util.Map;

/**
 * @Title: ICarAndDimChannelMappingService
 * @Package: com.voc.service.model.api
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/1 16:40
 * @Version:1.0
 */
public interface ICarAndDimChannelMappingService {
    Map<String, List<CarAndDimChannelMappingModel>> findAlMd5Maps(String clientId);

    String getMd5(String car, String dim);

    List<CarAndDimChannelMappingModel> getModelList(String clientId, String car, String dim);

}
