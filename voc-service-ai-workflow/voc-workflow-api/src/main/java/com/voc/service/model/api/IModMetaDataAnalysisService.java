package com.voc.service.model.api;

import com.voc.service.model.model.ModMetaDataAnalysisModel;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【mod_meta_data_analysis(大模型原文数据记录表)】的数据库操作Service
 * @createDate 2024-08-01 13:45:36
 */
public interface IModMetaDataAnalysisService {


    List<ModMetaDataAnalysisModel> findModMetaDataAnalysisList(List<String> idList);

    boolean batchUpdateStatus(List<String> customIds, int status);

    long count(String id);
}
