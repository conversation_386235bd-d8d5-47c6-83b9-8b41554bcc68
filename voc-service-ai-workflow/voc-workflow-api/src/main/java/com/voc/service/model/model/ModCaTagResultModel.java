package com.voc.service.model.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Title: MergingResultDataEntity
 * @Package: com.voc.service.model.entity
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 16:38
 * @Version:1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModCaTagResultModel implements Serializable {
    String newId;
    String voiceId;
    String originalId;
    String workId;
    String originalTextScene;
    String bTag;
    String qTag;
    String businessLabelTypeLevelFirst;
    String businessLabelTypeLevelSecond;
    String businessLabelTypeLevelThree;
    String businessLabelTypeLevelFour;
    String qualityLabelTypeLevelFirst;
    String qualityLabelTypeLevelSecond;
    String qualityLabelTypeLevelThree;
    String qualityLabelTypeLevelFour;
    String sentiment;
    String intentionType;
    String topic;
    String opinion;
    String faultLevel;
    String keywords;
    LocalDateTime createTime;
    private String channelId;
    private String contentType;
    private Integer modelType;
    private Object extFields;
}
