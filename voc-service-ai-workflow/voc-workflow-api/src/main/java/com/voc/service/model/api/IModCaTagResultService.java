package com.voc.service.model.api;

import com.voc.service.model.model.ModCaTagResultModel;

import java.util.List;
import java.util.Set;

/**
 * @Title: IModCaTagResultService
 * @Package: com.voc.service.model.api
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/6 10:14
 * @Version:1.0
 */
public interface IModCaTagResultService {
    boolean add(String clientId, List<ModCaTagResultModel> model);

    List<ModCaTagResultModel> findTagResultModel(Set<String> ids);
}
