package com.voc.service.model.model;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.voc.service.model.vo.ModLlmPromptTemplateVo;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class AiRequerstJsonlModel  implements Serializable {

    @JsonProperty("custom_id")
    private String customId;
    @JsonProperty("method")
    private String method;
    @JsonProperty("url")
    private String url;
    @JsonProperty("body")
    private BodyDTO body;

    public AiRequerstJsonlModel(AiRequestDataModel orgdata, ModLlmPromptTemplateVo template) {
        this.customId = orgdata.getId();
        this.method = template.getMethod();
        this.url = template.getUrl();
        this.body = new BodyDTO(template, orgdata);
    }

    public AiRequerstJsonlModel(AiRequestDataModel orgdata, Map<String, ModLlmPromptTemplateVo> sources) {
        this.customId = orgdata.getId();
        ModLlmPromptTemplateVo template = sources.get(orgdata.getSource());
        if (template == null) {
            template = sources.get("文本");
        }
        this.method = template.getMethod();
        this.url = template.getUrl();
        this.body = new BodyDTO(template, orgdata);
    }

    @Data
    public static class BodyDTO {
        private static final Logger log = LoggerFactory.getLogger(BodyDTO.class);
        @JsonProperty("model")
        private String model;
        @JsonProperty("messages")
        private List<MessagesDTO> messages;
        @JsonProperty("temperature")
        private Double temperature = 0.1;

        public BodyDTO(ModLlmPromptTemplateVo template, AiRequestDataModel orgdata) {
            this.model = template.getModel();
            List<MessagesDTO> messagesDTO = JSON.parseArray(template.getMessages(), MessagesDTO.class);
            try {
                MessagesDTO dto = new MessagesDTO();
                dto.setContent("\n<TEXT>" + orgdata.getContent() + "<TEXT>\n");
                dto.setRole("user");
                messagesDTO.add(dto);
            } catch (Exception e) {
                log.error("1组装模板数据报错：{}", orgdata.getContent());
                messagesDTO = JSON.parseArray(template.getMessages().replace("contentA_", ""), MessagesDTO.class);
                e.printStackTrace();
            }
            this.messages = messagesDTO;
        }


        @Data
        public static class MessagesDTO {
            @JsonProperty("role")
            private String role;
            @JsonProperty("content")
            private String content;
        }
    }
}
