package com.voc.service.model.api;


import com.voc.service.model.model.ModLlmReturnResultInfoRecordModel;

import java.util.List;
import java.util.Set;

public interface IModLlmReturnResultInfoRecordService {


    int saveModLlmResultData(String clientId, List<ModLlmReturnResultInfoRecordModel> modelList);

//    List<ModLlmReturnResultInfoRecordModel> findUntreatedData(int pushNormalizerFlowDatasetSize);


    List<ModLlmReturnResultInfoRecordModel> findReturnResultInfoRecord(Set<String> ids);

    int modifyToDoneDB(Set<String> ids);

    void modifyToDone(List<String> ids);


    Set<String> findByStatusDone(List<String> ids);

    Set<String> findUntreatedDataIds(int pushNormalizerFlowDatasetSize);

    List<ModLlmReturnResultInfoRecordModel> findByIds(Set<String> ids);

    Set<String> findVoiceId(String originalId);
}
