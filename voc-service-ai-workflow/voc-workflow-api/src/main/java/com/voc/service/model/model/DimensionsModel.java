package com.voc.service.model.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DimensionsModel implements Serializable {

    private String scenario;

    private String topic;

    private String opinion;

    private String subject;

    private String desc;

    private String levelOne;

    private String levelTwo;

    private String levelThree;

    private String levelFour;

    private String sentiment;

    private String intention;

    private String level;

    private Object extFields;

    private String sentimentScore;
}
