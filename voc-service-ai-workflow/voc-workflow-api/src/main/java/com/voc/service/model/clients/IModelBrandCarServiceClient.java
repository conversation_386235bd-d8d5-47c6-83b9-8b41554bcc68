package com.voc.service.model.clients;


import com.voc.service.model.model.AiBrandAndCarModel;
import com.voc.service.model.vo.ModBrandCarVo;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;


@FeignClient(name = "service.model.brand", url = "${service.model.v1}")
public interface IModelBrandCarServiceClient {

    @Schema(description = "声音关联对应的品牌/车系")
    @PostMapping("/vehicle_analysis")
    ModBrandCarVo vehicleAnalysis(List<AiBrandAndCarModel> aiBrandAndCarModelList);
}
