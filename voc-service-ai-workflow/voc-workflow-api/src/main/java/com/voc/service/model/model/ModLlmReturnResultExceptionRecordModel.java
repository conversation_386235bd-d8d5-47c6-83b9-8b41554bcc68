package com.voc.service.model.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Title: MergingResultDataEntity
 * @Package: com.voc.service.model.entity
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 16:38
 * @Version:1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModLlmReturnResultExceptionRecordModel implements Serializable {
    String newId;
    String batchId;
    String data;
    @Builder.Default
    LocalDateTime createTime = LocalDateTime.now();
    Integer done;
}
