package com.voc.service.model.api;

import com.voc.service.model.model.MergeStatusRecordModel;
import com.voc.service.model.model.MergingResultDataModel;

import java.util.List;
import java.util.Set;

/**
 * @Title: IMergeStatusRecordService
 * @Package: com.voc.service.model.api
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/5 17:15
 * @Version:1.0
 */
public interface IMergeStatusRecordService {
    //    void updateLabeledDataSizeDB(List<MergeStatusRecordModel> modelList);
//    void updateLabeledDataSizeDB(Set<String> ids,int status);
//    void updateLabeledDataSize(MergeStatusRecordModel build) throws Exception;
    void updateLabeledDataSize(Set<String> ids, int status) throws Exception;

    //    void updateScenariosDataSizeDB(List<MergeStatusRecordModel> modelList);
//    void updateScenariosDataSizeDB(Set<String> ids,int status);
//    void updateScenariosDataSize(MergeStatusRecordModel build) throws Exception;
    void updateScenariosDataSize(Set<String> ids, int status) throws Exception;

    //    void updateBrandDataSizeDB(List<MergeStatusRecordModel> modelList);
//    void updateBrandDataSizeDB(Set<String> ids,int status);
//    void updateBrandDataSize(MergeStatusRecordModel build) throws Exception;
    void updateBrandDataSize(Set<String> ids, int status) throws Exception;

    int saveMergeStatusRecord(List<MergeStatusRecordModel> mergeStatusRecordModelList);

    List<MergingResultDataModel> findUnpushedData(int pushAnalysisServiceMqDatasetSize);

    void sendModifyStatusEvent(List<String> ids);

    int modifyToDoneDB(Set<String> ids);

    int modifyToStatusDB(Set<String> ids);

    void preLoadDatase(final Set<String> ids);

    Set<String> updateDataStatus(int status, Set<String> ids, int conditionStatus);

    void push(String id, MergeStatusRecordModel model);

    List<MergeStatusRecordModel> getModelListCahce(Set<String> ids);

    List<MergeStatusRecordModel> getMergeStatusRecordList(Set<String> ids);

    boolean isWorkFlowSuccessed(Set<String> voiceIds, int status);
    void deleteDataStatus(Set<String> voiceIds);

}
