package com.voc.service.model.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Title: ModCarSceneResultEntity
 * @Package: com.voc.service.model.entity
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/6 10:20
 * @Version:1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModCarSceneResultModel  implements Serializable {
    String newId;
    String workId;
    String voiceId;
    String originalId;
    String orkId;
    String simScenario;
    String score;
    String scenarioSchema;
    LocalDateTime createTime;
    int done;
    private String channelId;
    private String contentType;
    private Integer modelType;
    private Object extFields;

}
