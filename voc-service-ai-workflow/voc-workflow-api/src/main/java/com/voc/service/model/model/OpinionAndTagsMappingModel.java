package com.voc.service.model.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @创建者: fanrong
 * @创建时间: 2024/9/5 下午3:09
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpinionAndTagsMappingModel implements Serializable {
    /**
     * 评价主体
     */
     String subject;
    /**
     * 评价属性
     */
    String aspect;
    /**
     * 描述
     */
    String description;
    /**
     * 观点
     */
    String opinion;
    /**
     * 归一观点
     */
    String topic;
    /**
     * 业务标签
     */
    String businessTag;
    /**
     * 质量标签
     */
    String businessTagCode;
    /**
     * 业务标签
     */
    String qualityTag;
    /**
     * 质量标签
     */
    String qualityTagCode;
    /**
     * 场景标签
     */
    String scenarioTag;
    /**
     * 情感
     */
    String sentiment;
    /**
     * 意图
     */
    String intention;
    String md5;

}
