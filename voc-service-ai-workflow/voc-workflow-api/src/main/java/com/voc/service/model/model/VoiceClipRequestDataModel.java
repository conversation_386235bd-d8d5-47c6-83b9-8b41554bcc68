package com.voc.service.model.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/8/2 下午3:45
 * @描述:
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VoiceClipRequestDataModel implements Serializable {
    /**
     * 声音ID
     */
    private String contentId;
    /**
     * 声音ID
     */
    private String new_id;
    /**
     * 用车场景
     */
    private String scenario;
    /**
     * 声音主体
     */
    private String subject;
    /**
     * 主体的子类/方法/属性
     */
    private String aspect;
    /**
     * 相关描述
     */
    private String desc;
    /**
     * 情感(正向/负向/中性中一个)
     */
    private String sentiment;
    /**
     * 意图(咨询/建议/抱怨/其他)
     */
    private String intent;
    /**
     * 大模型产生的评价情感程度的值
     */
    private String confidence;
    /**
     * 向量数据库查询的表名
     */
    private String collectionName;
    /**
     * 向量数据库查询的字段名
     */
    private String vectorFieldName;
    /**
     * 向量数据库查询后返回的字段名
     */
    private List<String> fieldNames;
    /**
     * 向量数据库查询条数 默认100条
     */
    @Builder.Default
    private Integer topK = 100;

    private String tag;


}
