package com.voc.service.model.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @创建者: fanrong
 * @创建时间: 2024/8/6 下午2:50
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CarAndDimOpinionMappingModel implements Serializable {
    /**
     * 整车体系
     */
    private String car;
    /**
     * 评价维度
     */
    private String dim;
    private String opinion;
    private String topic;
    private String md5;
}
