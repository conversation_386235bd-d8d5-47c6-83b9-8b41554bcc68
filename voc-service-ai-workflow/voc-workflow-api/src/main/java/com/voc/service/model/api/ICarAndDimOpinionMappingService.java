package com.voc.service.model.api;

import com.voc.service.model.model.CarAndDimOpinionMappingModel;

import java.util.List;
import java.util.Map;

/**
 * @创建者: fanrong
 * @创建时间: 2024/8/6 下午2:47
 * @描述:
 **/
public interface ICarAndDimOpinionMappingService {

    /**
     * @return java.util.Map<java.lang.String, java.util.List < com.voc.service.model.model.CarAndDimOpinionMappingModel>>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/8/6 下午2:54
     * @描述 获取全部 整车体系和评价维度 与opinion 的映射关系
     **/
    Map<String, List<CarAndDimOpinionMappingModel>> findAllCarAndDimOpinionMapping();

    String getMd5(String opinion);
}
