package com.voc.service.model.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 大模型原文数据记录表
 *
 * @TableName mod_meta_data_analysis
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModMetaDataAnalysisModel implements Serializable {
    /**
     * 主键
     */
    private String newId;

    /**
     * 接收处理标识
     */
    private String workId;

    /**
     * 客户标识
     */
    private String clientId;

    /**
     * 渠道标识
     */
    private String channelId;

    /**
     * 内容类型：文本：text、 工单：order
     */
    private String contentType;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;


    private Integer modelType;


    private String extFields;

    /**
     * 是否完成计算 是：1，否：0
     */
    private Integer done;

    /**
     * 数据状态 0全部 1去噪数据 2已打标数据 3未打标数据
     */
    private Integer dataStatus;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 接收时间
     */
    private Date createTime;

}