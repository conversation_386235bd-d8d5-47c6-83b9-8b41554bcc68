package com.voc.service.model.api;

import com.voc.service.model.model.ModCaNotTagResultModel;
import com.voc.service.model.model.ModCaTagResultModel;
import com.voc.service.model.model.ModCarSceneResultModel;

import java.util.List;
import java.util.Set;

/**
 * @Title: IModCaTagResultService
 * @Package: com.voc.service.model.api
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/6 10:14
 * @Version:1.0
 */
public interface IModCarNotTagResultService {

    List<ModCaNotTagResultModel> findCarNotTagResult(Set<String> ids);

    boolean add(String clientId, List<ModCaNotTagResultModel> list);
}
