package com.voc.service.model.api;

import com.voc.service.model.model.ModRequestAiModel;
import com.voc.service.model.model.ModRequestAiStatusModel;

import java.util.List;

/**
 * @Title: IModCaTagResultService
 * @Package: com.voc.service.model.api
 * @Description:
 * @Author: cuick
 * @Date: 2024/8/6 10:14
 * @Version:1.0
 */
public interface IModRequestAiStatusService {

    List<ModRequestAiStatusModel> findStatusData(List<String> idList);

    List<ModRequestAiStatusModel> findStatusList(List<String> idList);

    Boolean add(List<ModRequestAiModel> idList);
}
