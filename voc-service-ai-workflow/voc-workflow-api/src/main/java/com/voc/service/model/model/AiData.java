package com.voc.service.model.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiData implements Serializable {

    private String id;

    private String brand;

    private String carSeries;

    private String voiceId;

    private String sentence;

    @Builder.Default
    private List<FaultsModel> faults = new ArrayList<>();
    @Builder.Default
    private List<DimensionsModel> dimensions = new ArrayList<>();
    @Builder.Default
    private List<String> keywords = new ArrayList<>();
    @Builder.Default
    private List<OpinionsModel> unmarkedOpinions = new ArrayList<>();

}
