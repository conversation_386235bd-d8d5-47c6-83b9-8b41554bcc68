package com.voc.service.model.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Title: MergingResltData
 * @Package: com.voc.service.model.entity
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 16:38
 * @Version:1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MergingResultDataModel implements Serializable {

    private String newId; // 主键
    private String voiceId; // 声音ID
    private String originalId; // 原文ID
    private String workId; // 接收处理标识
    private String clientId; // 客户标识
    private String brandCodeName; // 品牌名称
    private String carSeriesName; // 车系名称
    private String bTag; // 业务标签
    private String qTag; // 质量标签
    private String businessLabelTypeLevelFirst; // 一级标签
    private String businessLabelTypeLevelSecond; // 二级标签
    private String businessLabelTypeLevelThree; // 三级标签
    private String businessLabelTypeLevelFour; // 四级标签/话题
    private String qualityLabelTypeLevelFirst; // 一级标签
    private String qualityLabelTypeLevelSecond; // 二级标签
    private String qualityLabelTypeLevelThree; // 三级标签
    private String qualityLabelTypeLevelFour; // 四级标签/话题
    private String scenario; // 用车场景
    private String sentiment; // 情感
    private String intentionType; // 意图
    private String topic; // 聚合后的观点=>标签叶子结点
    private String opinion; // 原始观点
    private String subject; // 主体【雨刮器】
    private String faultLevel; // 故障问题严重性等级
    private String description; // 描述/评价【时灵时不灵】
    private String sentimentScore; // 情感严重程度
    private String keywords; // 热词
    private String unmarkedOpinions; // JSON格式的未打标数据记录入库（这里用String暂存，实际使用时可能需要解析为JSON对象）
    private LocalDateTime createTime; // 创建时间
    private LocalDateTime updateTime; // 更新时间
    private Integer mergeStatus; // 是否合并完成，2, 4, 8
    private Integer done; // 是否推送完成，是：1，否：0
}
