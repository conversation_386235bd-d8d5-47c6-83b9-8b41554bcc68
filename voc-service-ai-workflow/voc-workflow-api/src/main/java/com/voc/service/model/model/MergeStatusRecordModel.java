package com.voc.service.model.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Title: MergingResultDataEntity
 * @Package: com.voc.service.model.entity
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 16:38
 * @Version:1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MergeStatusRecordModel implements Serializable {
    String newId;
    String workId;
    String voiceId;
    String originalId;
    String clientId;
    int labelNum;
    int scenarioNum;
    int brandCarNum;
    @Builder.Default
    LocalDateTime createTime = LocalDateTime.now();
    @Builder.Default
    LocalDateTime updateTime = LocalDateTime.now();
    int presetStatus;
    int status;
    int done;
    @Builder.Default
    int retryCount = -1;
    private String channelId;
    private String contentType;
    private Integer modelType;
    private Object extFields;
}
